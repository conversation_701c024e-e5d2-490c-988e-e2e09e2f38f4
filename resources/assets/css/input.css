.input-component{
    display: grid;
    grid-template-columns: 1fr 3fr;
    align-items: center;
}

.dark-theme .centered{
    color: #fff;
}

.input-component .centered {
    display: flex;
    align-items: center;
}

.input-component .input-error-group .border-red{
    border-color: red;
}

.input-component .input-error-group .form-control,
.input-component .input-error-group .form-select,
.input-component .input-error-group .input-group-text{
    border-color: red;
}

@media only screen and (max-width: 500px){
    .input-component{
        display: block;
    }

    label{
        margin-bottom: 5px;
    }
}
