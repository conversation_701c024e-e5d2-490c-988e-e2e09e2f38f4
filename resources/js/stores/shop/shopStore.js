import { defineStore } from 'pinia'
import { reactive } from 'vue'

export const useShopStore = defineStore('shop', () => {
    const state = reactive({
        shopUpdateWasSuccessful: false,

        partsTech: false,
        partsTechWasSuccessful: false,

        deletePartsTech: false,
        partsTechDeleteWasSuccessful:false,

        joinedShops: false,
        changeStartingRONumber: false,

        readOnly: false,
        readOnlyWasSuccessful: false,

        bossBoard: false,
        dvi: false,
        forceLogOut: false,

        trialExpiration: false,
        shopPackageIsTrial: false,

        churn: false,
        churnWasSuccessful: false,

        payBalanceLink: false,

        toggleMatco: false,
        toggleShopModeWasSuccessful: false,

        shopIsMatco: false,

        setSchool: false,
        setSchoolWasSuccessful: false,

        tpmsShopCredentials: false,

        epicorLogins: false,
        epicorLoginsWasSuccessful: false,

        addCardknoxKeys: false,
        addCardknoxKeysWasSuccessful: false,

        activateCardknox: false,
        activateCardknoxWasSuccessful: false,

        addCardknoxTerminal: false,
        addCardknoxTerminalWasSuccessful: false,

        sendToBackoffice: false,
        sendToBackofficeWasSuccessful: false,

        changeShopPackage: false,
        changeShopPackageWasSuccessful: false,

        suspendReactivate: false,
        suspendReactivateWasSuccessful: false,

        deleteSupplier: false,
        deleteSupplierWasSuccessful: false,

        deleteCardKnoxSupplier: false,
        deleteCardKnoxSupplierWasSuccessful: false,

        apiKeyUpdated: false,
        deleteKeyAlert: false,

        employeePasswordModal: false,
        employeePasswordWasSuccessful: false,

        payments360Modal: false,
        payments360WasSuccessful: false,

        betaFeaturesModal: false,

        newCommentWasSuccessful: false,

        shopIsTrial: false,
        switchToPaidModal: false
    });

    function setState(key, action = 'toggle') {
        if (key in state) {
            if (action === 'activate') {
                state[key] = true;
            } else if (action === 'deactivate') {
                state[key] = false;
            } else {
                state[key] = !state[key];
            }
        } else {
            console.error(`Unknown state: ${key}`);
        }
    }

    return { state, setState }
})
