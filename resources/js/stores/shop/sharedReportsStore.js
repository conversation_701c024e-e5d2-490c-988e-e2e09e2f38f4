import { defineStore } from 'pinia'
import { reactive } from 'vue'

export const useSharedReportsStore = defineStore('sharedReports', () => {
    const state = reactive({
        editSharedReportWasSuccessful: false,
        addToShopModalVisible: false,
        addToShopWasSuccessful: false,
        previewModalVisible: false,
        sharedReports: false,
        sharedReportsForShopsModalVisible: false,
        deleteSharedReportForShopWasSuccessful: false,
        message: '',
        customReportsShopbossAPIWasSuccessful: false,
        deleteSharedReportModal:false,
    });

    function setState(key, action = 'toggle') {
        if (key in state) {
            if (action === 'activate') {
                state[key] = true;
            } else if (action === 'deactivate') {
                state[key] = false;
            } else {
                state[key] = !state[key];
            }
        } else {
            console.error(`Unknown state: ${key}`);
        }
    }

    function setMessage(message){
        state.message = message
    }

    return { state, setState, setMessage }
})
