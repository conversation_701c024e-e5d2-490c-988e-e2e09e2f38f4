<template>
    <div class="card reactiveReportComponent">
        <div class="card-body">
            <table id="shopList" class="table table-bordered data-table table-hover" width="100%">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Shop ID</th>
                        <th v-if="suspendedIsSelected">Total RO#</th>
                        <th>Contact</th>
                        <th>Phone</th>
                        <th>SMS</th>
                        <th>Email</th>
                        <th>City/State</th>
                        <th>Status</th>
                        <th>PKG</th>
                        <th>Start</th>
                        <th v-if="suspendedIsSelected">Suspended Date</th>
                        <th v-if="trialIsSelected">Last Login</th>
                        <th v-if="trialIsSelected">Login Count</th>
                        <th>View</th>
                    </tr>
                </thead>
                <tbody></tbody>
            </table>
        </div>
    </div>

    <form @submit.prevent="deleteSMS.delete(route('shop.delete.smsnumber', {'shopid': deleteSMS.shopid}), {
            preserveState: (page) => Object.keys(page.props.errors).length > 0,
            onSuccess: () => {
                deleteSMS.modalIsOpen = false
            }
        })"
    >
        <ModalComponent v-model="deleteSMS.modalIsOpen" title="Remove SMS Number">
            <template #body>
                <p>This action cannot be undone. This will permanently remove this SMS Number from {{ deleteSMS.shopid }}.</p>
            </template>
            <template #footer>
                <ButtonComponent :processing="deleteSMS.processing" name='Remove'/>
            </template>
        </ModalComponent>
    </form>

    <BaseAlertComponent v-if="deleteSMS.wasSuccessful"
        :messageType="$page.props.flash.message ? 'success' : 'error'"
        :title="'SMS Number'"
        :message="$page.props.flash.message || $page.props.flash.error"
    />

    <form @submit.prevent="addSMS.post(route('shop.save.smsnumber'), {
            preserveState: (page) => Object.keys(page.props.errors).length > 0,
            onSuccess: () => {
                deleteSMS.modalIsOpen = false
            }
        })"
    >
        <ModalComponent v-model="addSMS.modalIsOpen" title="Add SMS Number">
            <template #body>
                <div class="mb-3">
                    <p>Choose SMS Number For {{ addSMS.shopid }}.</p>

                    <div class="loaderContainer" v-if="addSMS.numbersLoading">
                        <div class="loader"></div>
                    </div>

                    <div class="row">
                        <div v-if="addSMS.errors.smsnum" class="text-danger pt-1">
                            {{ addSMS.errors.smsnum }}
                        </div>
                        <div class="col-md-6" v-for="(number, index) in availableNumbers" :key="number">
                            <div class="form-check availableNumbers">
                                <input
                                    class="form-check-input"
                                    type="radio"
                                    name="exampleRadios"
                                    :id="'radio' + index"
                                    :value="number"
                                    v-model="addSMS.selectedNumber"
                                    required
                                >
                                <label class="form-check-label" :for="'radio' + index">
                                    {{ formatPhoneNumber(number) }}
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template #footer>
                <ButtonComponent v-if="!addSMS.numbersLoading" :processing="addSMS.processing" name='Add'/>
            </template>
        </ModalComponent>
    </form>


    <form @submit.prevent="suspendReactivateShop.put(route('shop.update.suspend-reactivate', {shopid: suspendReactivateShop.shopid}), {
            preserveState: (page) => Object.keys(page.props.errors).length > 0,
            onSuccess: () => {
                deleteSMS.modalIsOpen = false
            }
        })"
    >
        <ModalComponent v-model="suspendReactivateShop.modalIsOpen" title="Suspend Shop">
            <template #body>
                <div class="mb-3">
                    <p>This will {{ shopStatusLabel }} all access to the {{ suspendReactivateShop.shopid }} - {{ suspendReactivateShop.companyName }}. Are you sure?</p>
                </div>
            </template>
            <template #footer>
                <ButtonComponent :processing="suspendReactivateShop.processing" :name='shopStatusLabel'/>
            </template>
        </ModalComponent>
    </form>
</template>

<script setup>
    import { onMounted, ref, computed } from 'vue';
    import { usePage, useForm } from "@inertiajs/vue3";
    import ButtonComponent from '@/components/utils/ButtonComponent.vue'
    import ModalComponent from '@/components/utils/ModalComponent.vue'
    import BaseAlertComponent from '@/components/alerts/BaseAlertComponent.vue'
    import { formatPhoneNumber, formatDate } from '@/utils/utils.js'

    const page = usePage();
    const userRole = page.props.auth.user.role

    const props = defineProps({
        dRoute: String
    });

    const suspendedIsSelected = computed(() => {
        return props.dRoute.indexOf('suspended') !== -1
    })

    const trialIsSelected = computed(() => {
        return props.dRoute.indexOf('trials') !== -1
    })

    const columns = ref([
        { data: 'CompanyName', name: 'CompanyName' },
        { data: 'shopid', name: 'shopid' },
        { data: 'contact', name: 'contact' },
        { data: 'CompanyPhone', name: 'CompanyPhone'},
        {
            data: 'smsnum',
            name: 'smsnum',
            orderable: false,
            searchable: false,
            render: function(data, type, row) {
                if (userRole === 'sales') {
                    return data || '';
                }
                if (data) {
                    return `${data} <span class="delete-btn" onclick="deleteSMSModal('${row.shopid}')" style="cursor: pointer; display: inline-flex; align-items: center; vertical-align: middle; color: red;"><i class="bx bx-x-circle" style="position: relative; top: -1px; font-size: 20px;"></i></span>`;
                }
                return `${data} <span class="btn btn-primary" onclick="addSMSModal('${row.shopid}', '${row.address}')">Add SMS</span>`;
            }

        },
        { data: 'CompanyEmail', name: 'CompanyEmail' },
        { data: 'address', name: 'address' },
        { data: '', name: '',
            render: function(data, type, row) {
                return `<span class="btn btn-primary" onclick="suspendModal('${row.shopid}', '${row.CompanyName}', '${row.status}')">${row.status.toUpperCase() == 'ACTIVE' ? 'Suspend' : 'Activate'}</span>`;
            },
            orderable: false, searchable: false,
        },
        { data: 'newpackagetype', name: 'newpackagetype' },
        { data: 'datestarted', name: 'datestarted',
            render: function (data, type, row) {
                    if (type === 'display') {
                        return row.datestarted ? formatDate(row.datestarted) : '';
                    }
                    return row.datestarted || '';
                }
        },
        {
            data: 'shopid',
            name: 'View',
            width: 115,
            render: (data) => `<a href="/dashboard/shop/${data}" class="btn btn-primary">Shop Detail</a>`,
            orderable: false, searchable: false,
        }
    ]);

    if (suspendedIsSelected.value) {
        columns.value.splice(
            columns.value.findIndex(col => col.data === 'datestarted') + 1, 0,
            {
                data: 'datesuspended',
                name: 'datesuspended',
                render: function(data, type, row) {
                    if (type === 'display') {
                        return row.datesuspended ? formatDate(row.datesuspended) : '';
                    }
                    return row.datesuspended || '';
                },
                orderable: false,
                searchable: false
            }
        );

        columns.value.splice(
            columns.value.findIndex(col => col.data === 'shopid') + 1, 0,
            {
                data: 'roCount',
                name: 'roCount',
                orderable: false,
                searchable: false
            }
        );
    }

    if (trialIsSelected.value) {
        columns.value.splice(
            columns.value.findIndex(col => col.data === 'datestarted') + 1, 0,
            {
                data: 'lastLogin',
                name: 'lastLogin',
                render: function(data, type, row) {
                    if (type === 'display') {
                        return row.lastLogin ? formatDate(row.lastLogin) : '';
                    }
                    return row.lastLogin || '';
                },
                orderable: false,
                searchable: false
            },
            {
                data: 'loginCount',
                name: 'loginCount',
                orderable: false,
                searchable: false
            }
        );
    }

    onMounted(() => {
        loadDataTable();
    });

    const dataIsProcessing = ref(true);

    function loadDataTable() {
        const table = $('#shopList').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: props.dRoute,
                type: 'GET',
                dataSrc: (json) => {
                    dataIsProcessing.value = false;
                    return json.data || [];
                }
            },
            columns: columns.value,
            autoWidth: false,
            pageLength: 10,
            buttons: ['copy', 'excel'],
            initComplete: function() {
                table.buttons().container().appendTo(`#shopList_wrapper .col-md-6:eq(0)`);
                dataIsProcessing.value = false;

                if ( props.dRoute.indexOf('suspended=1') !== -1 || props.dRoute.indexOf('trials=1') !== -1 ){
                    table.column(4).visible(false);
                }
            },
        });

        window.addSMSModal = addSMSModal;
        window.deleteSMSModal = deleteSMSModal;
        window.suspendModal = suspendModal;
    }

    const deleteSMS = useForm({
        modalIsOpen: false,
        shopid: null,
        companyName: null
    })

    function deleteSMSModal(id){
        deleteSMS.modalIsOpen = true
        deleteSMS.shopid = id
    }

    const addSMS = useForm({
        modalIsOpen: false,
        shopid: null,
        companyName: null,
        selectedNumber: null,
        numbersLoading: true,
    })

    function addSMSModal(id, companyAddress){
        availableNumbers.value = []

        addSMS.modalIsOpen = true
        addSMS.shopid = id
        addSMS.numbersLoading = true

        getSMSNumbers(companyAddress)
    }

    const availableNumbers = ref([]);

    function getSMSNumbers(address){
        axios.get(route('shop.getNumbers', {ac: address.replace(', ', '|')}))
            .then(response => {
                availableNumbers.value = response.data.availableNumbers
                addSMS.numbersLoading = false
            }).catch(error => {
                console.log(error)
                addSMS.numbersLoading = false
            });
    }

    const suspendReactivateShop = useForm({
        modalIsOpen: false,
        shopid: null,
        companyName: null,
        now: true,
        status: null
    })

    const shopStatusLabel = computed(() => {
        return suspendReactivateShop.status.toUpperCase() == "ACTIVE" ? "Suspend" : "Activate"
    })

    function suspendModal(shopid, companyName, status){
        suspendReactivateShop.modalIsOpen = true
        suspendReactivateShop.shopid = shopid
        suspendReactivateShop.companyName = companyName
        suspendReactivateShop.status = status
    }
</script>

<style scoped>
    .dark-theme .reactiveReportComponent,
    .dark-theme .reactiveReportComponent .dataTables_processing {
        color: #fff;
        width: 100%;
        overflow: hidden;
    }
    td {
        text-transform: capitalize;
        white-space: nowrap;
    }

    .availableNumbers{
        font-size: 17px;
    }

    #shopList th:nth-child(5){
        width: 135px;
    }

    #shopList th:nth-child(4){
        width: 90px;
    }

    #shopList th:nth-child(9){
        width: 80px;
    }

    #shopList tbody {
        vertical-align: middle;
    }
</style>
