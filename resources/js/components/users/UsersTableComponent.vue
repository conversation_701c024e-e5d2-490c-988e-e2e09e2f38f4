<template>
    <div class="card reactiveReportComponent">
        <div class="card-body">
            <table :id="tableId" class="table table-bordered datatable table-hover no-footer" width="100%">
                <thead role="row">
                    <tr class="table-header">
                        <th>ID</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Created</th>
                        <th>Updated</th>
                        <th style="width: 125px;" v-if="userRole === 'superadmin'">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="user in users" :key="user.id">
                        <td>{{ user.id }}</td>
                        <td>{{ user.name }}</td>
                        <td>{{ user.email }}</td>
                        <td>{{ capitalizeWords(user.role) }}</td>
                        <td>{{ formatDate(user.created_at) }}</td>
                        <td>{{ formatDate(user.updated_at) }}</td>
                        <td v-if="userRole === 'superadmin'">
                            <Link :href="route('user.show', {'user' : user.id})" class="btn btn-primary me-2">View</Link>
                            <button class="btn btn-danger" @click="deleteModal(user.id, user.name)">Delete</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div v-if="deleteUser.wasSuccessful">
        <BaseAlertComponent
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'User Delete'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </div>

    <form @submit.prevent="deleteUser.delete(route('user.destroy', {'user': deleteUser.userid}))">
        <ModalComponent v-model="deleteUser.modal" title="Cardknox Terminal" :visible="deleteUser.visible">
            <template #body>
                <p>Are you sure you want to delete {{deleteUser.name}}?</p>
            </template>
            <template #footer>
                <button type="submit" class="btn btn-danger">
                    <span v-if="deleteUser.processing">
                        <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                        Processing
                    </span>
                    <span v-else>Delete</span>
                </button>
            </template>
        </ModalComponent>
    </form>

</template>

<script setup>
    import { onMounted, ref, watch, computed,nextTick } from 'vue';
    import { Link, useForm } from '@inertiajs/vue3';
    import { capitalizeWords, formatDate } from '@/utils/utils.js'
    import ModalComponent from '@/components/utils/ModalComponent.vue'
    import BaseAlertComponent from '@/components/alerts/BaseAlertComponent.vue';

    const props = defineProps({
        users: Array,
        userRole: String
    });

    const users = ref(props.users)

    const tableId = ref(`data-table-${Math.random().toString(36).substr(2, 9)}`);

    const deleteUser = useForm({
        userid: null,
        name: null,
        modal: false,
    })

    const deleteModal = (id, name) => {
        deleteUser.userid = id
        deleteUser.name = name
        deleteUser.modal = true
    }

    const toggleModal = () => {
        $('#deleteUsers').modal('toggle')
    }

    watch(() => deleteUser.wasSuccessful, (newValue) => {
        if (  newValue ) {
            deleteUser.modal = false
            users.value = users.value.filter(user => user.id !== deleteUser.userid);
        }
    })

    onMounted(() => {
        const tableInstance = $(`#${tableId.value}`).DataTable({
            processing: true,
            serverSide: false,
            buttons: ['copy', 'excel', 'print'],
            initComplete: function() {
                this.api().buttons().container().appendTo(`#${tableId.value}_wrapper .col-md-6:eq(0)`);
            },
        });
    });

</script>

<style scoped>
    .dark-theme .reactiveReportComponent,
    .dark-theme .reactiveReportComponent .dataTables_processing{
        color: #fff;
    }
</style>
