<template>
    <transition name="fade-slide">
        <div v-if="showAlert" class="alert border-0 alert-dismissible fade show py-2" :class="messageTypeClass" role="alert">
            <div class="d-flex align-items-center">
                <div class="font-35 text-white"><i :class="iconClass"></i></div>
                <div class="ms-3">
                    <h6 class="mb-0 text-white">{{ title }}</h6>
                    <div class="text-white">{{ message }}</div>
                </div>
            </div>
            <button type="button" class="btn-close" @click="closeAlert" aria-label="Close"></button>
        </div>
    </transition>
</template>

<script setup>
    import { computed, ref, watch } from 'vue';

    const props = defineProps({
        messageType: String,
        title: String,
        message: String,
        messageCount: Number
    });

    const showAlert = ref(false);

    const messageTypeClass = computed(() => {
        return props.messageType === 'success' ? 'bg-success' : 'bg-danger';
    });

    const iconClass = computed(() => {
        return props.messageType === 'success' ? 'bx bxs-check-circle' : 'bx bxs-error-circle';
    });

    const closeAlert = () => {
        showAlert.value = false;
    };

    watch(() => props.messageCount, (newMessageCount) => {
        if (newMessageCount) {
            showAlert.value = true;
            setTimeout(() => {
                showAlert.value = false;
            }, 6000);
        }
    });
</script>

<style scoped>
    .alert {
        margin-left: auto;
        max-width: 500px !important;
        position: fixed;
        top: 80px;
        right: 25px;
        z-index: 100;
    }

    .fade-slide-enter-active, .fade-slide-leave-active {
        transition: transform 0.5s, opacity 0.5s;
    }

    .fade-slide-enter-from, .fade-slide-leave-to {
        transform: translateY(-100%);
        opacity: 0;
    }

    .fade-slide-enter-to, .fade-slide-leave-from {
        transform: translateY(0);
        opacity: 1;
    }
</style>
