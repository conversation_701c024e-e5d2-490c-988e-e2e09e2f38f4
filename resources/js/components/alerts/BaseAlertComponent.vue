<template>
    <transition name="fade-slide">
        <div v-if="showAlert" class="alert border-0 alert-dismissible fade show py-2" :class="messageTypeClass" role="alert"
            @mouseover="onHover(true)"
            @mouseleave="onHover(false)">

            <div class="d-flex align-items-center">
                <div class="font-35 text-white"><i :class="iconClass"></i></div>
                <div class="ms-3">
                    <h6 class="mb-1 text-white">{{ title }}</h6>
                    <div class="text-white">{{ message }}</div>
                </div>
            </div>
            <button type="button" class="btn-close" @click="closeAlert" aria-label="Close"></button>
            <div class="alert-timer" :style="{ width: timerWidth + '%' }"></div>
        </div>
    </transition>
</template>


<script setup>
    import { computed, ref, onMounted, onBeforeUnmount } from 'vue';

    const props = defineProps({
        messageType: String,
        title: String,
        message: String,
    });

    const showAlert = ref(false);
    const timerWidth = ref(100);
    const timerInterval = ref(null);
    const hoverTimer = ref(false);

    const messageTypeClass = computed(() => {
        return props.messageType === 'success' ? 'bg-success' : 'bg-danger';
    });

    const iconClass = computed(() => {
        return props.messageType === 'success' ? 'bx bxs-check-circle' : 'bx bxs-error-circle';
    });

    const closeAlert = () => {
        showAlert.value = false;
        clearInterval(timerInterval.value);
    };

    const startTimer = () => {
        let width = 100;
        timerWidth.value = width;
        timerInterval.value = setInterval(() => {
            if (!hoverTimer.value) {
                width -= 0.5;
                timerWidth.value = width;
                if (width <= 0) {
                    clearInterval(timerInterval.value);
                    closeAlert();
                }
            }
        }, 40);
    };

    onMounted(() => {
        showAlert.value = true;
        startTimer();
    });

    onBeforeUnmount(() => {
        clearInterval(timerInterval.value);
    });

    const onHover = (isHovering) => {
        hoverTimer.value = isHovering;
    };
</script>


<style scoped>
    .alert {
        margin-left: auto;
        max-width: 500px !important;
        position: fixed;
        top: 80px;
        right: 25px;
        z-index: 100;
        overflow: hidden;
        cursor: pointer;
    }

    .alert-timer {
        position: absolute;
        bottom: 0;
        left: 0;
        height: 5px;
        background-color: rgba(255, 255, 255, 0.5);
        transition: width 0.05s linear;
    }

    .alert:hover .alert-timer {
        background-color: rgba(255, 255, 255, 0.8);
    }

    .fade-slide-enter-active, .fade-slide-leave-active {
        transition: transform 0.5s, opacity 0.5s;
    }

    .fade-slide-enter-from, .fade-slide-leave-to {
        transform: translateY(-100%);
        opacity: 0;
    }

    .fade-slide-enter-to, .fade-slide-leave-from {
        transform: translateY(0);
        opacity: 1;
    }
</style>
