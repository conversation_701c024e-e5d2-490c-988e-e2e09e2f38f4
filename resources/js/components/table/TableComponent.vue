<template>
    <div class="card radius-10 w-100">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <div>
                    <h5 class="mb-1">{{ title }}</h5>
                    <p class="mb-0 font-13 text-secondary"><i class='bx bx-info-circle'></i>{{ subtitle }}</p>
                </div>
            </div>
            <div v-if="loading" class="spinner-container pt-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
            <div class="table-responsive mt-4" v-else>
                <table class="table align-middle mb-0 table-hover" id="Transaction-History">
                    <thead class="table-light">
                        <tr>
                            <th v-for="column in props.columns">
                                {{ column.label }}
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="data in props.data">
                            <td v-for="column in props.columns">
                                <template v-if="column.link">
                                    <Link :href="column.baseLink + data[column.data]">
                                        <template v-if="column.type === 'phone'">
                                            {{ formatPhoneNumber(data[column.data]) }}
                                        </template>
                                        <template v-else-if="column.type === 'date'">
                                            {{ formatDate(data[column.data]) }}
                                        </template>
                                        <template v-else>
                                            {{ capitalizeWords(data[column.data]) }}
                                        </template>
                                    </Link>
                                </template>
                                <template v-else>
                                    <template v-if="column.type === 'phone'">
                                        {{ formatPhoneNumber(data[column.data]) }}
                                    </template>
                                    <template v-else-if="column.type === 'money'">
                                        {{ asDollars(data[column.data]) }}
                                    </template>
                                    <template v-else-if="column.type === 'date'">
                                        {{ formatDate(data[column.data]) }}
                                    </template>
                                    <template v-else>
                                        {{ capitalizeWords(data[column.data]) }}
                                    </template>
                                </template>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</template>

<script setup>
    import { Link } from '@inertiajs/vue3';
    import { formatPhoneNumber, formatDate, asDollars, capitalizeWords } from '@/utils/utils.js'

    const props =  defineProps({
        title: String,
        subtitle: String,
        data: Array,
        columns: Array,
        link: Boolean,
        baseLink: String,
        loading: Boolean
    })
</script>
