<template>
    <div v-show="suppliers.length">
        <div class="card reactiveReportComponent">
            <div class="card-body">
                <div v-if="props.title">
                    <h4 class="text-center">{{ props.title }}</h4>
                    <hr>
                </div>
                <table :id="tableId" class="table table-bordered datatable table-hover no-footer" width="100%">
                    <thead role="row">
                        <tr class="table-header">
                            <th>Id</th>
                            <th>Label</th>
                            <th>Host IP</th>
                            <th style="width: 54px !important;" v-if="userRole !== 'sales'">Action</th>
                        </tr>
                    </thead>

                    <tbody>
                        <tr v-for="supplier in suppliers" :key="supplier">
                            <td>{{supplier.id}}</td>
                            <td>{{supplier.label}}</td>
                            <td>{{supplier.hostip}}</td>
                            <td v-if="userRole !== 'sales'">
                                <button class="btn btn-danger" @click="deleteModal(supplier.id, supplier.label)">Delete</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <BaseAlertComponent v-if="shopStore.state.deleteCardKnoxSupplierWasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Cardknox'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />

        <form @submit.prevent="deleteSupplier.delete(route('shop.delete.cardknox-terminal', {'id': deleteSupplier.id}), {
                preserveState: false,
                onSuccess: () => {
                    shopStore.setState('deleteCardKnoxSupplier', 'deactivate'),
                    shopStore.setState('deleteCardKnoxSupplierWasSuccessful', 'activate')
                }
            })">

            <ModalComponent v-model="shopStore.state.deleteCardKnoxSupplier" title="Delete Cardknox" :visible="deleteSupplier.visible">
                <template #body>
                    <p>Are you sure you want to delete {{ deleteSupplier.name }}?</p>
                </template>
                <template #footer>
                    <ButtonComponent :processing="deleteSupplier.processing" name="Delete" class="danger"/>
                </template>
            </ModalComponent>
        </form>
    </div>
</template>

<script setup>
    import { onMounted, ref, watch } from 'vue';
    import { Link, useForm } from '@inertiajs/vue3';
    import { formatDate } from '@/utils/utils.js'
    import ModalComponent from '@/components/utils/ModalComponent.vue'
    import BaseAlertComponent from '@/components/alerts/BaseAlertComponent.vue';
    import ButtonComponent from '@/components/utils/ButtonComponent.vue'
    import { useShopStore } from '@/stores/shop/shopStore'

    const shopStore = useShopStore()

    const props = defineProps({
        suppliers: Array,
        title: {
            Type: String
        },
        userRole: String
    });

    const suppliers = ref(props.suppliers)

    const tableId = ref(`data-table-${Math.random().toString(36).substr(2, 9)}`);

    const deleteSupplier = useForm({
        id: null,
        name: null
    })

    const deleteModal = (id, name) => {
        deleteSupplier.id = id
        deleteSupplier.name = name

        shopStore.setState('deleteCardKnoxSupplier', 'activate')
    }

    onMounted(() => {
        const tableInstance = $(`#${tableId.value}`).DataTable({
            processing: true,
            serverSide: false,
            autoWidth: false,
            buttons: ['copy', 'excel', 'print'],
            initComplete: function() {
                this.api().buttons().container().appendTo(`#${tableId.value}_wrapper .col-md-6:eq(0)`);
            },
        });
    });
</script>

<style>
    .dark-theme .reactiveReportComponent,
    .dark-theme .reactiveReportComponent .dataTables_processing{
        color: #fff;
        width: 100%;
        overflow: hidden;
    }

    .reactiveReportComponent .card-body {
        overflow: auto !important;
    }


    .reactiveReportComponent .table-header th{
        border-bottom: 1px solid #dee2e6;
    }
</style>
