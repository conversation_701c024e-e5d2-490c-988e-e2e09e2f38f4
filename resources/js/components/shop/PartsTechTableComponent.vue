<template>
    <div class="card reactiveReportComponent">
        <div class="card-body">
            <div v-if="props.title">
                <h4 class="text-center">{{ props.title }}</h4>
                <hr>
            </div>
            <table :id="tableId" class="table table-bordered datatable table-hover no-footer" width="100%">
                <thead role="row">
                    <tr class="table-header">
                        <th>Id</th>
                        <th>User Id</th>
                        <th>List Price</th>
                        <th>API Key</th>
                        <th>Action</th>
                    </tr>
                </thead>

                <tbody>
                    <tr v-for="supplier in suppliers" :key="supplier">
                        <td>{{supplier.id}}</td>
                        <td>{{supplier.userid}}</td>
                        <td>{{supplier.uselistprice}}</td>
                        <td>{{supplier.apikey}}</td>
                        <td>
                            <button class="btn btn-danger" @click="deleteModal(supplier.id, supplier.userid)">Delete</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div v-if="deleteSupplier.wasSuccessful">
        <BaseAlertComponent
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Parts Tech'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </div>

    <form @submit.prevent="deleteSupplier.delete(route('shop.delete.partstech', {'id': deleteSupplier.id}))">
        <ModalComponent name="Delete User" :id="'deleteSupplier2'" size="normal" :visible="deleteSupplier.visible">
            <template #body>
                Are you sure you want to delete {{ deleteSupplier.name }}?
            </template>
            <template #footer>
                <ButtonComponent :processing="deleteSupplier.processing" name="Delete" class="danger"/>
            </template>
        </ModalComponent>
    </form>
</template>

<script setup>
    import { onMounted, ref, watch } from 'vue';
    import { Link, useForm } from '@inertiajs/vue3';
    import { formatDate } from '@/utils/utils.js'
    import ModalComponent from '@/components/utils/ModalComponent.vue'
    import BaseAlertComponent from '@/components/alerts/BaseAlertComponent.vue';
    import ButtonComponent from '@/components/utils/ButtonComponent.vue'

    const props = defineProps({
        suppliers: Array,
        title: {
            Type: String
        }
    });

    const suppliers = ref(props.suppliers)

    const tableId = ref(`data-table-${Math.random().toString(36).substr(2, 9)}`);

    const deleteSupplier = useForm({
        id: null,
        name: null
    })

    const deleteModal = (id, name) => {
        deleteSupplier.id = id
        deleteSupplier.name = name
        toggleModal()
    }

    const toggleModal = () => {
        $('#deleteSupplier2').modal('toggle')
    }

    onMounted(() => {
        const tableInstance = $(`#${tableId.value}`).DataTable({
            processing: true,
            serverSide: false,
            buttons: ['copy', 'excel', 'print'],
            initComplete: function() {
                this.api().buttons().container().appendTo(`#${tableId.value}_wrapper .col-md-6:eq(0)`);
            },
        });
    });

    watch(() => deleteSupplier.wasSuccessful, (newValue) => {
        if (  newValue ) {
            toggleModal()
            suppliers.value = suppliers.value.filter(supplier => supplier.id !== deleteSupplier.id);
        }
    })
</script>

<style>
    .dark-theme .reactiveReportComponent,
    .dark-theme .reactiveReportComponent .dataTables_processing{
        color: #fff;
        width: 100%;
        overflow: hidden;
    }

    .reactiveReportComponent .card-body {
        overflow: auto !important;
    }

    .reactiveReportComponent .table-header th{
        border-bottom: 1px solid #dee2e6;
    }
</style>
