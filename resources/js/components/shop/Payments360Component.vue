<template>
    <div v-show="payments360.terminalID.length > 0">
        <div class="card">
            <div class="card-body">
                <h4 class="text-center">360 Payments</h4>
                <hr>

                <form @submit.prevent="payments360.put(route('shop.update.payments360', {'shopid': shopid}), {
                        preserveState: false,
                        onSuccess: () => {
                            shopStore.setState('payments360WasSuccessful', 'activate')
                        }
                    })">

                    <InputComponent
                        v-model="payments360.terminalID"
                        placeholder="Force Terminal ID"
                        label="Force Terminal ID"
                        iconClass="bx bx-terminal"
                        :error="payments360.errors.terminalID"
                        :required='true'
                        :disabled="userRole === 'sales'"
                    />

                    <InputComponent
                        v-model="payments360.token"
                        placeholder="Force Payment Token"
                        label="Force Payment Token"
                        iconClass="bx bx-key"
                        :error="payments360.errors.token"
                        :required='true'
                        :disabled="userRole === 'sales'"
                    />

                    <Select2Component
                        :data="['360','360-3PP']"
                        v-model="payments360.type"
                        label="Type"
                        iconClass="bx bx-list-ul"
                        :error="payments360.errors.type"
                        :required="true"
                        :disabled="userRole === 'sales'"
                    />

                    <InputComponent
                        v-model="payments360.cfpid"
                        placeholder="CFPID"
                        label="CFPID"
                        iconClass="bx bx-fingerprint"
                        :error="payments360.errors.cfpid"
                        :required='true'
                        :disabled="userRole === 'sales'"
                    />
                    <div class="center-actions" v-if="userRole !== 'sales'">
                        <ButtonComponent :processing="payments360.processing" name="Update" class="primary"/>
                    </div>
                </form>
            </div>
        </div>
    </div>
</template>

<script setup>
    import { usePage, useForm } from '@inertiajs/vue3'
    import InputComponent from '@/components/utils/InputComponent.vue'
    import Select2Component from '@/components/utils/Select2Component.vue'
    import ButtonComponent from '@/components/utils/ButtonComponent.vue'
    import BaseAlertComponent from '@/components/alerts/BaseAlertComponent.vue';

    import { useShopStore } from '@/stores/shop/shopStore'
    const shopStore = useShopStore()

    const { props } = usePage()
    const userRole = props.auth.user.role

    const shopid = props.shopid

    const payments360 = useForm({
        terminalID: props.companyDetails.terminalid,
        token: props.companyDetails.paytoken,
        type: props.companyDetails.creditCardProcessor,
        cfpid: props.companyDetails.cfpid,
    })

</script>

<style scoped>
    .center-actions{
        display: grid;
        align-items: center;
        justify-content: center;
        margin-top: 15px;
    }
</style>
