<template>
    <div class="card reactiveReportComponent" v-show="isDataAvailable || !hideIfDataEmpty">
        <div class="card-body">
            <div v-if="props.title">
                <h4 class="text-center">{{ props.title }}</h4>
                <hr>
            </div>
            <table :id="tableId" class="table table-bordered data-table table-hover" width="100%">
                <thead>
                    <tr>
                        <th
                            v-for="(column, index) in columns" :key="index"
                            class="border-bottom"
                            :style="column.label === 'Action' ? 'width: 122px;' : ''"
                            >
                            {{column.label}}
                        </th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</template>

<script setup>
    import { onMounted, ref, computed } from 'vue';
    import { usePage } from "@inertiajs/vue3"

    const page = usePage();

    const props = defineProps({
        route: String,
        columns: Array,
        serverSide: {
            type: Boolean,
            default: false
        },
        id: {
            type: String,
            default: () => `data-table-${Math.random().toString(36).substr(2, 9)}`
        },
        shop: String,
        title: {
            Type: String
        },
        hideIfDataEmpty: {
            Type: Boolean,
            default: false
        }
    });

    const columns = computed(() => [
        ...props.columns,
        {
            data: null,
            name: 'action',
            label: 'Action',
            render: function(data, type, row) {
                return `<button class="btn btn-primary" onclick="handleClick('${row.id}')">View Invoice</button>`;
            },
            orderable: false,
            searchable: false
        }
    ]);
    const route = ref(props.route);
    const serverSide = ref(props.serverSide);
    const tableId = ref(props.id);
    const isDataAvailable = ref(false);

    onMounted(() => {
        const table = $(`#${tableId.value}`).DataTable({
            processing: true,
            serverSide: serverSide.value,
            pageLength: 10,
            ajax: {
                url: route.value,
                dataSrc: function(json) {
                    isDataAvailable.value = json.data && json.data.length > 0;
                    return json.data;
                },
            },
            columns: columns.value,
            order: [2, 'desc'],
            buttons: ['copy', 'excel', 'print'],
            initComplete: function() {
                table.buttons().container().appendTo(`#${tableId.value}_wrapper .col-md-6:eq(0)`);
            },
        });

        window.handleClick = handleClick;
    });

    function handleClick(id) {
        const link = `${page.props.SHOPBOSS_SITE}src/private/components/control_panel/reprintinvoice.asp?v=111&id=${id}&sendemail=no`;
        const pdfUrl = `${page.props.SHOPBOSS_SITE}/src/private/components/control_panel/temp/temp-invoice.pdf`;

        const newWindow = window.open('', '_blank');

        axios.get(link)
            .then(() => {
                if (newWindow) {
                    newWindow.location = pdfUrl;
                    newWindow.focus();
                } else {
                    console.error('Failed to open a new window');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (newWindow) {
                    newWindow.close();
                }
            });
    }

</script>



<style scoped>
    .dark-theme .reactiveReportComponent,
    .dark-theme .reactiveReportComponent .dataTables_processing{
        color: #fff;
        width: 100%;
        overflow: hidden;
    }
</style>
