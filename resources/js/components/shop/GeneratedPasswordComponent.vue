<template>
    <div class="mb-3 input-component">
        <label :for="inputId" class="centered">{{ label }}</label>
        <div>
            <div :class="['input-group', error && 'text-danger input-error-group']">
                <span :class="['input-group-text', error && 'border-red text-danger']">
                    <i :class="iconClass"></i>
                </span>
                <input
                    :type="showPassword ? 'text' : 'password'"
                    v-model="password"
                    class="form-control border-end-0"
                    :id="inputId"
                    :name="inputId"
                    :placeholder="placeholder"
                    :required="required"
                />
                <button @click="suggestPassword" class="input-group-text bg-transparent" style="border-left: 2px solid #dee2e6;" type="button">Suggest</button>
                <button @click="togglePasswordVisibility" class="input-group-text bg-transparent" type="button">
                    <i :class="showPassword ? 'bx bx-show' : 'bx bx-hide'"></i>
                </button>
            </div>
            <div v-if="error" class="text-danger pt-1">
                {{ error }}
            </div>
        </div>
    </div>
</template>

<script setup>
    import { ref, watch } from 'vue';
    import '@assets/css/input.css'

    const props = defineProps({
        label: {
            type: String,
            required: true
        },
        iconClass: {
            type: String,
            required: false,
            default: 'bx bx-key'
        },
        error: {
            type: String,
            required: false
        },
        gap:{
            type: Number,
            default: 3
        },
        modelValue: String,
        required: {
            type: Boolean,
            default: false
        },
        inputId: {
            type: String,
            default: () => 'input-' + Math.random().toString(36).substr(2, 9)
        },
        placeholder: {
            type: String,
            default: 'Enter Password'
        },
        firstName: String,
        lastName: String
    });

    const emit = defineEmits(['update:modelValue']);

    const password = ref(props.modelValue || '');
    const showPassword = ref(false);

    watch(() => props.modelValue, (newValue) => {
        password.value = newValue || '';
    });

    watch(password, (newValue) => {
        emit('update:modelValue', newValue);
    }, { immediate: true });

    function togglePasswordVisibility() {
        showPassword.value = !showPassword.value;
    }

    function suggestPassword(){
        password.value = Array.from(crypto.getRandomValues(new Uint8Array(12)))
            .map(b => String.fromCharCode(b % 94 + 33))
            .join('');
    }

</script>
