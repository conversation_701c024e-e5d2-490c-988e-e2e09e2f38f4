<template>
    <head>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" />
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />
    </head>
    <div class="d-flex flex-row-reverse choose-shop">
        <div>
            <div class="input-group">
                <div class="input-group-text">{{ props.name }}</div>
                <select ref="select2" class="form-select" :id="id" data-placeholder="Choose an option">
                    <option v-for="(item, key) in props.items" :key="key" :value="key">
                        {{ key }} - {{ item }}
                    </option>
                </select>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
const emit = defineEmits(['update:active']);

const props = defineProps({
    name: String,
    items: Object,
    active: String
});

const id = 'select2-' + props.name;
const select2 = ref(null);
const selected = ref(props.active || '');

onMounted(() => {
    $(select2.value).select2({
        theme: "bootstrap-5",
        width: 'resolve',
        placeholder: $(select2.value).data('placeholder'),
    }).val(selected.value).trigger('change');

    $(select2.value).on('change', function() {
        const newVal = $(this).val();

        selected.value = newVal;
        emit('update:active', newVal);
    });
});
</script>

<style scoped>
    .form-select{
        width:250px;
    }

    .choose-shop .input-group{
        flex-wrap: nowrap !important;
    }

    @media screen and (max-width: 500px ) {
        .choose-shop{
            display: block !important;
        }
    }
</style>
