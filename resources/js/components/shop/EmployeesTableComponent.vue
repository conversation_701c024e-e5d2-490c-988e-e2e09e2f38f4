<template>
    <div v-show="isDataAvailable || !hideIfDataEmpty">
        <div class="card reactiveReportComponent">
            <div class="card-body">
                <div v-if="props.title">
                    <h4 class="text-center">{{ props.title }}</h4>
                    <hr>
                </div>

                <table :id="tableId" class="table table-bordered data-table table-hover" width="100%">
                    <thead>
                        <tr>
                            <th
                                v-for="(column, index) in columns" :key="index"
                                class="border-bottom"
                                :style="column.label === 'Action' ? 'width: 171px;' : ''"
                            >
                                {{column.label}}
                            </th>
                        </tr>
                    </thead>
                </table>
            </div>
        </div>

        <form @submit.prevent="employeePassword.put(route('shop.update.employee-password'), {
                onSuccess: () => {
                    shopStore.setState('employeePasswordModal', 'deactivate')
                    shopStore.setState('employeePasswordWasSuccessful', 'activate')
                }
            })">

            <ModalComponent v-model="shopStore.state.employeePasswordModal" title="Update Employee Password">
                <template #body>
                    <div>
                        <PasswordComponent
                            v-model="employeePassword.password"
                            label="Password"
                            :error="employeePassword.errors.password"
                            :required="true"
                        />

                        <PasswordComponent
                            v-model="employeePassword.password_confirmation"
                            label="Confirm Password"
                            :error="employeePassword.errors.password_confirmation"
                            :required="true"
                        />
                    </div>
                </template>
                <template #footer>
                    <ButtonComponent :processing="employeePassword.processing"/>
                </template>
            </ModalComponent>
        </form>
        <BaseAlertComponent v-if="shopStore.state.employeePasswordWasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Employee Password'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </div>
</template>

<script setup>
    import { onMounted, ref, computed } from 'vue';
    import { useForm } from '@inertiajs/vue3';
    import ModalComponent from '@/components/utils/ModalComponent.vue'
    import ButtonComponent from '@/components/utils/ButtonComponent.vue'
    import PasswordComponent from '@/components/utils/PasswordComponent.vue'
    import BaseAlertComponent from '@/components/alerts/BaseAlertComponent.vue';
    import { route } from 'ziggy-js';
    import { useShopStore } from '@/stores/shop/shopStore'
    const shopStore = useShopStore()

    const props = defineProps({
        router: String,
        columns: Array,
        serverSide: {
            type: Boolean,
            default: false
        },
        id: {
            type: String,
            default: () => `data-table-${Math.random().toString(36).substr(2, 9)}`
        },
        title: {
            Type: String
        },
        hideIfDataEmpty: {
            Type: Boolean,
            default: false
        },
        userRole: String
    });

    const columns = computed(() => {
        if (props.userRole !== 'sales') {
            return [
                ...props.columns,
                {
                    data: null,
                    name: 'action',
                    label: 'Action',
                    render: function(data, type, row) {
                        return `<button class="btn btn-primary" onclick="openModal('${row.id}')">Change Password</button>`;
                    },
                    orderable: false,
                    searchable: false
                }
            ];
        }
        return props.columns;
    });


    const router = ref(props.router);
    const serverSide = ref(props.serverSide);
    const tableId = ref(props.id);
    const isDataAvailable = ref(false);

    onMounted(() => {
        const table = $(`#${tableId.value}`).DataTable({
            processing: true,
            serverSide: serverSide.value,
            pageLength: 10,
            ajax: {
                url: router.value,
                dataSrc: function(json) {
                    isDataAvailable.value = json.data && json.data.length > 0;
                    return json.data;
                },
            },
            columns: columns.value,
            buttons: ['copy', 'excel', 'print'],
            initComplete: function() {
                table.buttons().container().appendTo(`#${tableId.value}_wrapper .col-md-6:eq(0)`);
            },
        });

        window.openModal = openModal;
    });

    shopStore.setState('employeePasswordWasSuccessful', 'deactivate')

    const employeePassword = useForm({
        id: '',
        password: '',
        password_confirmation: '',
    })

    function openModal(id){
        employeePassword.id = id
        shopStore.setState('employeePasswordModal', 'activate')
    }
</script>


<style>
    .dark-theme .reactiveReportComponent,
    .dark-theme .reactiveReportComponent .dataTables_processing{
        color: #fff;
        width: 100%;
        overflow: hidden;
    }
    td{
        text-transform: capitalize;
    }
</style>
