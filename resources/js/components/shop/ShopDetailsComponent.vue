<template>
    <div class="flexSpaceBetween">
        <PageLocation :items="pageLocation"/>
        <ChooseShop :name="'Shop'" :items="props.companies" :active="company.shopid" @update:active="shopUpdate"/>
    </div>
    <div class="card">
        <div class="card-body p-4">
            <h4 class="text-center">Company Info </h4>
            <hr>
            <div v-if="shopStore.state.shopUpdateWasSuccessful">
                <BaseAlertComponent
                    :messageType="'success'"
                    :title="'Shop Updated'"
                    :message="companyName + ' Sucessfully updated'"
                />
            </div>

            <form @submit.prevent="shopData.put(route('shop.update',{'shop': company.shopid}),
                {
                    preserveState: (page) => Object.keys(page.props.errors).length > 0,
                    onSuccess: () => {
                        shopStore.setState('shopUpdateWasSuccessful', 'activate')
                    }
                }
            )">
                <div class="custom-grid">
                    <Select2Component
                        v-model="shopData.company.contact"
                        :data="props.contacts"
                        label="Contact"
                        iconClass="bx bx-user"
                        :error="shopData.errors['company.contact']"
                        :disabled="userRole === 'sales'"
                    />

                    <CustomInputComponent label="LRO" inputId="lro" iconClass="bx bx-calendar">
                        <template #input>
                            <input type="text" :value="lro" class="form-control" id="lro" placeholder="LRO" disabled>
                        </template>
                    </CustomInputComponent>

                    <CustomInputComponent label="Phone" inputId="phone" iconClass="bx bx-phone">
                        <template #input>
                            <input type="text" :value="(formatPhoneNumber(company.companyPhone))" class="form-control" id="phone" placeholder="Phone" disabled autocomplete="tel">
                        </template>
                    </CustomInputComponent>

                    <CustomInputComponent label="DOA" inputId="doa" iconClass="bx bx-calendar">
                        <template #input>
                            <input type="text" :value="company.dateofacceptance" class="form-control" id="doa" placeholder="DOA" disabled>
                        </template>
                    </CustomInputComponent>

                    <CustomInputComponent label="Email" inputId="email" iconClass="bx bx-envelope">
                        <template #input>
                            <input type="text" :value="company.companyEmail" class="form-control" id="email" placeholder="Email" disabled autocomplete="email">
                        </template>
                    </CustomInputComponent>

                    <Select2Component
                        v-model="shopData.settings.failedpayment"
                        :data="['yes','no']"
                        label="Failed Payment"
                        iconClass="bx bx-x-circle"
                        :error="shopData.errors['settings.failedpayment']"
                        :disabled="userRole === 'sales'"
                    />

                    <CustomInputComponent label="Address" inputId="address" iconClass="bx bx-map">
                        <template #input>
                            <input type="text" :value="company.address" class="form-control" id="address" placeholder="Address" disabled autocomplete="street-address">
                        </template>
                    </CustomInputComponent>

                    <CustomInputComponent label="Data Transfer" inputId="dataTransfer" iconClass="bx bx-transfer" :error="dataTransferError">
                        <template #input>
                            <select class="form-select" v-model="shopData.dataTransfer.type" name="dataTransferType" id="dataTransfer" :disabled="userRole === 'sales'">
                                <option value="">Type</option>
                                <option value="Full">Full</option>
                                <option value="Partial">Partial</option>
                            </select>
                            <select class="form-select" v-model="shopData.dataTransfer.status" name="dataTransferStatus" id="dataTransferStatus" :disabled="userRole === 'sales'">
                                <option value="">Status</option>
                                <option value="Docusign Received">Docusign Received</option>
                                <option value="Docusign sent">Docusign sent</option>
                                <option value="Transfer Cancelled">Transfer Cancelled</option>
                                <option value="Transfer Complete">Transfer Complete</option>
                                <option value="Transfer Requested">Transfer Requested</option>
                            </select>
                            <select class="form-select" v-model="shopData.dataTransfer.billing_type" name="dataTransferStatus" id="dataTransferStatus" :disabled="userRole === 'sales'">
                                <option value="">Billing</option>
                                <option value="Free">Free</option>
                                <option value="Paid">Paid</option>
                            </select>
                        </template>
                    </CustomInputComponent>

                    <CustomInputComponent label="SMS Number" inputId="textNumber" iconClass="bx bx-phone" v-if="props.smsNumber">
                        <template #input>
                            <input type="text" :value="(formatPhoneNumber(props.smsNumber))" class="form-control" id="textNumber" placeholder="Text Number" disabled>
                            <span class="btn-primary custom-button" @click="deleteSMSModal()">
                                <i v-if="!deleteSMS.processing" class='bx bx-x-circle'></i>
                                <span v-else class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            </span>
                        </template>
                    </CustomInputComponent>

                    <CustomInputComponent v-else label="SMS Number" inputId="bandwithSMSNumber" iconClass="bx bx-phone" :error="shopData.errors['smsNumber']">
                        <template #input>
                            <select class="form-select" v-model="shopData.smsNumber" name="bandwithSMSNumber" id="bandwithSMSNumber" :disabled="userRole === 'sales'">
                                <option value="Choose" selected>Choose</option>
                                <option v-for="number in availableNumbers" :key="number" :value="number">
                                    {{ formatPhoneNumber(number) }}
                                </option>
                            </select>
                        </template>
                    </CustomInputComponent>

                    <Select2Component
                        v-model="shopData.settings.motor"
                        :data="['yes','no']"
                        label="Motor"
                        iconClass="bx bx-car"
                        :error="shopData.errors['settings.motor']"
                        :disabled="userRole === 'sales'"
                    />

                    <Select2Component
                        v-model="shopData.company.status"
                        :data="['ACTIVE','SUSPENDED']"
                        label="Status"
                        iconClass="bx bx-check-circle"
                        :error="shopData.errors['company.status']"
                        :disabled="userRole === 'sales'"
                    />

                    <CustomInputComponent label="Accounting Link" inputId="backofficeKeys" iconClass="bx bx-link" :error="shopData.errors['backofficeKeys']">
                        <template #input>
                            <select class="form-select" v-model="shopData.backofficeKeys.active" name="dataTransferType" id="dataTransfer" :disabled="userRole === 'sales'">
                                <option value="yes">Yes</option>
                                <option value="no">No</option>
                            </select>

                            <input type="text" class="form-control"
                                v-if="shopData.backofficeKeys.ts"
                                :value="shopData.backofficeKeys.active == 'yes'
                                    ? shopData.backofficeKeys.ts
                                    : shopData.backofficeKeys.deactivated_on"
                                disabled>

                            <button class="btn btn-primary" v-if="shouldSendToBackOffice && userRole !== 'sales'" @click.prevent="shopStore.setState('sendToBackoffice', 'activate')">
                                Send to Backoffice
                            </button>
                        </template>
                    </CustomInputComponent>


                    <CustomInputComponent label="Category" inputId="companyCategory" iconClass="bx bx-category" :error="shopData.errors['masterCompanyCategories.category'] || shopData.errors['masterCompanyCategories.level']">
                        <template #input>
                            <select class="form-select" v-model="shopData.masterCompanyCategories.category" name="companyCategory" id="companyCategory" :disabled="userRole === 'sales'">
                                <option value="">Select Category</option>
                                <option v-for="category in category.categories" :value="category">
                                    {{ capitalizeWords(category) }}
                                </option>
                            </select>
                            <select class="form-select" v-model="shopData.masterCompanyCategories.level" name="companyLevel" id="companyLevel" :disabled="userRole === 'sales'">
                                <option value="">Select Level</option>
                                <option v-for="level in category.levels" :value="level">
                                    {{ capitalizeWords(level) }}
                                </option>
                            </select>
                        </template>
                    </CustomInputComponent>

                    <CustomInputComponent label="Onboarding Hours" inputId="onboardingHours" iconClass="bx bx-time-five" :error="shopData.errors['company.onboardinghours']">
                        <template #input>
                            <input type="number" v-model="shopData.company.onboardinghours" class="form-control" id="onboardingHours" placeholder="Onboarding Hours" step=".001" :disabled="userRole === 'sales'">
                        </template>
                    </CustomInputComponent>

                    <Select2Component
                        v-model="shopData.company.newpackagetype"
                        :data="['silver', 'gold', 'platinum', 'premier', 'gold trial', 'platinum trial']"
                        label="Current Package"
                        iconClass="bx bx-package"
                        :error="shopData.errors['company.newpackagetype']"
                        :disabled="userRole === 'sales'"
                    />

                    <CustomInputComponent label="Carfax ID" inputId="carfaxID" iconClass="bx bx-id-card" :error="shopData.errors['company.carfaxlocation']">
                        <template #input>
                            <span class="input-group-text" v-if="userRole !== 'sales'">
                                <input class="form-check-input" v-model="shopData.company.carfaxlocation" type="checkbox" role="switch" id="flexSwitchCheckDefault1">
                            </span>
                            <input type="text" :value="shopData.company.carfaxlocation ? company.shopid : 'No'" class="form-control" id="carfaxID" placeholder="Carfax ID" disabled>
                        </template>
                    </CustomInputComponent>

                    <Select2Component
                        v-model="shopData.company.profitboost"
                        :data="['yes','no']"
                        label="Profitboost"
                        iconClass="bx bx-trending-up"
                        :error="shopData.errors['company.profitboost']"
                        :disabled="userRole === 'sales'"
                    />

                    <Select2Component
                        v-model="shopData.company.merchantaccount"
                        :data="['cardknox','authorize.net', '360', '360-3PP', 'no']"
                        placeholder="Credit Card Processor"
                        label="Credit Card Processor"
                        inputId="creditCardProcessor"
                        iconClass="bx bx-credit-card"
                        :error="shopData.errors['company.merchantaccount']"
                        :disabled="userRole === 'sales'"
                    />

                    <Select2Component v-if="userRole != 'customer support'"
                        v-model="shopData.settings.pph"
                        :data="['yes','no']"
                        label="PPH"
                        iconClass="bx bx-star"
                        :error="shopData.errors['settings.pph']"
                        :disabled="userRole === 'sales'"
                    />

                    <CustomInputComponent label="Last Login Date & User" inputId="lastLoginDateAndUser" iconClass="bx bx-calendar">
                        <template #input>
                            <input type="text" :value="lastLogin" class="form-control" id="lastLoginDateAndUser" placeholder="Last Login Date & User" disabled>
                        </template>
                    </CustomInputComponent>

                    <CustomInputComponent label="Time Zone" inputId="timezone" iconClass="bx bx-calendar">
                        <template #input>
                            <input type="text" :value="company.timezone" class="form-control" id="timezone" placeholder="Time Zone" disabled>
                        </template>
                    </CustomInputComponent>

                    <div class="text-area" v-if="userRole !== 'sales'">
                        <CustomInputComponent label="Comment" inputId="comment" iconClass="bx bx-envelope" :error="shopData.errors['comment'] || newComment.errors['comment']">
                            <template #input>
                                <textarea v-model="shopData.comment" class="form-control" id="comment" placeholder="Comment ..." rows="3"></textarea>
                                <span class="btn-primary custom-button" @click="!newComment.processing && saveComment()">
                                    <i v-if="!newComment.processing" class='bx bx-check'></i>
                                    <span v-else class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                </span>
                            </template>
                        </CustomInputComponent>
                    </div>

                    <CustomInputComponent label="Onboarding Wizard Animation" inputId="onboardingAnimation" iconClass="bx bx-box" :error="shopData.errors['company.onboardingAnimation']" v-if="onboardingWizardSettingsVisible && userRole !== 'sales'">
                        <template #input>
                            <select class="form-select" v-model="shopData.company.onboardingAnimation" name="onboardingAnimation" id="onboardingAnimation">
                                <option value="on">On</option>
                                <option value="off">Off</option>
                            </select>
                            <span class="btn-primary custom-button" @click="!onboardingAnimation.processing && updateOnboarding('animation')">
                                <i v-if="!onboardingAnimation.processing" class='bx bx-check'></i>
                                <span v-else class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            </span>
                        </template>
                    </CustomInputComponent>

                    <CustomInputComponent label="Onboarding Wizard Status" inputId="onboardingStatus" iconClass="bx bx-box" :error="shopData.errors['company.onboardingStatus']" v-if="onboardingWizardSettingsVisible && userRole !== 'sales'">
                        <template #input>
                            <select class="form-select" v-model="shopData.company.onboardingWizard" name="onboardingStatus" id="onboardingStatus">
                                <option value="completed">Completed</option>
                                <option value="not completed">Not Completed</option>
                            </select>
                            <span class="btn-primary custom-button" @click="!onboardingStatus.processing && updateOnboarding('status')">
                                <i v-if="!onboardingStatus.processing" class='bx bx-check'></i>
                                <span v-else class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            </span>
                        </template>
                    </CustomInputComponent>
                </div>
                <div class="d-grid justify-content-center mt-3" v-if="userRole !== 'sales'">
                    <ButtonComponent :processing="shopData.processing" class="primary"/>
                </div>
            </form>
        </div>
    </div>

    <form @submit.prevent="cardknox.put(route('shop.request.cardknox-keys', {'shopid': company.shopid}), {
            preserveState: (page) => Object.keys(page.props.errors).length > 0,
            onSuccess: () => {
                shopStore.setState('sendToBackoffice', 'deactivate'),
                shopStore.setState('sendToBackofficeWasSuccessful', 'activate')
            }
        })">
        <ModalComponent v-model="shopStore.state.sendToBackoffice" title="Activate CardKnox">
            <template #body>
                <p>This will send the integration request to Back Office. Are you sure?</p>
            </template>
            <template #footer>
                <ButtonComponent :processing="cardknox.processing" name='Yes'/>
            </template>
        </ModalComponent>

        <BaseAlertComponent v-if="shopStore.state.sendToBackofficeWasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Cardknox'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>

    <form @submit.prevent="deleteSMS.delete(route('shop.delete.smsnumber', {'shopid': deleteSMS.shopid}), {
            preserveState: (page) => Object.keys(page.props.errors).length > 0,
            onSuccess: () => {
                deleteSMS.modalIsOpen = false
            }
        })"
    >
        <ModalComponent v-model="deleteSMS.modalIsOpen" title="Remove SMS Number">
            <template #body>
                <p>This action cannot be undone. This will permanently remove this SMS Number from {{ deleteSMS.companyName }}.</p>
            </template>
            <template #footer>
                <ButtonComponent :processing="deleteSMS.processing" name='Remove'/>
            </template>
        </ModalComponent>
    </form>

    <BaseAlertComponent v-if="deleteSMS.wasSuccessful"
        :messageType="$page.props.flash.message ? 'success' : 'error'"
        :title="'SMS Number'"
        :message="$page.props.flash.message || $page.props.flash.error"
    />

    <BaseAlertComponent v-if="shopStore.state.newCommentWasSuccessful"
        :messageType="$page.props.flash.message ? 'success' : 'error'"
        :title="'Comment'"
        :message="$page.props.flash.message || $page.props.flash.error"
    />

    <BaseAlertComponent v-if="onboardingStatus.wasSuccessful"
        :messageType="$page.props.flash.message ? 'success' : 'error'"
        title="Onboarding Wizard"
        :message="$page.props.flash.message || $page.props.flash.error"
    />

    <BaseAlertComponent v-if="onboardingAnimation.wasSuccessful"
        :messageType="$page.props.flash.message ? 'success' : 'error'"
        title="Onboarding Wizard"
        :message="$page.props.flash.message || $page.props.flash.error"
    />
</template>

<script setup>
    import { computed, reactive, ref, onMounted } from 'vue'
    import { usePage, router, useForm } from '@inertiajs/vue3'

    import CustomInputComponent from '@/components/utils/CustomInputComponent.vue'
    import Select2Component from '@/components/utils/Select2Component.vue'
    import ChooseShop from '@/components/shop/ChooseShop.vue'
    import BaseAlertComponent from '@/components/alerts/BaseAlertComponent.vue'
    import ButtonComponent from '@/components/utils/ButtonComponent.vue'
    import { formatPhoneNumber, capitalizeWords } from '@/utils/utils.js'
    import PageLocation from '@/components/utils/PageLocation.vue'
    import { useShopStore } from '@/stores/shop/shopStore'
    import ModalComponent from '@/components/utils/ModalComponent.vue'


    const shopStore = useShopStore()

    const { props } = usePage()

    const userRole = props.auth.user.role

    const companyName = props.companyDetails.companyName

    const category = reactive(props.companyCategory)

    const company = reactive(props.companyDetails)

    const onboardingWizard = reactive(props.onboardingWizard)

    const onboardingWizardStatus = computed(() => {
        return (onboardingWizard && onboardingWizard.general_info && onboardingWizard.settings && onboardingWizard.employees && onboardingWizard.suppliers && onboardingWizard.customize)
            ? "completed"
            : "not completed"
    });

    const onboardingWizardAnimtaion = computed(() => {
        if ( onboardingWizard && !onboardingWizard.animation ){
            return "on"
        }else{
            return company.onboardingAnimation
        }
    });

    const onboardingWizardSettingsVisible = computed(() => {
        return new Date(company.ts) > new Date('2024-08-20')
    });

    const lro = props.lro
    const lastLogin = props.lastLoginDateAndUser

    const pageLocation = [
        {
            name: 'Shop Detail',
            link: '/dashboard/shop'
        },
        {
            name: company.companyName,
            link: ''
        }
    ]

    shopStore.setState('newCommentWasSuccessful', 'deactivate')
    shopStore.setState('shopUpdateWasSuccessful', 'deactivate')
    shopStore.setState('sendToBackofficeWasSuccessful', 'deactivate')

    const shopData = useForm({
        company: {
            contact: company.contact?.trim() || '',
            profitboost: company.profitboost,
            onboardinghours: company.onboardingHours,
            newpackagetype: company.currentPackage,
            status: company.status,
            carfaxlocation: company.carfaxID === company.shopid,
            merchantaccount: company.creditCardProcessor,
            onboardingAnimation: onboardingWizardAnimtaion.value,
            onboardingWizard: onboardingWizardStatus.value
        },
        smsNumber: 'Choose',
        settings: props.settings,
        masterCompanyCategories: {
            category: category.selectedCategory,
            level: category.selectedLevel,
        },
        dataTransfer: props.dataTransfer,
        backofficeKeys: {
            active: props.accountLink?.active ?? 'no',
            ts: props.accountLink?.ts,
            deactivated_on: props.accountLink?.deactivated_on,
            orderid: props.accountLink?.orderid,
        },
        comment: ""
    })

    function shopUpdate(shopid) {
        router.get(route('shop.show', {'shop' : shopid}));
    }

    const shouldSendToBackOffice = computed(() => {
        return !props.accountLink?.orderid && props.accountLink?.active;
    });

    const cardknox = useForm({})

    const availableNumbers = ref([]);

    function getSMSNumbers(){
        axios.get(route('shop.getNumbers', {ac: props.companyDetails.ac}))
            .then(response => {
                availableNumbers.value = response.data.availableNumbers
            })
            .catch(error => {
                console.log(error.response.data)
            })
    }

    onMounted(() => {
        getSMSNumbers()
    });

    const deleteSMS = useForm({
        modalIsOpen: false,
        shopid: null,
        companyName: null
    })

    function deleteSMSModal(){
        deleteSMS.modalIsOpen = true
        deleteSMS.shopid = company.shopid
        deleteSMS.companyName = props.companyDetails.companyName
    }

    const newComment = useForm({})

    async function saveComment(){
        newComment.post(route('shop.store.comment' , {comment: shopData.comment, shopid: company.shopid}),
            {
                preserveState: (page) => Object.keys(page.props.errors).length > 0,
                onSuccess: () => {
                    shopStore.setState('newCommentWasSuccessful', 'activate')
                }
            }
        )
    }

    const onboardingStatus = useForm({})
    const onboardingAnimation = useForm({})

    async function updateOnboarding(which){
        if (which === 'status'){
            onboardingStatus.put(route('shop.update.onboarding.status', {
                status: shopData.company.onboardingWizard,
                shopid: company.shopid
            }));
        }else{
            onboardingAnimation.put(route('shop.update.onboarding.animation', {
                status: shopData.company.onboardingAnimation,
                shopid: company.shopid
            }));
        }
    }

    const dataTransferError = computed(() => {
        const typeError = shopData.errors['dataTransfer.type'] || '';
        const statusError = shopData.errors['dataTransfer.status'] || '';

        if (typeError && statusError) {
            return `${typeError} ${statusError}`;
        } else {
            return typeError || statusError || '';
        }
    });
</script>

<style scoped>
    .custom-grid{
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        column-gap: 30px;
        align-items: start;
    }

    .custom-grid-2{
        display: grid;
        grid-template-columns: 1fr 1fr;
        column-gap: 30px;
        align-items: start;
    }

    input{
        text-transform: capitalize;
    }

    .form-check-input.form-check-input{
        margin-top: 0;
    }

    .input-group-text{
        --margin: 10px;
        padding-top: var(--margin);
        padding-bottom: var(--margin);
    }

    .alert p:last-of-type {
        margin: 0;
    }

    .custom-button{
        display: grid;
        border: var(--bs-border-width) solid var(--bs-border-color);
        border-radius: var(--bs-border-radius);
        align-items: center;
        padding: 0 5px;
        cursor: pointer;
    }

    .custom-button .bx-check,
    .custom-button .bx-x-circle{
        font-size: 20px;
    }

    @media only screen and (max-width: 2000px){
        .custom-grid{
            grid-template-columns: 1fr 1fr;
        }
    }
    @media only screen and (max-width: 1400px){
        .custom-grid{
            grid-template-columns: 1fr;
        }
    }
</style>
