<template>
    <div class="card reactiveReportComponent">
        <div class="card-body">
            <table :id="tableId" class="table table-bordered data-table table-hover" width="100%">
                <thead>
                    <tr>
                        <th v-for="(column, index) in columns" :key="index" class="border-bottom">{{column.label}}</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
    <ModalComponent v-model="shopStore.state.deleteKeyAlert" title="Delete API Key">
        <template #body>
            <p>This action cannot be undone. This will permanently delete API Key from our servers.</p>
        </template>
        <template #footer>
            <button type="button" class="btn btn-danger" @click="deleteAPI">
                <span v-if="api.processing">
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    Saving
                </span>
                <span>Delete</span>
            </button>
        </template>
    </ModalComponent>
</template>

<script setup>
    import { onMounted, ref, computed } from 'vue';
    import ModalComponent from '@/components/utils/ModalComponent.vue'
    import { useForm } from "@inertiajs/vue3"
    import { router } from '@inertiajs/vue3'
    import { useShopStore } from '@/stores/shop/shopStore'

    const shopStore = useShopStore()

    const props = defineProps({
        route: String,
        columns: Array,
        serverSide: {
            type: Boolean,
            default: false
        },
        id: {
            type: String,
            default: () => `data-table-${Math.random().toString(36).substr(2, 9)}`
        },
        shop: String
    });

    const columns = computed(() => [
        ...props.columns,
        {
            data: null,
            name: 'action',
            label: 'Actions',
            render: function(data, type, row) {
                return `<button class="btn btn-danger" onclick="handleClick('${row.id}')">Delete</button>`;
            },
            orderable: false,
            searchable: false
        }
    ]);
    const ajaxRoute = ref(props.route);
    const serverSide = ref(props.serverSide);
    const tableId = ref(props.id);

    onMounted(() => {
        const table = $(`#${tableId.value}`).DataTable({
            processing: true,
            serverSide: serverSide.value,
            pageLength: 10,
            ajax: ajaxRoute.value,
            columns: columns.value,
            buttons: [
                'copy',
                {
                    extend: 'excel',
                    title: props.title,
                },
                {
                    extend: 'print',
                    autoPrint: true,
                    orientation: 'landscape',
                    customize: function (win) {
                        $(win.document.body).css('font-size', '12pt');

                        $(win.document.body).find('table').css('width', '100%');
                        $(win.document.body).find('table td').css('max-width', '300px');
                        $(win.document.body).find('table td').css('overflow', 'hidden');
                        $(win.document.body).find('table td').css('font-size', '10px');
                        $(win.document.body).find('table th').css('max-width', '300px');
                    }
                },
            ],
            initComplete: function() {
                table.buttons().container().appendTo(`#${tableId.value}_wrapper .col-md-6:eq(0)`);
            },
        });

        window.handleClick = handleClick;
    });

    const api = useForm({})

    const api_key = ref()

    function handleClick(id) {
        api_key.value = id
        shopStore.setState('deleteKeyAlert', 'activate')
    }

    const deleteAPI = () => {
        api.delete(route('api-key.destroy', {'shop': props.shop, 'api_key': api_key.value}),{
            preserveState: false,
            onSuccess: () => {
                shopStore.setState('deleteKeyAlert', 'deactivate')
            }
        })
    };
</script>


<style>
    .dark-theme .reactiveReportComponent,
    .dark-theme .reactiveReportComponent .dataTables_processing{
        color: #fff;
    }
</style>
