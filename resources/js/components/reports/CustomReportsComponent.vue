<template>
    <div class="card reactiveReportComponent">
        <div class="card-body">
            <table :id="tableId" class="table table-bordered data-table table-hover" width="100%">
                <thead>
                    <tr>
                        <th v-for="(column, index) in columns" :key="index" class="border-bottom">{{column.label}}</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>

    <form @submit.prevent="addReportToSharedDirectory">
        <ModalComponent v-model="reportModal.addToDirectory" title="Add Report to shared directory">
                <template #body>
                    <InputComponent
                        v-model="reportShare.name"
                        placeholder="Name"
                        label="Name"
                        iconClass="bx bx-star"
                        :error="reportShare.errors.name"
                        :required='true'
                    />

                    <InputComponent
                        v-model="reportShare.description"
                        placeholder="Description"
                        label="Description"
                        iconClass="bx bx-comment"
                        :error="reportShare.errors.description"
                        :required='true'
                    />

                    <Select2Component
                        v-model="reportShare.category"
                        :data="reportCategories"
                        placeholder="Category"
                        label="Category"
                        iconClass="bx bx-category"
                        :error="reportShare.errors.category"
                    />

                    <InputComponent
                        v-model="reportShare.columns"
                        placeholder="Columns"
                        label="Columns"
                        iconClass="bx bx-columns"
                        :error="reportShare.errors.columns"
                    />
                </template>
                <template #footer>
                    <ButtonComponent name="Add " class="primary" :processing="reportModal.processing"/>
                </template>
        </ModalComponent>
    </form>

    <BaseAlertComponent v-if="sharedReportsStore.state.customReportsShopbossAPIWasSuccessful"
        messageType='success'
        title="Shared Report"
        message="Report successfully added to the shared directory"
    />
</template>

<script setup>
    import { onMounted, ref, reactive } from 'vue';
    import { useForm, usePage } from "@inertiajs/vue3"
    import { formatDate } from '@/utils/utils.js'
    import ModalComponent from '@/components/utils/ModalComponent.vue'
    import InputComponent from '@/components/utils/InputComponent.vue'
    import ButtonComponent from '@/components/utils/ButtonComponent.vue'
    import Select2Component from '@/components/utils/Select2Component.vue'
    import BaseAlertComponent from '../alerts/BaseAlertComponent.vue';
    import { useSharedReportsStore } from '@/stores/shop/sharedReportsStore'

    const sharedReportsStore = useSharedReportsStore()

    const page = usePage();

    const props = defineProps({
        id: {
            type: String,
            default: () => `data-table-${Math.random().toString(36).substr(2, 9)}`
        }
    });

    const columns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'name', name: 'name', label: 'Name'},
            {data: 'desc', name: 'desc', label: 'Description'},
            {data: 'location', name: 'location', label: 'Location'},
            {data: 'ts', name: 'ts', label: 'Added On',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.ts ? formatDate(row.ts) : '';
                    }
                    return row.ts || '';
                }
            },
            {data: '', name: '', label: 'Actions',
                render: function (data, type, row) {
                    return `<div class="d-flex gap-3 justify-content-center align-items-center">
                                <button class="btn btn-secondary preview-btn" data-id="${row.location}">Preview</button>
                                <button class="btn btn-primary addToDirectory-btn" style="width:160px" data-id="${row.id}" data-name="${row.name}" data-description="${row.desc}" data-location="${row.location}">Add To Directory</button>
                            </div>`;
                }
            }
        ]
    );

    const serverSide = ref(props.serverSide);
    const tableId = ref(props.id);

    onMounted(() => {
        const table = $(`#${tableId.value}`).DataTable({
            processing: true,
            pageLength: 10,
            ajax: route('custom-reports.datatable'),
            columns: columns.value,
            buttons: ['copy', 'excel', 'print'],
            initComplete: function() {
                table.buttons().container().appendTo(`#${tableId.value}_wrapper .col-md-6:eq(0)`);
            },
        });

        $(`#${tableId.value}`).on('click', '.preview-btn', function() {
            const shopId = $(this).data('id');
            preview(shopId);
        });

        $(`#${tableId.value}`).on('click', '.addToDirectory-btn', function() {
            const id = $(this).data('id');
            const name = $(this).data('name');
            const description = $(this).data('description');
            const location = $(this).data('location');

            addToDirectory(id, name, description, location);
        });
    });

    const reportModal = reactive({
        preview: false,
        addToDirectory: false
    });

    var iframeSrc = ref({});

    function preview(location){
        iframeSrc = `${page.props.SHOPBOSS_SITE}customreports_classic/share-report.php?reportDir=${location.replace('customreports/', '')}&token=4I6OITopBmpAzFIodd5xqtQZ4PrLDC`;

        window.open(iframeSrc, '_blank').focus();
    }

    const reportShare = useForm({
        id: "",
        name: "",
        description: "",
        category: "",
        location: "",
        columns: ""
    })

    const reportCategories = [
        "Ar Aging",
        "Inventory",
        "Discount",
        "Labor+sold",
        "Spd",
        "Commission",
        "Fee",
        "Parts Invoice Number",
        "Service Writer Commissions",
        "Customer",
        "Discounts",
        "Concerns Tracking"
    ]

    function addToDirectory(id, name, description, location){
        reportShare.id = id
        reportShare.name = name
        reportShare.description = description
        reportShare.location = location

        reportModal.addToDirectory = false;
        reportModal.addToDirectory = true;
    }


    function addReportToSharedDirectory(){
        let path = reportShare.location.substring(0, reportShare.location.lastIndexOf('/'))
        path = path.replace('customreports/', '')
        path = path.replace(/\//g, '\\');

        reportShare.post(route('custom-reports.addToShared'), {
            preserveState: false,
            onSuccess: () => {
                const id = page.props.flash.data
                addReportToSharedDirectoryIntoShopboss(id, path)
            }
        })
    }

    function addReportToSharedDirectoryIntoShopboss(id, path){
        const requestAddress = `${page.props.SHOPBOSS_SITE}src/private/components/control_panel/reports_portal/rm_cp.php`

        axios.get(requestAddress,  {
            params: {
                token: '4f2Igvw6PH6DkFn6K3mR8erQVAyIgvw6PH6DkP4fspbBi4m5QL',
                action: 'cp',
                id: id,
                reportDirectory: path
            }
        })
        .then(response =>{
            sharedReportsStore.setState('customReportsShopbossAPIWasSuccessful', 'activate')
        })
        .catch(error => {
            console.log('Error: ', error)
        });
    }
</script>


<style>
    .dark-theme .reactiveReportComponent,
    .dark-theme .reactiveReportComponent .dataTables_processing{
        color: #fff;
        width: 100%;
        overflow: hidden;
    }
    td{
        text-transform: capitalize;
    }
</style>
