<template>
    <div class="bandwith-messages card reactiveReportComponent">
        <div class="card-body">
            <div class="dataTables_length pb-3">
                <label for="data-table-length-select" class="me-2">Show</label>

                <select id="data-table-length-select" v-model="selectedEntries" class="form-select form-select-sm d-inline-block w-auto">
                    <option v-for="option in entriesOptions" :key="option" :value="option">
                        {{ option }}
                    </option>
                </select>

                <span>entries</span>
            </div>

            <div class="dataTables_processing card" style="display: block;"  v-if="loading">Processing...</div>

            <table id="bandwithMessages" class="table table-bordered data-table table-hover" width="100%">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Shop ID</th>
                        <th>Shop Type</th>
                        <th>Campaign Id</th>
                        <th>Source Telephone</th>
                        <th>Destination Telephone</th>
                        <th>Carrier Name</th>
                        <th>Status</th>
                        <th>Direction</th>
                        <th>Error Code</th>
                        <th
                            :class="{
                                'sorting_asc': computedParams.sort === 'receiveTime:asc',
                                'sorting_desc': computedParams.sort === 'receiveTime:desc'
                            }" @click="sortData('receiveTime')">
                            Time
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="message in data.messages">
                        <td>{{ message.messageId }}</td>
                        <td>{{ message.shopid }}</td>
                        <td>{{ message.sourceShop }}</td>
                        <td>{{ message.campaignId }}</td>
                        <td>{{ formatPhoneNumber(message.sourceTn) }}</td>
                        <td>{{ formatPhoneNumber(message.destinationTn) }}</td>
                        <td>{{ message.carrierName }}</td>
                        <td>{{ capitalizeWords(message.messageStatus) }}</td>
                        <td>{{ capitalizeWords(message.messageDirection) }}</td>
                        <td>{{ message.errorCode }}</td>
                        <td>{{ formatDate(message.receiveTime, true) }}</td>
                    </tr>
                </tbody>
            </table>
            <div class="row">
                <div class="col-sm-12 col-md-5">
                    <div class="dataTables_info" id="data-table-info" role="status" aria-live="polite">
                        Showing {{data.messages.length}} entries
                    </div>
                </div>
                <div class="col-sm-12 col-md-7">
                    <div class="dataTables_paginate paging_simple_numbers">
                        <ul class="pagination">
                            <li class="paginate_button page-item previous"
                                v-if="data.pageInfo && data.pageInfo.prevPageToken"
                                @click="goToPage(data.pageInfo.prevPageToken)"
                            >
                                <a href="#" aria-controls="data-table" data-dt-idx="0" tabindex="0" class="page-link">Prev</a>
                            </li>

                            <li class="paginate_button page-item next"
                                v-if="data.pageInfo && data.pageInfo.nextPageToken"
                                @click="goToPage(data.pageInfo.nextPageToken)"
                            >
                                <a href="#" aria-controls="data-table" data-dt-idx="1" tabindex="0" class="page-link">Next</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
    import { ref, watch, onMounted, computed } from 'vue';
    import { formatPhoneNumber, formatDate, asDollars, capitalizeWords } from '@/utils/utils.js'

    const props = defineProps({
        startDate: Date,
        endDate: Date,
        messageStatus: String,
        search: Array
    });

    const selectedEntries = ref(10);
    const entriesOptions = [10, 25, 50, 100, 250, 500, 1000, 2000, 5000, 10000];
    const data = ref({ messages: [], totalCount: 0, pageInfo: {} });
    const page = ref(null);
    const loading = ref(true);
    const sort = ref('receiveTime:desc');

    const fetchMessages = async () => {
        loading.value = true;
        try {
            const response = await axios.get(route('bandwith-messages.datatable', computedParams.value));
            data.value = response.data.data;
            loading.value = false;
        } catch (error) {
            console.error('Error fetching messages:', error);
        } finally {
            loading.value = false;
        }
    };

    onMounted(fetchMessages);

    const computedParams = computed(() => ({
        start_date: props.startDate,
        end_date: props.endDate,
        limit: selectedEntries.value,
        pageToken: page.value,
        messageStatus: props.messageStatus,
        search: props.search,
        sort: sort.value
    }));

    const goToPage = (token) => {
        page.value = token;
    };

    const sortData = (field) => {
        if (sort.value === `${field}:asc`) {
            sort.value = `${field}:desc`;
        } else {
            sort.value = `${field}:asc`;
        }
    };


    watch([computedParams], fetchMessages, { immediate: true, deep: true });
</script>



<style scoped>
    .dark-theme .reactiveReportComponent,
    .dark-theme .reactiveReportComponent .dataTables_processing{
        color: #fff;
        width: 100%;
        overflow: hidden;
    }
    td{
        text-transform: capitalize;
    }

    .bandwith-messages .pagination{
        justify-content: end;
        margin: 0;
    }

    .bandwith-messages .dataTables_processing.card{
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
        padding: 14px;
        width: 140px!important;
    }

    .bandwith-messages .sorting_asc,
    .bandwith-messages .sorting_desc {
        cursor: pointer;
        position: relative;
        padding-right: 1.5em;
    }

    .bandwith-messages .sorting_asc:after,
    .bandwith-messages .sorting_desc:after{
        content: "↑↓";
        font-size: 1.2em;
        position: absolute;
        right: 0.5em;
        top: 50%;
        transform: translateY(-50%);
        display: inline-block;
    }

    .bandwith-messages .sorting_asc:after {
        content: "↑";
    }

    .bandwith-messages .sorting_desc:after {
        content: "↓";
    }
</style>
