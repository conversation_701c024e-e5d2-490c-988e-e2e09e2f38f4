<template>
    <div class="card reactiveReportComponent">
        <div class="card-body">
            <table id="sharedReports" class="table table-bordered data-table table-hover" width="100%">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th style="width: 50px;">Shared</th>
                        <th style="width: 250px;">Name</th>
                        <th style="width: 250px;">Category</th>
                        <th style="width: 250px;">Description</th>
                        <th style="width: 250px;">Columns</th>
                        <th style="width: 200px;">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="sharedReport in props.sharedReports">
                        <td>{{ sharedReport.id }}</td>
                        <td
                            @click="sharedReport.customReports.length > 0 ? viewSharedReports(sharedReport.customReports) : null"
                            style="cursor: pointer;"
                            :class="{ 'has-reports': sharedReport.customReports.length > 0 }"
                            >
                            {{ sharedReport.customReports.length > 0 ? sharedReport.customReports.length : '' }}
                        </td>
                        <td>
                            <textarea type="text" class="form-control" v-model="sharedReport.name" required></textarea>
                        </td>
                        <td>
                            <select class="form-control" v-model="sharedReport.category" required>
                                <option value="Ar Aging">Ar Aging</option>
                                <option value="Commission">Commission</option>
                                <option value="Concerns Tracking">Concerns Tracking</option>
                                <option value="Customer">Customer</option>
                                <option value="Discount">Discount</option>
                                <option value="Discounts">Discounts</option>
                                <option value="Fee">Fee</option>
                                <option value="Inventory">Inventory</option>
                                <option value="Labor+sold">Labor+sold</option>
                                <option value="Parts Invoice Number">Parts Invoice Number</option>
                                <option value="Service Writer Commisions">Service Writer Commisions</option>
                                <option value="Spd">Spd</option>
                            </select>
                        </td>
                        <td>
                            <textarea type="text" class="form-control" v-model="sharedReport.description" required></textarea>
                        </td>
                        <td>
                            <textarea type="text" class="form-control" v-model="sharedReport.columns" required></textarea>
                        </td>
                        <td>
                            <div class="d-grid-custom">
                                <div @click="editSharedReport(sharedReport)">
                                    <ButtonComponent name="Edit" class="success" :processing="sharedReport.processing" :width="'130'"/>
                                </div>
                                <div @click="deleteSharedReportModal(sharedReport)">
                                    <ButtonComponent name="Delete" class="danger" :width="'130'"/>
                                </div>
                                <div @click="preview(sharedReport.location)">
                                    <ButtonComponent name="Preview" class="secondary" :width="'130'"/>
                                </div>
                                <div @click="addToShop(sharedReport.id, sharedReport.name)">
                                    <ButtonComponent name="Add To Shop" class="primary" :width="'130'"/>
                                </div>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <ModalComponent v-model="sharedReportsStore.state.deleteSharedReportModal" title="Delete Shared Report">
        <template #body>
            <p>Are you sure you want to delete shared report?</p>
        </template>
        <template #footer>
            <ButtonComponent @click="deleteSharedReport" :processing="deleteReport.processing" name="Delete" class="danger"/>
        </template>
    </ModalComponent>

    <form @submit.prevent="addReportToShop.put(route('custom-reports.addToShop'), {
            preserveState: false,
            onSuccess: () => {
                sharedReportsStore.setMessage('Its saved')
                sharedReportsStore.setState('addToShopModalVisible', 'deactivate')
                sharedReportsStore.setState('addToShopWasSuccessful', 'activate')
            }
        })"
    >
        <ModalComponent v-model="sharedReportsStore.state.addToShopModalVisible" title="Add Report">
            <template #body>
                <div>
                    <Select2Component
                        :data="props.companies"
                        :keyValue="true"
                        v-model="addReportToShop.shopid"
                        label="Shop ID"
                        iconClass="bx bx-store"
                        :error="addReportToShop.errors.shopid"
                        :required='true'
                    />

                    <InputComponent
                        v-model="addReportToShop.name"
                        placeholder="Name"
                        label="Name"
                        iconClass="bx bx-star"
                        :error="addReportToShop.errors.name"
                        :required='true'
                    />
                </div>
            </template>
            <template #footer>
                <ButtonComponent :processing="addReportToShop.processing"/>
            </template>
        </ModalComponent>
    </form>

    <ModalComponent v-model="sharedReportsStore.state.sharedReportsForShopsModalVisible" title="Shared Reports" size="big">
        <template #body>
            <div>
                <table id="sharedReports2" class="table table-bordered data-table table-hover" width="100%">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Shop</th>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="sharedReportsForShop in sharedReportsForShops">
                            <td>{{ sharedReportsForShop.id }}</td>
                            <td>{{ sharedReportsForShop.shopid }}</td>
                            <td>
                                <input type="text" class="form-control" v-model="sharedReportsForShop.name">
                            </td>
                            <td>
                                <input type="text" class="form-control" v-model="sharedReportsForShop.desc">
                            </td>
                            <td class="d-flex gap-1 justify-content-center">
                                <div @click="editSharedReportForShop(sharedReportsForShop)">
                                    <ButtonComponent :processing="sharedReportsForShop.processingEdit" name="Edit"/>
                                </div>

                                <div @click="deleteSharedReportForShop(sharedReportsForShop)">
                                    <ButtonComponent :processing="deleteCustomReports.processing" class="danger" name="Delete"/>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </template>
    </ModalComponent>

    <BaseAlertComponent v-if="isAlertVisible"
        :messageType="$page.props.flash.message ? 'success' : 'error'"
        :title="'Shared Report'"
        :message="$page.props.flash.message || $page.props.flash.error"
    />
</template>

<script setup>
    import { onMounted, ref, computed } from 'vue';
    import { useForm, usePage } from "@inertiajs/vue3"
    import { formatDate } from '@/utils/utils.js'
    import ModalComponent from '@/components/utils/ModalComponent.vue'
    import InputComponent from '@/components/utils/InputComponent.vue'
    import ButtonComponent from '@/components/utils/ButtonComponent.vue'
    import Select2Component from '@/components/utils/Select2Component.vue'
    import BaseAlertComponent from '../alerts/BaseAlertComponent.vue';
    import { useSharedReportsStore } from '@/stores/shop/sharedReportsStore'
    import axios from 'axios';

    const page = usePage();

    const sharedReportsStore = useSharedReportsStore()

    const props = defineProps({
        sharedReports: Array,
        companies: Object
    });

    onMounted(() => {
        setAsDatatable('#sharedReports')
    })

    function setAsDatatable(tableID){
        const table = $(tableID).DataTable({
            processing: true,
            buttons: ['copy', 'excel', 'print'],
            columnDefs: [
                { searchable: false, targets: [2, 3, 4, 5, 6] },  // Name, Category, Description, Columns, Actions
                { orderable: false, targets: [2, 3, 4, 5, 6] }    // Name, Category, Description, Columns, Actions
            ],
            initComplete: function() {
                setTimeout(() => {
                    table.buttons().container().appendTo(`${tableID}_wrapper .col-md-6:eq(0)`);
                }, 1);
            },
        });

        return table;
    }

    const editShareReport = useForm({
        id: '',
        name: '',
        category: '',
        description: '',
        columns: ''
    })

    function editSharedReport(report){
        editShareReport.id = report.id
        editShareReport.name = report.name
        editShareReport.category = report.category
        editShareReport.description = report.description
        editShareReport.columns = report.columns
        report.processing = true

        editShareReport.put(route('custom-reports.editSharedReport'), {
            preserveState: false,
            onSuccess: () => {
                sharedReportsStore.setState('editSharedReportWasSuccessful', 'activate')
                report.processing = false
            }
        })
    }

    var iframeSrc = ref({});

    function preview(location){
        iframeSrc = `${page.props.SHOPBOSS_SITE}customreports_classic/share-report.php?reportDir=${location.replace('customreports/', '')}&token=4I6OITopBmpAzFIodd5xqtQZ4PrLDC`;

        window.open(iframeSrc, '_blank').focus();
    }

    const addReportToShop = useForm({
        id: '',
        name: '',
        shopid: ''
    })

    function addToShop(reportId,reportName){
        addReportToShop.id = reportId
        addReportToShop.name = reportName

        sharedReportsStore.setState('addToShopModalVisible', 'activate')
    }

    const sharedReportsForShops = ref([])

    function viewSharedReports(reports){
        sharedReportsForShops.value = reports

        sharedReportsStore.setState('sharedReportsForShopsModalVisible', 'activate')
        setTimeout(() => {
            setAsDatatable('#sharedReports2')
        }, 1);
    }

    const updateShopReport = useForm({
        id: '',
        name: '',
        description: ''
    })

    function editSharedReportForShop(report){
        updateShopReport.id = report.id
        updateShopReport.name = report.name
        updateShopReport.description = report.desc
        report.processingEdit = true

        updateShopReport.put(route('custom-reports.editForShop'), {
            onSuccess: () => {
                sharedReportsStore.setState('sharedReportsForShopsModalVisible', 'deactivate')
                report.processingEdit = false
            }
        })
    }

    const deleteReport = useForm({
        report: ''
    })

    function deleteSharedReportModal(report){
        deleteReport.report = report

        sharedReportsStore.setState('deleteSharedReportModal', 'activate')
    }

    function deleteSharedReport(){
        deleteReport.delete(route('custom-reports.deleteSharedReport', {id : deleteReport.report.id}), {
            preserveState: false,
            onSuccess: () => {
                deleteSharedReportInDirectory(deleteReport.report.id)
            }
        })
    }

    function deleteSharedReportInDirectory(id){
        const headers = { 'Authorization': 'Bearer 4f2Igvw6PH6DkFn6K3mR8erQVAyIgvw6PH6DkP4fspbBi4m5QL' };
        const requestAddress = `${page.props.SHOPBOSS_SITE}src/private/components/control_panel/reports_portal/rm_cp.php?action=rm&id=${id}`

        axios.get(requestAddress, {headers})
            .then(response =>{
                console.log(response)
            })
            .catch(error => {
                console.log('Error: ', error)
            });

        sharedReportsStore.setState('sharedReportsForShopsModalVisible', 'deactivate')
        sharedReportsStore.setState('deleteSharedReportForShopWasSuccessful', 'activate')
        sharedReportsStore.setState('deleteSharedReportModal', 'deactivate')
    }

    const deleteCustomReports = useForm({})
    function deleteSharedReportForShop(report){
        deleteCustomReports.delete(route('custom-reports.deleteForShop', {id : report.id}), {
            preserveState: false,

            onSuccess: () => {
                sharedReportsStore.setState('deleteSharedReportForShopWasSuccessful', 'activate')
                sharedReportsStore.setState('sharedReportsForShopsModalVisible', 'deactivate')
            }
        })
    }

    const isAlertVisible = computed(() => {
        return (
            (
                updateShopReport.wasSuccessful ||
                sharedReportsStore.state.addToShopWasSuccessful ||
                sharedReportsStore.state.deleteSharedReportForShopWasSuccessful ||
                sharedReportsStore.state.editSharedReportWasSuccessful
            ) &&
            (page.props.flash.message || page.props.flash.error)
        );
    });
</script>


<style>
    .dark-theme .reactiveReportComponent,
    .dark-theme .reactiveReportComponent .dataTables_processing{
        color: #fff;
        width: 100%;
        overflow: hidden;
    }
    td{
        text-transform: capitalize;
    }

    .d-grid-custom{
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 10px;
    }
</style>
