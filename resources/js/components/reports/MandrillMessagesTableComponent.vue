<template>
    <div class="bandwith-messages card reactiveReportComponent">
        <div class="card-body">
            <div class="dataTables_processing card" v-if="loading">Processing...</div>
            <table :id="tableId" class="table table-bordered data-table table-hover" width="100%">
                <thead>
                    <tr>
                        <th>Id</th>
                        <th>Sender</th>
                        <th>Receiver</th>
                        <th>Subject</th>
                        <th>State</th>
                        <th>Timestamp</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</template>

<script setup>
    import { ref, watch, onMounted, computed, onBeforeUnmount } from 'vue';
    import { formatDate } from '@/utils/utils.js'
    // import { format, addHours, isBefore } from 'date-fns';

    const props = defineProps({
        startDate: Date,
        endDate: Date,
        messageStatus: String,
    });

    const tableId = ref(`mandrill-messages-${Math.random().toString(36).substr(2, 9)}`);
    const messages = ref([]);
    const loading = ref(true);
    let dataTable = null;

    const computedParams = computed(() => ({
        startDate: props.startDate,
        endDate: props.endDate,
        messageStatus: props.messageStatus,
    }));


    function fetchMessages() {
        loading.value = true;
        messages.value = [];

        let start = new Date(computedParams.value.startDate);
        let end = new Date(computedParams.value.endDate);

        // while (isBefore(start, end)) {
        //     const next = addHours(start, 4);
        //     const rangeStart = format(start, 'yyyy-MM-dd');
        //     const rangeEnd = format(next < end ? next : end, 'yyyy-MM-dd');

        //     console.log(rangeStart, rangeEnd)
        //     // try {
        //     //     const response = await axios.post('https://mandrillapp.com/api/1.0/messages/search.json', {
        //     //         key: 'md-UYhNcWBIpF6LDyQeALnA8w',
        //     //         date_from: rangeStart,
        //     //         date_to: rangeEnd,
        //     //         query: computedParams.value.messageStatus,
        //     //         limit: 1000
        //     //     });

        //     //     if (Array.isArray(response.data) && result.length > 0) {
        //     //         messages.value.push(...response.data);
        //     //     }
        //     // } catch (e) {
        //     //     console.error('Error fetching Mandrill messages:', e);
        //     // }

        //     start = next;
        // }

        setDatatable();
        loading.value = false;
    }


    function setDatatable(){
        if (dataTable) {
                dataTable.destroy();
            }

        // Initialize DataTable with the new data
        dataTable = $(`#${tableId.value}`).DataTable({
            data: messages.value,
            columns: [
                { data: '_id' },
                { data: 'sender' },
                { data: 'email' },
                { data: 'subject' },
                { data: 'state' },
                {
                    data: 'ts',
                    render: function(data) {
                        return new Date(data*1000);
                    }
                }
            ],
            processing: true,
            buttons: ['copy', 'excel', 'print'],
            initComplete: function() {
                this.api().buttons().container().appendTo(`#${tableId.value}_wrapper .col-md-6:eq(0)`);
            },
        });
    }

    onMounted(() => {
        fetchMessages();
    });

    // Clean up DataTable on component unmount
    onBeforeUnmount(() => {
        if (dataTable) {
            dataTable.destroy();
        }
    });

    watch([computedParams], fetchMessages, { immediate: false, deep: true });
</script>

<style>
    .dark-theme .reactiveReportComponent,
    .dark-theme .reactiveReportComponent .dataTables_processing{
        color: #fff;
        width: 100%;
        overflow: hidden;
    }

    .card.reactiveReportComponent{
        overflow: auto;
    }
</style>
