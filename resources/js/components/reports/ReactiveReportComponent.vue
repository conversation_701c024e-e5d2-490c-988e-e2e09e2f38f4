<template>
    <div v-show="isDataAvailable || !hideIfDataEmpty">
        <div class="card reactiveReportComponent">
            <div class="card-body">
                <div v-if="props.title">
                    <h4 class="text-center">{{ props.title }}</h4>
                    <hr>
                </div>

                <table :id="tableId" class="table table-bordered data-table table-hover" width="100%">
                    <thead>
                        <tr>
                            <th v-for="(column, index) in columns" :key="index" class="border-bottom"
                                :style="{
                                    width: column.maxWidth ? column.maxWidth + 'px !important' : 'auto'
                                }">
                                {{ column.label }}
                            </th>
                        </tr>
                    </thead>
                    <tfoot v-if="totals && !dataIsProcessing" style="color: #7f8695; border: 1px solid #7f8695">
                        <tr></tr>
                        <tr >
                            <td v-for="total in totals">
                                {{ total }}
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</template>

<script setup>
    import { onMounted, ref } from 'vue';

    const props = defineProps({
        route: String,
        columns: Array,
        serverSide: {
            type: Boolean,
            default: false
        },
        id: {
            type: String,
            default: () => `data-table-${Math.random().toString(36).substr(2, 9)}`
        },
        title: {
            type: String
        },
        hideIfDataEmpty: {
            type: Boolean,
            default: false
        },
        totals: Array,
        print: {
            type: Boolean,
            default: true
        }
    });

    const columns = ref(props.columns);
    const route = ref(props.route);
    const serverSide = ref(props.serverSide);
    const tableId = ref(props.id);
    const isDataAvailable = ref(false);
    const dataIsProcessing = ref(true);

    onMounted(() => {
        const defaultSortColumnIndex = columns.value.findIndex(column => column.order);
        const defaultSortOrder = defaultSortColumnIndex !== -1 ? columns.value[defaultSortColumnIndex].order : 'asc';

        const sortColumn = defaultSortColumnIndex !== -1 ? defaultSortColumnIndex : 0;
        const sortDirection = defaultSortColumnIndex !== -1 ? defaultSortOrder : 'asc';

        const table = $(`#${tableId.value}`).DataTable({
            processing: true,
            serverSide: serverSide.value,
            pageLength: 10,
            ajax: {
                url: route.value,
                dataSrc: function(json) {
                    isDataAvailable.value = json.data && json.data.length > 0;
                    dataIsProcessing.value = false;
                    return json.data;
                },
            },
            columns: columns.value,
            autoWidth: false,
            order: [[sortColumn, sortDirection]],
            buttons: [
                'copy',
                {
                    extend: 'excel',
                    title: props.title,
                    customizeData: function(data) {
                        data.body.forEach(row => {
                            row.forEach((cell, index) => {
                                if (typeof cell === 'string' && cell.includes('$')) {
                                    row[index] = `$ ${parseFloat(cell.replace(/[^0-9.-]+/g, '')).toFixed(2)}`;
                                }
                            });
                        });
                    },
                },
                ...(props.print ? [{
                    extend: 'print',
                    autoPrint: true,
                    orientation: 'landscape',
                    customize: function (win) {
                        $(win.document.body).css('font-size', '12pt');
                        $(win.document.body).find('table').css('width', '100%');
                        $(win.document.body).find('table td').css({
                            'max-width': '300px',
                            'overflow': 'hidden',
                            'font-size': '10px',
                        });
                        $(win.document.body).find('table th').css('max-width', '300px');
                    }
                }] : [])
            ],
            initComplete: function() {
                table.buttons().container().appendTo(`#${tableId.value}_wrapper .col-md-6:eq(0)`);
                dataIsProcessing.value = false;
            },
        });
    });
</script>


<style>
    .dark-theme .reactiveReportComponent,
    .dark-theme .reactiveReportComponent .dataTables_processing{
        color: #fff;
        width: 100%;
        overflow: hidden;
    }

    .card.reactiveReportComponent{
        overflow: auto;
    }
</style>
