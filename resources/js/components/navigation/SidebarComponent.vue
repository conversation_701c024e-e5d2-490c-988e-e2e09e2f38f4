<template>
    <div class="sidebar-wrapper"
        data-simplebar="true"
        @mouseenter="$emit('mouseEnter')"
        @mouseleave="$emit('mouseLeave')">
        <div class="sidebar-header">
            <div>
                <img src="/assets/images/logo-icon.png" class="logo-icon" alt="logo icon" />
            </div>
            <div>
                <h4 class="logo-text">
                    <img src="/assets/images/logo-text.png" class="logo-icon logo-icon-text" alt="logo icon" />
                </h4>
            </div>
            <div class="toggle-icon ms-auto" @click="$emit('toggleMenu')">
                <i class='bx bx-arrow-back'></i>
            </div>
        </div>

        <ul class="metismenu" id="menu">
            <li v-if="allowedRoles(['superadmin', 'admin'])">
                <Link :href="route('dashboard.index')">
                    <div class="parent-icon">
                        <i class='bx bx-home-alt'></i>
                    </div>
                    <div class="menu-title">Dashboard</div>
                </Link>
            </li>

            <li v-if="allowedRoles(['superadmin', 'admin', 'customer success', 'customer support', 'sales'])">
                <Link :href="route('report.shoplist.index')">
                    <div class="parent-icon">
                        <i class='bx bx-store'></i>
                    </div>
                    <div class="menu-title">Shop List</div>
                </Link>
            </li>

            <li v-if="allowedRoles(['matco'])">
                <Link :href="route('report.shoplist.matco.index')">
                    <div class="parent-icon">
                        <i class='bx bx-home-alt'></i>
                    </div>
                    <div class="menu-title">Shop List</div>
                </Link>
            </li>

            <li :class="isSubMenuOpen && 'mm-active'">
                <Link :href="route('shop.index')" :class="isSubMenuOpen && 'has-arrow'">
                    <div class="parent-icon">
                        <i class='bx bx-wrench'></i>
                    </div>
                    <div class="menu-title">Shop Detail</div>
                </Link>
                <ul v-show="isSubMenuOpen" v-if="shopid && companyName" class="sub-item">
                    <li>
                        <Link :href="route('shop.show', {shop: shopid})">
                            <i class='bx bx-radio-circle'></i>
                            Shop {{ companyName }} Details
                        </Link>
                    </li>
                    <li>
                        <a href="#" @click="shopStore.setState('changeStartingRONumber')">
                            <i class='bx bx-radio-circle'></i>
                            Change Starting RO Number
                        </a>
                    </li>
                    <li>
                        <a href="#" @click="shopStore.setState('partsTech')">
                            <i class='bx bx-radio-circle'></i>
                            Add Parts Tech
                        </a>
                    </li>
                    <li>
                        <a href="#" @click="shopStore.setState('suspendReactivate')">
                            <i class='bx bx-radio-circle'></i>
                            Suspend | Reactivate
                        </a>
                    </li>
                    <li>
                        <a href="#" @click="shopStore.setState('readOnly')">
                            <i class='bx bx-radio-circle'></i>
                            Read Only
                        </a>
                    </li>
                    <li>
                        <a href="#" @click="shopStore.setState('forceLogOut')">
                            <i class='bx bx-radio-circle'></i>
                            Force Log Out
                        </a>
                    </li>
                    <li>
                        <a href="#" @click="shopStore.setState('payBalanceLink')">
                            <i class='bx bx-radio-circle'></i>
                            Pay Balance Link
                        </a>
                    </li>
                    <li>
                        <a href="#" @click="shopStore.setState('changeShopPackage')">
                            <i class='bx bx-radio-circle'></i>
                            Change Shop Package
                        </a>
                    </li>
                    <li>
                        <a href="#" @click="shopStore.setState('joinedShops')">
                            <i class='bx bx-radio-circle'></i>
                            Linked Shops
                        </a>
                    </li>
                    <li>
                        <Link :href="route('api-key.index', {shop: shopid})">
                            <i class='bx bx-radio-circle'></i>
                            Add New API
                        </Link>
                    </li>
                    <li>
                        <a href="#" @click="shopStore.setState('bossBoard')">
                            <i class='bx bx-radio-circle'></i>
                            Boss Board
                        </a>
                    </li>
                    <li>
                        <a href="#" @click="shopStore.setState('dvi')">
                            <i class='bx bx-radio-circle'></i>
                            DVI
                        </a>
                    </li>
                    <li v-if="shopStore.state.shopPackageIsTrial">
                        <a href="#" @click="shopStore.setState('trialExpiration')">
                            <i class='bx bx-radio-circle'></i>
                            Trial Expiration
                        </a>
                    </li>
                    <li>
                        <a href="#" @click="shopStore.setState('churn')">
                            <i class='bx bx-radio-circle'></i>
                            Churn
                        </a>
                    </li>
                    <li>
                        <a href="#" @click="shopStore.setState('toggleMatco')">
                            <i class='bx bx-radio-circle'></i>
                            Switch To {{ shopStore.state.shopIsMatco ? "Shopboss" : "Matco" }}
                        </a>
                    </li>
                    <li>
                        <a href="#" @click="shopStore.setState('setSchool')">
                            <i class='bx bx-radio-circle'></i>
                            Set School
                        </a>
                    </li>
                    <li>
                        <a href="#" @click="shopStore.setState('tpmsShopCredentials')">
                            <i class='bx bx-radio-circle'></i>
                            TPMS Credentials
                        </a>
                    </li>
                    <li>
                        <a href="#" @click="shopStore.setState('epicorLogins')">
                            <i class='bx bx-radio-circle'></i>
                            Epicor Logins
                        </a>
                    </li>
                    <li>
                        <a href="#" @click="shopStore.setState('payments360Modal')">
                            <i class='bx bx-radio-circle'></i>
                            360 Payments
                        </a>
                    </li>
                    <li>
                        <a href="#" @click="shopStore.setState('addCardknoxKeys')">
                            <i class='bx bx-radio-circle'></i>
                            Add Cardknox Keys
                        </a>
                    </li>
                    <li>
                        <a href="#" @click="shopStore.setState('activateCardknox')">
                            <i class='bx bx-radio-circle'></i>
                            Activate Cardknox
                        </a>
                    </li>
                    <li>
                        <a href="#" @click="shopStore.setState('addCardknoxTerminal')">
                            <i class='bx bx-radio-circle'></i>
                            Add Cardknox Terminal
                        </a>
                    </li>
                    <li>
                        <a href="#" @click="shopStore.setState('betaFeaturesModal')">
                            <i class='bx bx-radio-circle'></i>
                            Beta Features
                        </a>
                    </li>

                    <li v-if="shopStore.state.shopIsTrial">
                        <a href="#" @click="shopStore.setState('switchToPaidModal')">
                            <i class='bx bx-radio-circle'></i>
                            Switch to Paid
                        </a>
                    </li>
                </ul>
            </li>

            <li v-if="allowedRoles(['superadmin', 'admin', 'sales'])">
                <Link :href="route('new-shop.index')">
                    <div class="parent-icon">
                        <i class='bx bxs-store'></i>
                    </div>
                    <div class="menu-title">Create Shop</div>
                </Link>
            </li>

            <li v-if="allowedRoles(['superadmin', 'admin', 'customer success'])">
                <Link :href="route('report.index')">
                    <div class="parent-icon">
                        <i class='bx bxs-report'></i>
                    </div>
                    <div class="menu-title">Reports</div>
                </Link>
            </li>

            <li v-if="allowedRoles(['superadmin', 'admin', 'customer support' ])">
                <Link :href="route('custom-reports.index')">
                    <div class="parent-icon">
                        <i class='bx bx-share-alt'></i>
                    </div>
                    <div class="menu-title">Shared Reports</div>
                </Link>
            </li>

            <li v-if="allowedRoles(['superadmin', 'admin'])">
                <Link :href="route('user.index')">
                    <div class="parent-icon">
                        <i class='bx bxs-group'></i>
                    </div>
                    <div class="menu-title">Users</div>
                </Link>
            </li>
        </ul>
    </div>
</template>


<script setup>
    import { Link, usePage } from '@inertiajs/vue3';
    import { ref, computed } from 'vue';

    import { useShopStore } from '@/stores/shop/shopStore'
    const shopStore = useShopStore()

    const { props } = usePage();

    const shopid = props?.shopid
    const companyName = props?.companyDetails?.companyName || props?.companyName
    const userRole = props.auth.user.role

    const isShopPage = /^\/dashboard\/shop\/\d+(\/[a-zA-Z0-9-]+)*\/?[a-zA-Z0-9-]*$/.test(usePage().url);
    const isSubMenuOpen = ref(isShopPage && userRole !== 'sales');

    // Check if user got access to concrete thing
    const allowedRoles = computed(() => (roles) => roles.includes(userRole));
</script>

<style scoped>
    a{
        user-select: none;
    }

    .has-arrow::after{
        transform: rotate(-135deg);
    }

</style>
