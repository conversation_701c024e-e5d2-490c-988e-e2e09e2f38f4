<template>
    <header>
        <div class="topbar d-flex align-items-center">
            <nav class="navbar navbar-expand gap-3">
                <div class="mobile-toggle-menu" @click="$emit('toggleMenu')"><i class='bx bx-menu'></i>
                </div>
                <div class="top-menu ms-auto">
                    <ul class="navbar-nav align-items-center gap-1">
                        <li class="nav-item dark-mode d-sm-flex">
                            <a class="nav-link dark-mode-icon" href="javascript:;" @click="toggleDarkMode">
                                <i :class="darkModeIconClass"></i>
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="user-box dropdown ps-3">
                    <a class="d-flex align-items-center nav-link dropdown-toggle gap-3 dropdown-toggle-nocaret" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <div>
                            <img src="@/../assets/images/user-setting.png" class="img-fluid" width="35" alt=""/>
                        </div>
                        <div class="user-info">
                            <p class="user-name mb-0">{{ user }}</p>
                            <p class="designattion mb-0">{{ capitalizeWords(role) }}</p>
                        </div>
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <!-- <li>
                            <Link class="dropdown-item d-flex align-items-center" href="javascript:;"><i class="bx bx-user fs-5"></i><span>Profile</span></Link>
                        </li>
                        <li>
                            <Link class="dropdown-item d-flex align-items-center" href="javascript:;"><i class="bx bx-cog fs-5"></i><span>Settings</span></Link>
                        </li>
                        <li>
                            <div class="dropdown-divider mb-0"></div>
                        </li> -->
                        <li>
                            <Link class="dropdown-item d-flex align-items-center" href="javascript:;" @click="logout"><i class="bx bx-log-out-circle"></i><span>Logout</span></Link>
                        </li>
                    </ul>
                </div>
            </nav>
            <div class="overlay toggle-icon"></div>
        </div>
    </header>
</template>

<script setup>
    import { Link, usePage, router } from '@inertiajs/vue3';
    import { ref } from 'vue';
    import { capitalizeWords } from '@/utils/utils.js'

    function logout() {
        router.post('/logout');
    }

    const page = usePage();
    const user = page.props.auth.user.name
    const role = page.props.auth.user.role

    const setColorSchemeAttribute = (className) => {
        document.querySelector('html')?.setAttribute('class', className)
    }


    const darkModeIconClass = ref('');

    if (localStorage.getItem('color_mode') === 'dark') {
        darkModeIconClass.value = 'bx bx-sun';
        setColorSchemeAttribute('dark-theme');
    } else {
        darkModeIconClass.value = 'bx bx-moon';
        setColorSchemeAttribute('light-theme');
    }

    function toggleDarkMode() {
        if (darkModeIconClass.value === 'bx bx-sun') {
            darkModeIconClass.value = 'bx bx-moon';
            localStorage.setItem('color_mode', 'white')
            setColorSchemeAttribute('light-theme');
        } else {
            darkModeIconClass.value = 'bx bx-sun';
            localStorage.setItem('color_mode', 'dark')
            setColorSchemeAttribute('dark-theme');
        }
    }
</script>
