<template>
    <div>
        <label>Date Range</label>
        <flat-pickr v-model="localDate" :config="config" class="form-control date-range"/>
    </div>
</template>

<script setup>
    import { ref, watch } from 'vue';
    import flatPickr from 'vue-flatpickr-component';
    import 'flatpickr/dist/flatpickr.css';

    const props = defineProps({
        date: String,
        config: Object
    });

    const emit = defineEmits(['update:dateRange', 'update:reportDateRange']);
    const localDate = ref(props.date);

    const config = {
        mode: 'range',
        dateFormat: "M-j-Y",
        altInput: true,
        altFormat: "M-j-Y",
        onClose: function (selectedDates, dateStr, instance) {
            if (selectedDates.length === 2) {
                const dateRange = selectedDates.map(date => {
                    const year = date.getFullYear();
                    const month = String(date.getMonth() + 1).padStart(2, '0');
                    const day = String(date.getDate()).padStart(2, '0');
                    return `${year}-${month}-${day}`;
                });

                emit('update:dateRange', dateRange);
            }
        }
    };

    watch(() => props.date, (newDate) => {
        localDate.value = newDate;
    });
</script>
