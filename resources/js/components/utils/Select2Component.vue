<template>
    <head>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" />
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />
    </head>
    <div :class="['mb-3', labelPosition === 'left' ? 'input-component' : '']">
        <label :for="inputId" class="centered" v-if="labelPosition === 'left'">{{ label }}</label>
        <div class="select-2-basic">
            <label :for="inputId" class="centered" v-if="labelPosition === 'top'">{{ label }}</label>
            <div :class="['input-group', error && 'text-danger input-error-group']">
                <span :class="['input-group-text', error && 'border-red text-danger']" v-if="iconClass">
                    <i :class="iconClass"></i>
                </span>
                <select ref="select2" class="form-select" data-placeholder="Choose an option" :id="inputId" :name="inputId" style="width: 250px;" :multiple="props.multiple" :disabled="disabled">
                    <option
                        v-for="(item, key) in data"
                        :key="props.keyValue ? key : item"
                        :value="props.keyValue ? key : item"
                    >
                        {{ props.keyValue ? `${key} - ${item}` : capitalizeWords(item) }}
                    </option>
                </select>
            </div>
            <div v-if="error" class="text-danger pt-1">
                {{ error }}
            </div>
        </div>
    </div>
</template>

<script setup>
    import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
    import { capitalizeWords } from '@/utils/utils.js'
    import '@assets/css/input.css'

    const props = defineProps({
        label: {
            type: String,
            required: true
        },
        labelPosition: {
            type: String,
            required: false,
            default: "left"
        },
        inputId: {
            type: String,
            required: false,
            default: () => 'input-' + Math.random().toString(36).substr(2, 9)
        },
        iconClass: {
            type: String,
            required: false
        },
        error: {
            type: String,
            required: false
        },
        required: {
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: 'text'
        },
        placeholder: {
            type: String,
            default: ''
        },
        modelValue: {
            type: [String, Array],
            default: () => []
        },
        data: {
            type: Object,
            required: true
        },
        multiple: {
            type: Boolean,
            default: false
        },
        keyValue: {
            type: Boolean,
            default: false
        },
        disabled: {
            type: Boolean,
            default: false
        }
    });

    const emit = defineEmits(['update:modelValue']);
    const select2 = ref(null);

    onMounted(() => {
        $(select2.value).select2({
            theme: "bootstrap-5",
            width: 'resolve',
            placeholder: $(select2.value).data('placeholder'),
        });

        $(select2.value).val(props.modelValue).trigger('change');

        $(select2.value).on('change', function () {
            emit('update:modelValue', $(this).val());
        });
    });

    // Cleanup on unmount
    onBeforeUnmount(() => {
        $(select2.value).select2('destroy');
    });
</script>

<style>
    .select-2-basic .text-danger .select2-selection{
        border: 1px solid red !important;
    }
</style>
