<template>
    <div v-if="isOpen" class="modal-overlay" @click="closeModal">
        <div :class="['modal-dialog', 'modal-dialog-centered', modalSize, { 'modal-show': showModal, 'modal-hide': !showModal }]" @click.stop>
            <div class="card">
                <div class="card-body">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">{{ title }}</h5>
                            <button type="button" class="btn-close" @click="closeModal" aria-label="Close"></button>
                        </div>
                        <hr>
                        <div class="modal-body">
                            <div v-if="Object.keys(errors).length" class="alert alert-danger">
                                <div v-for="(error, field) in errors" :key="field">
                                    {{ error }}
                                </div>
                            </div>
                            <slot name="body"></slot>
                        </div>
                        <div class="modal-footer text-center">
                            <button type="button" class="btn btn-secondary me-2" @click="closeModal">Close</button>
                            <slot name="footer"></slot>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
    import { ref, watch, computed } from 'vue';

    const props = defineProps({
        modelValue: Boolean,
        title: String,
        size: {
            type: String,
            default: 'normal'
        },
        errors: {
            type: Object,
            default: () => ({})
        }
    });

    const emit = defineEmits();
    const isOpen = ref(props.modelValue);
    const showModal = ref(false);

    watch(() => props.modelValue, (newValue) => {
        isOpen.value = newValue;
        if (newValue) {
            setTimeout(() => { showModal.value = true; }, 10);
        } else {
            showModal.value = false;
            setTimeout(() => { isOpen.value = false; }, 300);
        }
    });

    function closeModal() {
        showModal.value = false;
        setTimeout(() => {
            isOpen.value = false;
            emit('update:modelValue', false);
        }, 300);
    }

    const sizeClasses = {
        small: 'modal-sm',
        medium: 'modal-lg',
        big: 'modal-xl',
        normal: 'modal-normal'
    };

    const modalSize = sizeClasses[props.size];
    const errors = computed(() => props.errors);
</script>

<style scoped>
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 120;
    }

    .modal-header{
        justify-content: space-between;
    }

    .modal-footer{
        justify-content: center;
    }

    .modal-show {
        opacity: 1;
        transform: scale(1);
        transition: opacity 0.3s ease, transform 0.3s ease;
    }

    .modal-hide {
        opacity: 0;
        transform: scale(0.9);
        transition: opacity 0.3s ease, transform 0.3s ease;
    }

    .modal-dialog-centered {
        display: grid;
        align-items: center;
    }

    .modal-sm{
        width: 400px;
    }

    .modal-normal{
        width: 500px;
    }

    .modal-lg{
        width: 650px;
    }

    .modal-xl{
        width: 950px;
    }
</style>
