<template>
    <div class="mb-3">
        <h3 class="text-center">{{ props.title }}</h3>
        <p v-if="props.subtitle" class="text-center">{{ props.subtitle }}</p>
        <p v-if="props.dateRange && props.dateRange.length === 2" class="text-center">
            {{ formatDate(props.dateRange[0]) }} to {{ formatDate(props.dateRange[1]) }}
        </p>
    </div>
</template>

<script setup>
    import { formatDate } from '@/utils/utils.js'

    const props = defineProps({
        title: String,
        subtitle: String,
        dateRange: {
            type: Array,
            default: () => []
        }
    })
</script>
