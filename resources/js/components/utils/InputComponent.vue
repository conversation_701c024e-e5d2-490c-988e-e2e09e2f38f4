<template>
    <div class="mb-3 input-component">
        <label :for="inputId" class="centered">{{ label }}</label>
        <div>
            <div :class="['input-group', error && 'text-danger input-error-group']">
                <span v-if="iconClass != 'no'" :class="['input-group-text', error && 'border-red text-danger']">
                    <i :class="iconClass"></i>
                </span>
                <input
                    :type="props.type"
                    v-model="inputValue"
                    class="form-control"
                    :placeholder="props.placeholder"
                    :required="props.required"
                    :disabled="props.disabled"
                    :name="inputId"
                    :id="inputId"
                    v-if="props.type !== 'checkbox'"
                >

                <input
                    :type="props.type"
                    v-model="inputValue"
                    :required="props.required"
                    :disabled="props.disabled"
                    :name="inputId"
                    :id="inputId"
                    v-if="props.type === 'checkbox'"
                    class="form-check-input"
                />
            </div>
            <div v-if="error" class="text-danger pt-1">
                {{ error }}
            </div>
        </div>
    </div>
</template>

<script setup>
    import { ref, watch } from 'vue';
    import '@assets/css/input.css'

    const props = defineProps({
        label: {
            type: String,
            required: true
        },
        inputId: {
            type: String,
            default: () => 'input-' + Math.random().toString(36).substr(2, 9)
        },
        iconClass: {
            type: String,
            required: true,
            default: 'no'
        },
        error: {
            type: String,
            required: false
        },
        modelValue: [String, Boolean],
        required: {
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: 'text'
        },
        placeholder: {
            type: String,
            default: ''
        },
        disabled: {
            type: Boolean,
            default: false
        }
    });

    const inputValue = ref(props.modelValue)

    const emit = defineEmits(['update:modelValue']);

    watch(() => props.modelValue, (newValue) => {
        inputValue.value = newValue || '';
    });

    watch(inputValue, (newValue) => {
        emit('update:modelValue', newValue);
    }, { immediate: true });

</script>


<style scoped>
    .form-check-input{
        width: 3em;
        height: 3em;
        margin-top: 0;
    }
</style>
