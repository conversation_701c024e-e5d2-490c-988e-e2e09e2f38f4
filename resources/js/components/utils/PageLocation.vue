<template>
    <div class="custom-flex-container">
        <div class="custom-page-location">
            <div v-for="(item, index) in items" :key="item.id" class="custom-item">
                <Link :href="item.link" class="custom-link">
                    <p class="custom-text">{{ item.name }}</p>
                    <div v-if="index < items.length - 1" class="custom-arrow">
                        <i class="bx bx-right-arrow"></i>
                    </div>
                </Link>
            </div>
        </div>
    </div>

</template>

<script setup>
    import { Link } from '@inertiajs/vue3';

    const props = defineProps({
        items: Array
    });

    const items = props.items
</script>

<style scoped>
    .custom-flex-container {
        display: flex;
        align-items: center;
        max-width: 600px;
    }

    @media only screen and (max-width: 1300px){
        .custom-flex-container {
            max-width: 500px;
        }
    }

    @media only screen and (max-width: 1200px){
        .custom-flex-container {
            max-width: 300px;
        }
    }

    .custom-page-location {
        display: flex;
        align-items: center;
    }

    .custom-item {
        display: flex;
        align-items: center;
        color: #007bff;
    }

    .custom-link {
        display: flex;
        align-items: center;
        text-decoration: none;
        color: inherit;
    }

    .custom-text {
        margin-bottom: 0;
    }

    .custom-arrow {
        padding: 0.5rem;
        font-size: 22px;
        color: #007bff;
    }
</style>
