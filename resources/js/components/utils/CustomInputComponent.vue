<template>
    <div class="mb-3 input-component">
        <label :for="inputId" class="centered">{{ label }}</label>
        <div>
            <div :class="['input-group', error && 'text-danger input-error-group']">
                <span :class="['input-group-text', error && 'border-red text-danger']">
                <i :class="iconClass"></i>
                </span>
                <slot name="input"></slot>
            </div>
            <div v-if="error" class="text-danger pt-1">
                {{ error }}
            </div>
        </div>
    </div>
</template>

<script setup>
    import '@assets/css/input.css'

    const props = defineProps({
        label: {
            type: String,
            required: true
        },
        inputId: {
            type: String,
            default: () => 'input-' + Math.random().toString(36).substr(2, 9)
        },
        iconClass: {
            type: String,
            required: true
        },
        error: {
            type: String,
            required: false,
        },
        gap:{
            type: Number,
            default: 3
        }
    });
</script>
