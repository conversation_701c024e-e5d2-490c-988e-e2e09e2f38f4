<template>
    <button type="submit" :class="btnClass" :style="{ minWidth: width ? width + 'px' : 'auto' }" :disabled="processing">
        <span v-if="processing">
            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
            Saving
        </span>
        <span v-else>{{ name }}</span>
    </button>
</template>

<script setup>
    import { ref, watch, computed } from 'vue';

    const props = defineProps({
        class: {
            type: String,
            default: "primary"
        },
        name: {
            type: String,
            default: "Update"
        },
        processing: {
            type: Boolean,
            required: false
        },
        width: {
            type: String,
            default: "0"
        }
    });

    const processing = ref(props.processing);
    const btnClass = computed(() => "btn btn-" + props.class);

    watch(() => props.processing, (newValue) => {
        processing.value = newValue;
    }, { immediate: true });
</script>
