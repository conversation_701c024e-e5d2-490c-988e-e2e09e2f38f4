<template>
    <div class="card reactiveReportComponent">
        <div class="card-body">
            <div v-if="props.title">
                <h4 class="text-center">{{ props.title }}</h4>
                <hr>
            </div>
            <table :id="tableId" class="table table-bordered datatable table-hover no-footer" width="100%">
                <thead role="row">
                    <tr class="table-header">
                        <th>Name</th>
                        <th>Value</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="label in labels">
                        <td>{{ label.label }}</td>
                        <td>{{ data[label.data] }}</td>
                    </tr>
                </tbody>
            </table>
            <div class="dataTables_processing card" v-if="loading">Processing...</div>
        </div>
    </div>
</template>

<script setup>
    import { onMounted, ref, watch } from 'vue';

    const props = defineProps({
        data: Array,
        labels: Array,
        loading: <PERSON><PERSON><PERSON>,
        title: {
            Type: String
        }
    });


    const tableId = ref(`data-table-${Math.random().toString(36).substr(2, 9)}`);

    onMounted(() => {
        const tableInstance = $(`#${tableId.value}`).DataTable({
            buttons: ['copy', 'excel', 'print'],
            columnDefs: [
                { orderable: false, targets: 1 },
            ],
            initComplete: function() {
                this.api().buttons().container().appendTo(`#${tableId.value}_wrapper .col-md-6:eq(0)`);
            },
        });
    });
</script>

<style scoped>
    .reactiveReportComponent table{
        position: relative;
    }

    .dataTables_processing{
        position: absolute;
        height: auto;
        width: 140px;
        padding: 10px 20px;
        text-align: center;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
    }
</style>
