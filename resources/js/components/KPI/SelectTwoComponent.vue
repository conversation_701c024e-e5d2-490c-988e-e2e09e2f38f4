<template>
    <head>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" />
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />
    </head>
    <select ref="select2" class="form-select" data-placeholder="Choose an option" style="width: 250px;" :multiple="props.multiple" :name="inputId" :id="inputId">
        <option
            v-for="item in data"
            :key="item"
            :value="item"
            >
            {{ formatDate(item) }}
        </option>
    </select>
</template>

<script setup>
    import { ref, onMounted, watch } from 'vue';
    import { formatDate } from '@/utils/utils.js'

    const props = defineProps({
        modelValue: {
            type: [String, Array],
            default: () => []
        },
        data: {
            type: Array,
            required: true
        },
        multiple: {
            type: Boolean,
            default: false
        },
        inputId: {
            type: String,
            default: () => 'input-' + Math.random().toString(36).substr(2, 9)
        },
    });

    const emit = defineEmits(['update:modelValue']);
    const select2 = ref(null);

    onMounted(() => {
        $(select2.value).select2({
            theme: "bootstrap-5",
            width: 'resolve',
            placeholder: $(select2.value).data('placeholder'),
        }).val(props.modelValue).trigger('change');

        $(select2.value).on('change', function () {
            emit('update:modelValue', $(this).val());
        });
    });
</script>
