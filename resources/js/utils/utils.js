export function asDollars(value) {
    const numberValue = Number(value);
    if (isNaN(numberValue)) {
        return '$0.00';
    }
    return numberValue.toLocaleString("en-US", { style: "currency", currency: "USD" });
}

export function formatDate(dateString, withHours = false) {
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    if (dateString === '0000-00-00') return '';

    const date = new Date(dateString);
    const day = date.getDate();
    const month = months[date.getMonth()];
    const year = date.getFullYear();

    if (withHours) {
        let hours = date.getHours();
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const ampm = hours >= 12 ? 'PM' : 'AM';
        hours = hours % 12 || 12;

        return `${month}-${day}-${year} ${hours}:${minutes} ${ampm}`;
    }

    return `${month}-${day}-${year}`;
}



export function revertFormatDate(formattedDate) {
    const [month, day, year] = formattedDate.split('-');
    const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    const monthIndex = months.indexOf(month) + 1;
    const formattedMonth = String(monthIndex).padStart(2, '0');
    const formattedDay = String(day).padStart(2, '0');

    return `${year}-${formattedMonth}-${formattedDay}`;
}

export function formatDateBack(date){
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
};

export function getCurrentMonthRange(format = 'default') {
    const start = new Date();
    start.setDate(1);
    const end = new Date(start.getFullYear(), start.getMonth() + 1, 0);

    if (format === 'us') {
        return [formatDate(start), formatDate(end)];
    } else {
        return [formatDateBack(start), formatDateBack(end)];
    }
}

export function getToday(format = 'default') {
    const today = new Date();
    if (format === 'us') {
        return formatDate(today);
    } else {
        return formatDateBack(today);
    }
}

export function calculateElapsedTime(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);

    let years = end.getFullYear() - start.getFullYear();
    let months = end.getMonth() - start.getMonth();
    let days = end.getDate() - start.getDate();

    if (days < 0) {
        months -= 1;
        days += new Date(end.getFullYear(), end.getMonth(), 0).getDate();
    }
    if (months < 0) {
        years -= 1;
        months += 12;
    }

    let result = '';
    if (years > 0) result += `${years} years`;
    if (months > 0) result += `${result ? ', ' : ''}${months} months`;
    if (days > 0) result += `${result ? ', ' : ''}${days} days`;

    return result || '0 days';
}

export function formatPhoneNumber(phoneNumber) {
    phoneNumber = phoneNumber.replace(/[^\d]/g, '');

    if (phoneNumber.length === 10) {
        return `(${phoneNumber.substring(0, 3)}) ${phoneNumber.substring(3, 6)}-${phoneNumber.substring(6)}`;
    } else if (phoneNumber.length === 11 && phoneNumber.startsWith('1')) {
        return `+1 (${phoneNumber.substring(1, 4)}) ${phoneNumber.substring(4, 7)}-${phoneNumber.substring(7)}`;
    } else {
        return phoneNumber;
    }
}

export function capitalizeWords(str) {
    if (typeof str !== 'string') {
        return str;
    }

    return str
        .toLowerCase()
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
}
