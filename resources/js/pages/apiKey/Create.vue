<template>
    <head>
        <title>ShopBoss - New Api Key</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="props.companyDetails.companyName + ' New API Key'"/>
        <div class="d-flex justify-content-between align-items-end mb-3 main-filters-flexed">
            <PageLocation :items="pageLocation"/>
            <ChooseShop :name="'Shop'" :items="props.companies" :active="props.shopid" @update:active="shopUpdate"/>
        </div>


        <div class="card">
            <div class="card-body">
                <h4 class="text-center">New API</h4>
                <hr>
                <div class="custom-grid mt-4">
                    <InputComponent
                        label="Shop ID"
                        iconClass="bx bx-store"
                        v-model="props.shopid"
                        placeholder="Shop ID"
                        disabled
                    />

                    <Select2Component
                        :data="props.apiCompanies"
                        v-model="api.name"
                        label="Choose API"
                        iconClass="bx bx-key"
                        :error="api.errors.name"
                    />

                    <CustomInputComponent label="Instructions" iconClass="bx bx-book">
                        <template #input>
                            <input type="text"  class="form-control" :value="currentInstructions" disabled>
                            <button class="btn btn-primary" @click="openInstuctions" v-if="isValidUrl(currentInstructions)">Open</button>
                        </template>
                    </CustomInputComponent>

                    <InputComponent
                        label="Support Agent"
                        iconClass="bx bx-user"
                        v-model="api.agent"
                        :error="api.errors.agent"
                        placeholder="Support Agent"
                        disabled
                    />

                    <InputComponent
                        label="New Api Integration"
                        iconClass="bx bx-key"
                        v-model="api.key"
                        :error="api.errors.key"
                        placeholder="New Api Integration"
                        :required="true"
                    />

                    <InputComponent
                        v-if="(api.name == 'birdeye' || api.name == 'podium')"
                        label="Api Key Provided"
                        iconClass="bx bx-key"
                        v-model="api.providedKey"
                        :error="api.errors.providedKey"
                        placeholder="Api Key Provided"
                        :required="true"
                    />

                    <InputComponent
                        v-if="(api.name == 'birdeye' || api.name == 'podium')"
                        label="Location / Business ID"
                        iconClass="bx bx-key"
                        v-model="api.businessId"
                        :error="api.errors.businessId"
                        placeholder="Location / Business ID"
                        :required="true"
                    />
                </div>

                <div class="d-flex align-items-center justify-content-center gap-2 mt-2">
                    <button type="button" class="btn btn-secondary" @click="addKey('Create API Key')" v-if="!apiButtonsShare.includes(api.name)">
                        <span v-if="api.processing && api.type == 'Create API Key'">
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            Saving
                        </span>
                        <span>Create API Key</span>
                    </button>
                    <button type="button" class="btn btn-primary" @click="addKey('Add New API Key')" v-if="!apiButtonsShare.includes(api.name)">
                        <span v-if="api.processing && api.type == 'Add New API Key'">
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            Saving
                        </span>
                        <span>Add New API Key</span>
                    </button>

                    <button type="button" class="btn btn-primary" @click="addAutoServ1" v-if="api.name == 'autoserv1'">
                        <span v-if="api.processing">
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            Saving
                        </span>
                        <span>Add Autoserv1</span>
                    </button>

                    <button type="button" class="btn btn-primary" @click="addKey('Birdeye Add')" v-if="api.name == 'birdeye'">
                        <span v-if="api.processing && api.type == 'Birdeye Add'">
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            Saving
                        </span>
                        <span>Add</span>
                    </button>

                    <button type="button" class="btn btn-primary" @click="addKey('Broadly Add')" v-if="api.name == 'broadly'">
                        <span v-if="api.processing">
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            Saving
                        </span>
                        <span>Add Broadly</span>
                    </button>

                    <button type="button" class="btn btn-primary" @click="addKey('Podium Add')" v-if="api.name == 'podium'">
                        <span v-if="api.processing">
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            Saving
                        </span>
                        <span>Add Podium</span>
                    </button>

                    <button type="button" class="btn btn-primary" @click="addKey('RepairShopSolutions Add')" v-if="api.name == 'repairshopsolutions'">
                        <span v-if="api.processing">
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            Saving
                        </span>
                        <span>Add RSS</span>
                    </button>
                </div>
            </div>
        </div>
        <ModalsComponent />
    </DashboardLayout>

    <BaseAlertComponent v-if="shopStore.state.apiKeyUpdated"
        :messageType="$page.props.flash.message ? 'success' : 'error'"
        :title="'API Key'"
        :message="$page.props.flash.message || $page.props.flash.error"
    />
</template>

<script setup>
    import { usePage, router, useForm } from '@inertiajs/vue3';
    import { watch, ref } from 'vue';

    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import ModalsComponent from '@/components/shop/ModalsComponent.vue';
    import ChooseShop from '@/components/shop/ChooseShop.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import InputComponent from '@/components/utils/InputComponent.vue';
    import CustomInputComponent from '@/components/utils/CustomInputComponent.vue';
    import Select2Component from '@/components/utils/Select2Component.vue'
    import BaseAlertComponent from '@/components/alerts/BaseAlertComponent.vue';
    import { useShopStore } from '@/stores/shop/shopStore'

    const shopStore = useShopStore()

    const { props } = usePage();

    const pageLocation = [
        {
            name: 'API Keys',
            link: route('api-key.index', props.shopid)
        },
        {
            name: props.companyDetails.companyName + ' API Keys',
            link: route('shop.show', props.shopid)
        }
    ]

    function shopUpdate(shopid) {
        router.get(route('api-key.create', shopid));
    }

    const api = useForm({
        type: "Create API Key",
        shopid: props.shopid,
        name: "apination",
        instructions: "",
        agent: props.auth.user.email,
        key: '',
        providedKey: '',
        businessId: ''
    })

    const apiButtonsShare = [
        'autoserv1',
        'repairshopsolutions'
    ]

    const instructions = {
        autoserv1: 'An APIKey from Autoserv1 is required.',
        podium: 'https://shopboss.atlassian.net/wiki/spaces/SOP/pages/*********/APIs',
        repairshopsolutions: 'Please enter the API key provided by RSS to proceed.'
    }

    const currentInstructions = ref("https://shopboss.atlassian.net/wiki/spaces/SOP/pages/*********/APIs")

    const openInstuctions = () => {
        window.open(currentInstructions.value, '_blank');
    }

    watch(() => api.name, (newValue) => {
        if ( apiButtonsShare.includes(newValue) ){
            currentInstructions.value = instructions[newValue]
        }
    });

    const isValidUrl = (url) => {
        try {
            new URL(url);
            return true;
        } catch (_) {
            return false;
        }
    }

    const addAutoServ1 = () => {
        api.post(route('api-key.autoserv1', {'shop': props.shopid}), {
            api,
            preserveState: false,
            onSuccess: () => {
                shopStore.setState('apiKeyUpdated', 'activate')
            }
        })
    }

    const addKey = (type) => {
        api.type = type ? type : "Create API Key"

        api.post(route('api-key.store', {'shop': props.shopid}), {
            api,
            preserveState: false,
            onSuccess: () => {
                shopStore.setState('apiKeyUpdated', 'activate')
            }
        })
    }
</script>

<style scoped>

    .form-select{
        text-transform: capitalize;
    }
    .custom-grid {
        display: grid;
        grid-gap: 20px;
        grid-template-columns: 1fr 1fr;
    }

    @media only screen and (max-width: 1800px) {
        .custom-grid {
            grid-template-columns: 1fr;
        }
    }

    @media screen and (max-width: 650px) {
        .main-filters-flexed {
            display: block !important;
        }
    }
</style>
