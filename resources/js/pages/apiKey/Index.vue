<template>
    <head>
        <title>ShopBoss - Api Key</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="props.companyDetails.companyName + ' API Keys'"/>
        <div class="d-flex justify-content-between align-items-end mb-3 main-filters-flexed">
            <PageLocation :items="pageLocation"/>
            <div class="d-flex gap-2 filters-flexed">
                <ChooseShop :name="'Shop'" :items="props.companies" :active="props.shopid" @update:active="shopUpdate"/>
                <div class="new-api">
                    <Link :href="route('api-key.create', props.shopid)" class="btn btn-primary">New API</Link>
                </div>
            </div>
        </div>

        <APIKeyTableComponent :columns="columns" :route="route('api-key.datatable', props.shopid)" :shop="props.shopid"/>

        <ModalsComponent />
    </DashboardLayout>
</template>

<script setup>
    import { ref, onMounted } from 'vue';
    import { usePage, router, Link } from '@inertiajs/vue3';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import APIKeyTableComponent from '@/components/apiKey/APIKeyTableComponent.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import ModalsComponent from '@/components/shop/ModalsComponent.vue';
    import ChooseShop from '@/components/shop/ChooseShop.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';

    const { props } = usePage();

    const pageLocation = [
        {
            name: 'Shop Detail',
            link: route('shop.index')
        },
        {
            name: props.companyDetails.companyName + ' API Keys',
            link: route('shop.show', props.shopid)
        }
    ]

    const columns = ref(
        [
            {data: 'id', name: 'id', label: 'ID'},
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'companyname', name: 'companyname', label: 'Company'},
            {data: 'username', name: 'username', label: 'Username'},
            {data: 'password', name: 'password', label: 'Password'},
            {data: 'apikey', name: 'apikey', label: 'Key'},
        ]
    );

    function shopUpdate(shopid) {
        const url = `/dashboard/shop/${shopid}/api-key`;
        router.get(url);
    }
</script>

<style scoped>
    @media screen and (max-width: 800px) {
        .main-filters-flexed,
        .filters-flexed {
            display: block !important;
        }

        .filters-flexed .new-api{
            display: grid;
            align-items: end;
            justify-content: end;
            margin-top: 10px;
        }
    }

    @media screen and (max-width: 500px) {
        .filters-flexed .new-api{
            display: grid;
            justify-content: initial;
            margin-top: 10px;
        }
    }
</style>
