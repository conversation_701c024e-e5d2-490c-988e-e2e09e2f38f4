<template>
    <head>
        <title>ShopBoss - Reset Password</title>
        <meta name="description" content="Log in to ShopBoss">
    </head>
    <div class="wrapper">
        <div class="section-authentication-cover">
            <div class="">
                <div class="row g-0">
                    <div class="col-12 col-xl-7 col-xxl-8 auth-cover-left align-items-center justify-content-center d-none d-xl-flex">
                        <div class="card shadow-none bg-transparent shadow-none rounded-0 mb-0">
                            <div class="card-body">
                                <img src="@/../assets/images/sh_logo_custom.png" class="img-fluid" width="600" alt=""/>
                            </div>
                        </div>
                    </div>
                    <div class="col-12 col-xl-5 col-xxl-4 auth-cover-right align-items-center justify-content-center">
                        <div class="card rounded-0 m-3 shadow-none bg-transparent mb-0">
                            <div class="card-body p-sm-5">
                                <div class="p-3">
                                    <div class="text-center">
                                        <img src="/assets/images/forgot.png" width="100" alt="" />
                                    </div>
                                    <h4 class="mt-5 font-weight-bold">Reset Password</h4>
                                    <p class="text-muted">Update account password</p>
                                    <form @submit.prevent="sendResetLink">
                                        <div :class="['input-group mb-3', forgotPassword.errors.email && 'text-danger input-error-group']">
                                            <label for="inputEmailAddress" class="form-label">Email</label>
                                            <div class="input-group">
                                                <span :class="['input-group-text', forgotPassword.errors.email && 'border-red text-danger']">
                                                    <i class="bx bx-envelope"></i>
                                                </span>
                                                <input type="email" v-model="forgotPassword.email" class="form-control border-red" id="inputEmailAddress" placeholder="<EMAIL>">
                                            </div>
                                            <div v-if="forgotPassword.errors.email" class="text-danger pt-1">
                                                {{ forgotPassword.errors.email }}
                                            </div>
                                        </div>

                                        <div :class="['input-group mb-3', forgotPassword.errors.password && 'text-danger input-error-group']">
                                            <label for="inputEmailAddress" class="form-label">Password</label>
                                            <div class="input-group">
                                                <span :class="['input-group-text', forgotPassword.errors.password && 'border-red text-danger']">
                                                    <i class="bx bx-key"></i>
                                                </span>
                                                <input :type="showPassword ? 'text' : 'password'" v-model="forgotPassword.password" class="form-control border-end-0" id="inputChoosePassword" placeholder="Enter Password">
                                                <button @click="togglePasswordVisibility" class="input-group-text bg-transparent" type="button">
                                                    <i :class="showPassword ? 'bx bx-show' : 'bx bx-hide'"></i>
                                                </button>
                                            </div>
                                            <div v-if="forgotPassword.errors.password" class="text-danger pt-1">
                                                {{ forgotPassword.errors.password }}
                                            </div>
                                        </div>

                                        <div :class="['input-group mb-3', forgotPassword.errors.password_confirmation && 'text-danger input-error-group']">
                                            <label for="inputEmailAddress" class="form-label">Confirm Password</label>
                                            <div class="input-group">
                                                <span :class="['input-group-text', forgotPassword.errors.password_confirmation && 'border-red text-danger']">
                                                    <i class="bx bx-key"></i>
                                                </span>
                                                <input :type="showPassword ? 'text' : 'password'" v-model="forgotPassword.password_confirmation" class="form-control border-end-0" id="inputChoosePassword" placeholder="Enter Password">
                                                <button @click="togglePasswordVisibility" class="input-group-text bg-transparent" type="button">
                                                    <i :class="showPassword ? 'bx bx-show' : 'bx bx-hide'"></i>
                                                </button>                                            </div>
                                            <div v-if="forgotPassword.errors.password_confirmation" class="text-danger pt-1">
                                                {{ forgotPassword.errors.password_confirmation }}
                                            </div>
                                        </div>


                                        <div class="d-grid gap-2">
                                            <button type="submit" class="btn btn-primary">Send</button>
                                            <Link :href="route('login')" class="btn btn-light"><i class='bx bx-arrow-back me-1'></i>Back to Login</Link>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
    import { Link, useForm } from '@inertiajs/vue3';
    import { ref } from 'vue';

    const props = defineProps({
        token: String,
        email: String
    })

    const showPassword = ref(false)

    function togglePasswordVisibility() {
        showPassword.value = !showPassword.value
    }

    const forgotPassword = useForm({
        token: props.token,
        email: props.email,
        password: '',
        password_confirmation: ''
    });

    const sendResetLink = () => {
        forgotPassword.post(route('password.update'));
    };
</script>

<style>
    .input-error-group .border-red{
        border-color: red;
    }

    .input-error-group .form-control,
    .input-error-group .form-select,
    .input-error-group .input-group-text{
        border-color: red;
    }
</style>
