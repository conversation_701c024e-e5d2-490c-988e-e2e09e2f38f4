<template>
    <head>
        <title>ShopBoss - Login</title>
        <meta name="description" content="Log in to ShopBoss">
    </head>
    <div>
        <div class="wrapper">
            <div class="section-authentication-cover">
                <div class="">
                    <div class="row g-0">
                        <div class="col-12 col-xl-7 col-xxl-8 auth-cover-left align-items-center justify-content-center d-none d-xl-flex">
                            <div class="card shadow-none bg-transparent shadow-none rounded-0 mb-0">
                                <div class="card-body">
                                    <img src="@/../assets/images/sh_logo_custom.png" class="img-fluid auth-img-cover-login" width="650" alt=""/>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-xl-5 col-xxl-4 auth-cover-right align-items-center justify-content-center">
                            <div class="card rounded-0 m-3 shadow-none bg-transparent mb-0">
                                <div class="card-body p-sm-5">
                                    <div class="">
                                        <div class="mb-3 text-center">
                                            <img src="/assets/images/logo-icon.png" width="60" alt="">
                                        </div>
                                        <div class="text-center mb-4">
                                            <h5 style="color: #212529;">ShopBoss Admin</h5>
                                            <p class="mb-0">Please log in to your account</p>
                                        </div>
                                        <div class="text-danger text-xs" v-if="props.errors.email">{{ props.errors.email }}</div>
                                        <form @submit.prevent="login" autocomplete="on">
                                            <div class="form-body">
                                                <div class="row g-3">
                                                    <div class="col-12">
                                                        <label for="inputEmailAddress" class="form-label">Email</label>
                                                        <div class="input-group">
                                                            <span class="input-group-text">
                                                                <i class="bx bx-envelope"></i>
                                                            </span>
                                                            <input type="email"
                                                                v-model="loginForm.email"
                                                                class="form-control"
                                                                id="inputEmailAddress"
                                                                placeholder="<EMAIL>"
                                                                autocomplete="username"
                                                                required>
                                                        </div>
                                                    </div>
                                                    <div class="col-12">
                                                        <label for="inputChoosePassword" class="form-label">Password</label>
                                                        <div class="input-group">
                                                            <span class="input-group-text">
                                                                <i class="bx bx-key"></i>
                                                            </span>
                                                            <input :type="showPassword ? 'text' : 'password'"
                                                                v-model="loginForm.password"
                                                                class="form-control border-end-0"
                                                                id="inputChoosePassword"
                                                                placeholder="Enter Password"
                                                                autocomplete="current-password"
                                                                required>
                                                            <button @click="togglePasswordVisibility"
                                                                    class="input-group-text bg-transparent"
                                                                    type="button">
                                                                <i :class="showPassword ? 'bx bx-show' : 'bx bx-hide'"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-check form-switch">
                                                            <input class="form-check-input"
                                                                type="checkbox"
                                                                id="flexSwitchCheckChecked"
                                                                v-model="loginForm.remember">
                                                            <label class="form-check-label"
                                                                for="flexSwitchCheckChecked">
                                                                Remember Me
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6 text-end">
                                                        <Link :href="route('forgot-password')">Forgot Password ?</Link>
                                                    </div>
                                                    <div class="col-12">
                                                        <div class="d-grid">
                                                            <button type="submit" class="btn btn-primary">Sign in</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <BaseAlertComponent v-if="props.flash.message"
        :messageType="'success'"
        :title="'Password'"
        :message="props.flash.message"
    />
</template>

<script setup lang="ts">
    import { Link, router } from '@inertiajs/vue3'
    import { reactive, ref, onMounted } from 'vue'
    import BaseAlertComponent from '../../components/alerts/BaseAlertComponent.vue';
    import CryptoJS from 'crypto-js';

    interface Props {
        errors: {
            email?: string
        },
        status?: string,
        flash: {
            message?: string
        }
    }

    const props = defineProps<Props>();

    const loginForm = reactive({
        email: "",
        password: "",
        remember: false
    })

    const showPassword = ref(false)

    function togglePasswordVisibility() {
        showPassword.value = !showPassword.value
    }

    function login() {
        const encryptedPassword = CryptoJS.AES.encrypt(loginForm.password, 'Zt"4Sg.p_2}=/Az6').toString();

        router.post('login', loginForm, {
        onSuccess: () => {
            if (loginForm.remember) {
                localStorage.setItem('email', loginForm.email);
                localStorage.setItem('password', encryptedPassword);
                localStorage.setItem('remember', loginForm.remember.toString());
            }else {
                localStorage.removeItem('email');
                localStorage.removeItem('password');
                localStorage.removeItem('remember');
            }
        }
    });
    }

    onMounted(() => {
        if (localStorage.getItem('remember') === 'true') {
            loginForm.email = localStorage.getItem('email') || '';
            const password = localStorage.getItem('password') || '';
            if (password) {
                const bytes = CryptoJS.AES.decrypt(password, 'Zt"4Sg.p_2}=/Az6');
                const decryptedPassword = bytes.toString(CryptoJS.enc.Utf8);
                loginForm.password = decryptedPassword
            }
            loginForm.remember = true;
        }
    });
</script>
