<template>
    <head>
        <title>ShopBoss - Dashboard</title>
    </head>

    <DashboardLayout>
      <div class="shadow-none bg-transparent">
        <div class="card-header py-3">
          <div class="row align-items-center">
            <div class="col-md-2">
              <h4 class="mb-3 mb-md-0">Dashboard</h4>
            </div>
            <div class="col-md-10">
              <form class="float-end">
                <div style="width: 250px;">
                    <div class="d-flex align-items-center">
                        <div class="input-group">
                        <select class="form-select" id="dateRange" v-model="selectedOption" @change="updateDate">
                            <option value="today">Today</option>
                            <option value="this_week">This Week</option>
                            <option value="this_month">This Month</option>
                        </select>
                        </div>
                    </div>
                </div>
              </form>
            </div>
          </div>
        </div>
        <div>
            <LineChartComponent
                class="mt-4"
                :data="roDetails.totalMovementsData"
                :labels="roDetails.totalMovementsLabel"
                :title="'RO Total Prices'"
                :subtitle="'Price Movements'"
                :total="roDetails.totalMovementsTotal"
                :increase="roDetails.totalMovementsIncrease"
                :loading="loadingStates.roDetails"
            />
            <div class="row row-cols">
                <div class="col-sm-12 col-md-6 col-lg-4">
                    <BarChartComponent
                        :data="roDetails.totalCountMovementsData"
                        :title="'Count Movements'"
                        :subtitle="'RO Count'"
                        :total="roDetails.totalCountMovementsIncrease"
                        :increase="roDetails.totalCountMovementsTotal"
                        :loading="loadingStates.roDetails"
                    />
                </div>
                <div class="col-sm-12 col-md-6 col-lg-4">
                    <BarChartComponent
                        :data="ssShops.data"
                        :title="'Shops Movements'"
                        :subtitle="'SS Shops'"
                        :total="ssShops.total"
                        :increase="ssShops.increase"
                        :loading="loadingStates.ssShops"
                    />
                </div>
                <div class="col-sm-12 col-md-6 col-lg-4">
                    <BarChartComponent
                        :data="msShops.data"
                        :title="'Shops Movements'"
                        :subtitle="'MS Shops'"
                        :total="msShops.total"
                        :increase="msShops.increase"
                        :loading="loadingStates.msShops"
                    />
                </div>
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <StaticWidget1
                        :total="bossPay.total"
                        :title="'Boss Pay'"
                        :increase="bossPay.increase"
                        :loading="loadingStates.bossPay"
                    />
                </div>
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <StaticWidget1
                        :total="aro.total"
                        :title="'ARO'"
                        :increase="aro.increase"
                        :dollarFormat="true"
                        :loading="loadingStates.aro"
                    />
                </div>
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <StaticWidget1
                        :total="accounts.silver.total"
                        :title="'Silver Accouts'"
                        :increase="accounts.silver.increase"
                        :loading="loadingStates.accounts"
                    />
                </div>
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <StaticWidget1
                        :total="accounts.gold.total"
                        :title="'Gold Accounts'"
                        :increase="accounts.gold.increase"
                        :loading="loadingStates.accounts"
                    />
                </div>
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <StaticWidget1
                        :total="accounts.platinum.total"
                        :title="'Platinum Accounts'"
                        :increase="accounts.platinum.increase"
                        :loading="loadingStates.accounts"
                    />
                </div>
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <StaticWidget1
                        :total="accounts.premier.total"
                        :title="'Premier Accounts'"
                        :increase="accounts.premier.increase"
                        :loading="loadingStates.accounts"
                    />
                </div>
                <div class="col-sm-12 col-md-6 col-lg-3">
                    <StaticWidget1
                        :total="accounts.premierPlus.total"
                        :title="'Premier Plus Accounts'"
                        :increase="accounts.premierPlus.increase"
                        :loading="loadingStates.accounts"
                    />
                </div>
            </div>
        </div>
      </div>
    </DashboardLayout>
</template>

<script setup>
    import { ref, onMounted } from 'vue';

    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import LineChartComponent from '@/components/charts/LineChartComponent.vue';
    import BarChartComponent from '@/components/charts/BarChartComponent.vue';
    import StaticWidget1 from '@/components/charts/StaticWidget1.vue';

    const selectedOption = ref('this_week');
    const startDate = ref('');
    const endDate = ref('');

    const updateDate = () => {
        const today = new Date();
        if (selectedOption.value === 'today') {
            startDate.value = today.toISOString().slice(0, 10);
            endDate.value = today.toISOString().slice(0, 10);
        } else if (selectedOption.value === 'this_week') {
            const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()));
            const endOfWeek = new Date(today.setDate(today.getDate() - today.getDay() + 6));

            startDate.value = startOfWeek.toISOString().slice(0, 10);
            endDate.value = endOfWeek.toISOString().slice(0, 10);
        } else if (selectedOption.value === 'this_month') {
            const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);

            startDate.value = endOfMonth.toISOString().slice(0, 8) + "01";
            endDate.value = endOfMonth.toISOString().slice(0, 10);
        }

        getData()
    }

    const getData = async () => {
        await Promise.all([
            getRepairOrderChartDetails(),
            getAccountsChartDetails(),
            getSSShopsChartDetails(),
            getMSShopsChartDetails(),
            getAROChartDetails(),
            getBossPayChartDetails()
        ]);
    }

    const roDetails = ref({
        totalMovementsData: [],
        totalMovementsLabel: [],
        totalMovementsIncrease: null,
        totalMovementsTotal: null,
        totalCountMovementsData: [],
        totalCountMovementsIncrease: null,
        totalCountMovementsTotal: null,
    });

    const loadingStates = ref({
        roDetails: true,
        ssShops: true,
        msShops: true,
        accounts: true,
        aro: true,
        bossPay: true
    });

    const getRepairOrderChartDetails = async () => {
        try {
            loadingStates.value.roDetails = true;
            const res = await axios.get(route('chart.details.ro'), {
                params: {
                    start_date: startDate.value,
                    end_date: endDate.value
                }
            });
            const data = res.data;

            roDetails.value.totalMovementsData = data.ro_data.map(item => item.ammount);
            roDetails.value.totalCountMovementsData = data.ro_data.map(item => item.count);

            roDetails.value.totalMovementsLabel = data.ro_data.map(item => {
                const date = new Date(item.date);
                return `${date.getDate()}-${date.toLocaleString('default', { month: 'short' })}`;
            });

            roDetails.value.totalMovementsIncrease = data.ro_status.ammount;
            roDetails.value.totalMovementsTotal = data.ro_totals.ammount;

            roDetails.value.totalCountMovementsIncrease = data.ro_totals.count;
            roDetails.value.totalCountMovementsTotal = data.ro_status.count;
        } catch (errors) {
            console.error(errors);
        } finally {
            loadingStates.value.roDetails = false;
        }
    }


    const accounts = ref({
        silver: {
            total: null,
            increase: null
        },
        gold: {
            total: null,
            increase: null
        },
        platinum: {
            total: null,
            increase: null
        },
        premier: {
            total: null,
            increase: null
        },
        premierPlus: {
            total: null,
            increase: null
        }
    });

    const getAccountsChartDetails = async () => {
        try {
            loadingStates.value.accounts = true;
            const res = await axios.get(route('chart.details.accounts'), {
                params: {
                    start_date: startDate.value,
                    end_date: endDate.value
                }
            });
            const data = res.data;

            accounts.value.silver.total = data.silver.total;
            accounts.value.silver.increase = data.silver.increase;

            accounts.value.gold.total = data.gold.total;
            accounts.value.gold.increase = data.gold.increase;

            accounts.value.platinum.total = data.platinum.total;
            accounts.value.platinum.increase = data.platinum.increase;

            accounts.value.premier.total = data.premier.total;
            accounts.value.premier.increase = data.premier.increase;

            accounts.value.premierPlus.total = data.premierPlus.total;
            accounts.value.premierPlus.increase = data.premierPlus.increase;
        } catch (errors) {
            console.error(errors);
        } finally {
            loadingStates.value.accounts = false;
        }
    }

    const ssShops = ref({
        data : [],
        increase : 0,
        total: 0
    });

    const getSSShopsChartDetails = async () => {
        try {
            loadingStates.value.ssShops = true;
            const res = await axios.get(route('chart.details.ss-shops'), {
                params: {
                    start_date: startDate.value,
                    end_date: endDate.value
                }
            });
            const data = res.data;

            ssShops.value.data = data.shops.map(item => item.count);
            ssShops.value.total = data.shops.reduce((accumulator, item) => accumulator + item.count, 0);
            ssShops.value.increase = data.previous_shops.reduce((accumulator, item) => accumulator + item.count, 0);
        } catch (errors) {
            console.error(errors);
        } finally {
            loadingStates.value.ssShops = false;
        }
    }

    const msShops = ref({
        data : [],
        increase : 0,
        total: 0
    });

    const getMSShopsChartDetails = async () => {
        try {
            loadingStates.value.msShops = true;
            const res = await axios.get(route('chart.details.ms-shops'), {
                params: {
                    start_date: startDate.value,
                    end_date: endDate.value
                }
            });
            const data = res.data;

            msShops.value.data = data.shops.map(item => item.count);
            msShops.value.total = data.shops.reduce((accumulator, item) => accumulator + item.count, 0);
            msShops.value.increase = data.previous_shops.reduce((accumulator, item) => accumulator + item.count, 0);
        } catch (errors) {
            console.error(errors);
        } finally {
            loadingStates.value.msShops = false;
        }
    }

    const aro = ref({
        total: null,
        increase: null
    });

    const getAROChartDetails = async () => {
        try {
            loadingStates.value.aro = true;
            const res = await axios.get(route('chart.details.aro'), {
                params: {
                    start_date: startDate.value,
                    end_date: endDate.value
                }
            });
            const data = res.data;

            aro.value.total = data.aro;
            aro.value.increase = data.aro - data.previous_aro;
        } catch (errors) {
            console.error(errors);
        } finally {
            loadingStates.value.aro = false;
        }
    }

    const bossPay = ref({
        total: 0,
        increase: 0
    });

    const getBossPayChartDetails = async () => {
        try {
            loadingStates.value.bossPay = true;
            const res = await axios.get(route('chart.details.boss-pay'), {
                params: {
                    start_date: startDate.value,
                    end_date: endDate.value
                }
            });
            const data = res.data;

            bossPay.value.total = data.boss_pay;
            bossPay.value.increase = data.boss_pay - data.previous_boss_pay;
        } catch (errors) {
            console.error(errors);
        } finally {
            loadingStates.value.bossPay = false;
        }
    }

    onMounted(() => {
        updateDate();
    });
</script>



