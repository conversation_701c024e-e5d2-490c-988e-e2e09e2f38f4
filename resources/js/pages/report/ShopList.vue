<template>
    <head>
        <title>ShopBoss - {{ pageTitle }}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle"/>
        <div class="d-flex justify-content-between align-items-center mb-2 main-filters-flexed">
            <PageLocation :items="pageLocation"/>

            <div class="d-flex align-items-end filters-flexed">
                <div class="form-check pe-3" v-if="!shopIsMatco && userRole != 'customer support' && userRole != 'customer success'">
                    <Link class="custom-button" :href="route('billing.index')">Billing</Link>
                </div>

                <div class="form-check pe-3" v-if="!trials">
                    <input class="form-check-input" type="checkbox" v-model="suspended" id="suspended">
                    <label class="form-check-label" for="suspended">
                        Suspended
                    </label>
                </div>

                <div class="form-check pe-3" v-if="!shopIsMatco">
                    <input class="form-check-input" type="checkbox" v-model="matco" id="matco">
                    <label class="form-check-label" for="suspended">
                        Matco Shops
                    </label>
                </div>

                <div class="form-check pe-3" v-if="!suspended">
                    <input class="form-check-input" type="checkbox" v-model="trials" id="trials">
                    <label class="form-check-label" for="trials">
                        Trials
                    </label>
                </div>
            </div>
        </div>

        <ReportComponent :dRoute="computedRoute" :key="computedRoute"/>
    </DashboardLayout>
</template>

<script setup>
    import { ref, computed } from 'vue';
    import { Link, usePage } from '@inertiajs/vue3';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReportComponent from '@/components/shopList/ReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';

    const { props } = usePage();

    const pageTitle = 'Shop List Report';
    const trials = ref(false)
    const suspended = ref(false)
    const matco = ref(false)
    const shopIsMatco = props.auth.user.role === 'matco'
    const userRole = props.auth.user.role

    const pageLocation = [
        {
            name: 'Report',
            link: route('report.index')
        },
        {
            name: pageTitle,
            link: route('report.shoplist.index')
        }
    ]

    const computedRoute = computed(() => {
        return route('report.shoplist.datatable', {
            trials: trials.value || undefined,
            suspended: suspended.value || undefined,
            matco: shopIsMatco ? true : matco.value || undefined,
        });
    });
</script>

<style scoped>
    .custom-button{
        border: 1px solid #0d6efd;
        border-radius: 10px;
        padding: 5px;
        transition: .4s;
    }
    .custom-button:hover{
        background-color: #0d6efd;
        color: white;
    }

    @media screen and (max-width: 650px) {
        .main-filters-flexed {
            display: block !important;
        }
    }

    @media screen and (max-width: 450px) {
        .filters-flexed {
            display: block !important;
        }

        .filters-flexed div {
            margin-bottom: 10px;
        }
    }
</style>
