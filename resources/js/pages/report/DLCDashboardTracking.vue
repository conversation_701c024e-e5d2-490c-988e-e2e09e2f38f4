<template>
    <head>
        <title>ShopBoss - {{ pageTitle }}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle" :dateRange="reportDateRange" :key="computedRoute"/>

        <div class="d-flex justify-content-between align-items-center mb-2 dlc-filters">
            <PageLocation :items="pageLocation"/>

            <div class="filters-container d-flex">
                <div>
                    <label for="statuses">Status</label>
                    <select class="form-control form-select" v-model="status">
                        <option value="All">All</option>
                        <option v-for="option in props.statuses" :key="option" :value="option">{{ option }}</option>
                    </select>
                </div>

                <div class="date-picker-container">
                    <DatePicker :date="dateRange" @update:dateRange="updateDateRange"/>
                    <span class="delete-btn" @click="clearDateRange">
                        <i class="bx bx-x-circle"></i>
                    </span>
                </div>
            </div>
        </div>

        <ReactiveReportComponent :columns="columns" :route="computedRoute" :key="computedRoute" :print="false"/>
    </DashboardLayout>
</template>

<script setup>
    import { ref, computed, watch } from 'vue';
    import { usePage } from '@inertiajs/vue3';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import { getCurrentMonthRange, formatDate, formatPhoneNumber } from '@/utils/utils.js'
    import DatePicker from '@/components/utils/DatePicker.vue';
    import Select2Component from '@/components/utils/Select2Component.vue'

    const pageTitle = '10DLC Dashboard Tracking Report';

    const { props } = usePage();

    const pageLocation = [
        {
            name: 'Report',
            link: '/dashboard/report'
        },
        {
            name: pageTitle,
            link: '/dashboard/report/lro/churn'
        }
    ]
    const columns = ref(
        [
            {data: 'id', name: 'id', label: 'ID'},
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'status', name: 'status', label: 'Status'},
            {data: 'brandid', name: 'brandid', label: 'Brand ID'},
            {data: 'campaignid', name: 'campaignid', label: 'Campaign ID'},
            {data: 'companyname', name: 'companyname', label: 'Company Name'},
            {data: 'dba', name: 'dba', label: 'DBA'},
            {data: 'legaltype', name: 'legaltype', label: 'Legal Type'},
            {data: 'ein', name: 'ein', label: 'EIN',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.ein ? formatPhoneNumber(row.ein) : '';
                    }
                    return row.ein || '';
                }
            },
            {data: 'countryein', name: 'countryein', label: 'Country EIN'},
            {data: 'address', name: 'address', label: 'Address'},
            {data: 'city', name: 'city', label: 'City'},
            {data: 'state', name: 'state', label: 'State'},
            {data: 'zip', name: 'zip', label: 'ZIP Code'},
            {data: 'country', name: 'country', label: 'Country'},
            {data: 'website', name: 'website', label: 'Website'},
            {data: 'stocksymbol', name: 'stocksymbol', label: 'Stock Symbol'},
            {data: 'stockexchange', name: 'stockexchange', label: 'Stock Exchange'},
            {data: 'verticaltype', name: 'verticaltype', label: 'Vertical Type'},
            {data: 'refid', name: 'refid', label: 'Reference ID'},
            {data: 'firstname', name: 'firstname', label: 'First Name'},
            {data: 'lastname', name: 'lastname', label: 'Last Name'},
            {data: 'cellphone', name: 'cellphone', label: 'Cellphone',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.cellphone ? formatPhoneNumber(row.cellphone) : '';
                    }
                    return row.cellphone || '';
                }
            },
            {data: 'ts', name: 'ts', label: 'Timestamp',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.ts ? formatDate(row.ts, true) : '';
                    }
                    return row.ts || '';
                }
            }
        ]
    );

    const dateRange = ref();
    const reportDateRange = ref();
    const status = ref('All');

    watch(status, () => {
        computedRoute.value;
    });

    const updateDateRange = (newDateRange) => {
        dateRange.value = newDateRange.map(date => formatDate(date));
        reportDateRange.value = newDateRange;
    };

    const computedRoute = computed(() => {
        const [startDate, endDate] = reportDateRange.value || [];
        return route('dlc-dashboard-tracking.datatable', {start_date: startDate, end_date: endDate, status: status.value});
    });

    const clearDateRange = () => {
        dateRange.value = [];
        reportDateRange.value = [];
    };
</script>

<style scoped>
    td .multiline {
        white-space: pre-line;
    }

    th:nth-child(4),
    th:nth-child(5),
    th:nth-child(7),
    th:nth-child(11),
    th:nth-child(12),
    th:nth-child(23){
        min-width: 100px;
    }
    .btn.btn-outline-secondary.buttons-print{
        display: none;
    }
    .btn.btn-outline-secondary.buttons-excel.buttons-html5{
        border-top-right-radius: 7px;
        border-bottom-right-radius: 7px
    }

    .date-picker-container{
        display: flex;
        margin-left: 5px;
    }

    .dlc-filters .delete-btn{
        cursor: pointer;
        font-size: 24px;
        transform: translate(2px, 21px);
    }

    .dlc-filters .delete-btn:hover{
        color: red;
    }

    .dlc-filters {
        flex-wrap: wrap;
    }

    .filters-container {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: flex-start;
    }

    @media (max-width: 768px) {
        .filters-container {
            flex-direction: column;
            align-items: flex-start;
        }


        .date-picker-container{
            margin-left: 0;
        }

        .dlc-filters .delete-btn{
            transform: translate(2px, 25px);
        }
    }
</style>
