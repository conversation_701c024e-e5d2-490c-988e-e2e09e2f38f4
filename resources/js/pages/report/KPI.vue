<template>
    <head>
        <title>ShopBoss - {{pageTitle}}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle"/>

        <div class="d-flex justify-content-between align-items-center mb-2">
            <PageLocation :items="pageLocation"/>
            <div class="d-flex flex-row-reverse mb-3 gap-2">
                <DatePicker :date="dateRange" @update:dateRange="updateDateRange"/>
                <div>
                    Select Data Date
                    <Select2Component
                        v-model="dates.data_date"
                        :data="dataDate"
                        label="Select Data Date"
                        iconClass="bx bx-calendar"
                        keyID="dtime"
                    />
                </div>
            </div>
        </div>

        <KPITableComponent :data="mainKPI" :loading="true" :labels="mainKPILabels" title="Main KPI'S" />

        <KPITableComponent :data="standardKPI" :loading="standardKPILoading" :labels="standardKPILabels" title="Standard KPI'S" />

        <KPITableComponent :data="KPIAccounts.packages" :loading="KPIAccountsLoading" :labels="KPIPackagesLabels" title="KPI Packages" />

        <KPITableComponent :data="KPIAccounts.merchantAccounts" :loading="KPIAccountsLoading" :labels="KPIMerchantsLabels" title="KPI Merchant Accounts" />

    </DashboardLayout>
</template>

<script setup>
    import { ref, onMounted, watch } from 'vue';
    import { usePage } from '@inertiajs/vue3';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import KPITableComponent from '@/components/KPI/KPITableComponent.vue';
    import Select2Component from '@/components/KPI/SelectTwoComponent.vue';
    import { formatDate, revertFormatDate, getCurrentMonthRange } from '@/utils/utils.js'
    import DatePicker from '@/components/utils/DatePicker.vue';

    const pageTitle = 'KPI Report';

    const pageLocation = [
        {
            name: 'Report',
            link: route('report.index')
        },
        {
            name: pageTitle,
            link: route('kpi.index')
        }
    ]

    const { props } = usePage();

    const dataDate = props.dDates;

    const dateRange = ref(getCurrentMonthRange('us'));
    const reportDateRange = ref(getCurrentMonthRange());


    const dates = ref({
        data_date: dataDate[0],
        start_date: reportDateRange.value[0],
        end_date: reportDateRange.value[1]
    })

    const updateDateRange = (newDateRange) => {
        dateRange.value = newDateRange.map(date => formatDate(date));
        reportDateRange.value = newDateRange;

        dates.value.start_date = reportDateRange.value[0]
        dates.value.start_date = reportDateRange.value[1]

        GetMainKPI()
    };

    watch(() => dates.value.data_date, (newValue) => {
        GetMainKPI()
    });

    const mainKPI = ref({})
    const standardKPI = ref({})
    const KPIAccounts = ref({
        packages: [],
        merchantAccounts: []
    })

    const mainKPILabels = [
        {
            label: 'Sold',
            data: 'sold',
        },
        {
            label: 'New Revenue',
            data: 'newRevenue',
        },
        {
            label: "# Suspended",
            data: 'suspended'
        },
        {
            label: '# Churned',
            data: 'churned'
        },
        {
            label: 'Churned Revenue',
            data: 'churnedRevenue'
        },
        {
            label: 'Net Revenue Gain',
            data: 'netRevenueGain'
        },
        {
            label: 'New Trials Created',
            data: 'newTrialsCreated'
        },
        {
            label: 'Current Total Trials',
            data: 'currentTotalTrials'
        },
        {
            label: "SQL's",
            data: 'SQLS'
        }
    ]

    const KPIPackagesLabels = [
        {
            label: 'Silver ($109/mo)',
            data: 'silver'
        },
        {
            label: 'Gold ($199/mo)',
            data: 'gold'
        },
        {
            label: 'Platinum ($299/mo)',
            data: 'platinum'
        },
        {
            label: 'Premier ($399/mo)',
            data: 'premier'
        },
        {
            label: 'Premier Plus ($499/mo)',
            data: 'premierPlus'
        },
        {
            label: 'No Package ($0-$249/mo)',
            data: 'none'
        },
    ]

    const KPIMerchantsLabels = [
        {
            label: '360 Payments',
            data: 'payment360'
        },
        {
            label: 'Cardknox',
            data: 'authorize'
        },
        {
            label: 'TNP',
            data: 'cardknox'
        },
        {
            label: 'Authorize.net',
            data: 'tnp'
        },
        {
            label: 'None',
            data: 'none'
        },
    ]

    const standardKPILabels = [
        {
            label: 'Active SSO Shops',
            data: 'activeSSO'
        },
        {
            label: 'Read Only Shops',
            data: 'readOnlyShops'
        },
        {
            label: 'Enterprise MSO Shops',
            data: 'enterpriseMSOShops'
        },
        {
            label: 'Total Shop Count',
            data: 'totalShopCount'
        },
        {
            label: 'Current Year Pace',
            data: 'currentYearPace'
        },
        {
            label: 'Canadian Shops',
            data: 'canadianShops'
        },
        {
            label: 'Suspended Shops',
            data: 'suspendedShops'
        },
        {
            label: 'Churn',
            data: 'churn'
        },
        {
            label: 'YTD Sold',
            data: 'YTDSold'
        },
        {
            label: 'YTD Daily',
            data: 'YTDDaily'
        },
        {
            label: 'YTD Pace',
            data: 'YTDPace'
        },
        {
            label: 'Last YTD',
            data: 'LastYTD'
        }
    ];


    const mainKPILoading = ref(true);
    const standardKPILoading = ref(true);
    const KPIAccountsLoading = ref(true);

    async function GetMainKPI(type) {
        mainKPILoading.value = true
        try {
            const res = await axios.get(route('kpi.main'), {
                params: {
                    ...dates
                }
            });

            mainKPI.value = res.data
            mainKPILoading.value = false
        } catch (errors) {
            console.error(errors);
        }
    }

    async function GetStandardKPI(type) {
        standardKPILoading.value = true
        try {
            const res = await axios.get(route('kpi.standard'));

            standardKPI.value = res.data
            standardKPILoading.value = false
        } catch (errors) {
            console.error(errors);
        }
    }

    async function GetKPIAccounts(type) {
        KPIAccountsLoading.value = false
        try {
            const res = await axios.get(route('kpi.accounts'));

            KPIAccounts.value = res.data
            KPIAccountsLoading.value = false
        } catch (errors) {
            console.error(errors);
        }
    }

    onMounted(() => {
        GetMainKPI()
        GetKPIAccounts()
        GetStandardKPI()
    });
</script>

<style scoped>
    .header{
        text-align: center;
        font-weight: bold;
    }

    .items.custom-grid{
        display: grid;
        grid-template-columns: 1fr 1fr;
    }
    .items.custom-grid:hover{
        background-color: #dee2e6;
    }

    .items.custom-grid .title{
        font-weight: 550;
    }

    .items.custom-grid .title,
    .items.custom-grid .value{
        border: 1px solid #dee2e6;
        padding: 7px;
    }
</style>
