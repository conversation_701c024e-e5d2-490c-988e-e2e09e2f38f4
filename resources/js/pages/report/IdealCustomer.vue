<template>
    <head>
        <title>ShopBoss - {{ pageTitle }}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle" :dateRange="reportDateRange" :key="computedRoute"/>

        <div class="d-flex justify-content-between align-items-center mb-2">
            <PageLocation :items="pageLocation"/>
            <div class="d-flex flex-row-reverse mb-3 gap-3">
                <DatePicker :date="dateRange" @update:dateRange="updateDateRange"/>
                <div>
                    <label for="package">Choose Package</label>
                    <select v-model="packageType" class="form-control date-range">
                        <option value="all">ALL</option>
                        <option value="platinum">Platinum</option>
                        <option value="silver">Silver</option>
                        <option value="gold">Gold</option>
                        <option value="premier">Premier</option>
                        <option value="premier plus">Premier Plus</option>
                    </select>
                </div>
            </div>
        </div>

        <ReactiveReportComponent :columns="columns" :route="computedRoute" :key="computedRoute"/>
    </DashboardLayout>
</template>

<script setup>
    import { ref, computed } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import DatePicker from '@/components/utils/DatePicker.vue';
    import { getCurrentMonthRange, formatDate } from '@/utils/utils.js'

    const pageTitle = 'Ideal Customer Report';

    const pageLocation = [
        {
            name: 'Report',
            link: route('report.index')
        },
        {
            name: pageTitle,
            link: route('ideal.customer.index')
        }
    ]

    const columns = ref(
        [
            {data: 'selectedPackage', name: 'selectedPackage', label: 'Package'},
            {data: 'totalRO', name: 'totalRO', label: "Total RO's"},
            {data: 'roRevenue', name: 'roRevenue', label: 'RO Revenue'},
            {data: 'ARO', name: 'ARO', label: 'ARO'},
        ]
    );

    const dateRange = ref(getCurrentMonthRange('us'));
    const reportDateRange = ref(getCurrentMonthRange());
    const packageType = ref('all');

    const updateDateRange = (newDateRange) => {
        dateRange.value = newDateRange.map(date => formatDate(date));
        reportDateRange.value = newDateRange;
    };

    const computedRoute = computed(() => {
        const [startDate, endDate] = reportDateRange.value;
        return route('ideal.customer.datatable', {start_date: startDate, end_date: endDate, package: packageType.value});
    });
</script>

<style scoped>
    td .multiline {
        white-space: pre-line;
    }
    .btn {
        margin-bottom: 10px;
    }
</style>
