<template>
    <head>
        <title>ShopBoss - {{ pageTitle }}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle"/>
        <PageLocation :items="pageLocation"/>
        <ReactiveReportComponent :columns="columns" :route="route('bluesnap.ms.datatable')"/>
    </DashboardLayout>
</template>

<script setup>
    import { ref } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';

    const pageTitle = 'BlueSnap ID Enterprise Report';

    const pageLocation = [
        {
            name: 'Report',
            link: route('report.index')
        },
        {
            name: pageTitle,
            link: route('bluesnap.ms.index')
        }
    ]

    const columns = ref(
        [
            {data: 'enterpriseid', name: 'enterpriseid', label: 'Enterprice ID'},
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'bluesnapid', name: 'bluesnapid', label: 'BlueSnap ID'},
            {data: 'CompanyName', name: 'CompanyName', label: 'Company Name'},
            {data: 'contact', name: 'contact', label: 'Contact'},
        ]
    );
</script>
