<template>
    <head>
        <title>ShopBoss - {{ pageTitle }}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle"/>
        <PageLocation :items="pageLocation"/>
        <ReactiveReportComponent :columns="columns" :route="route('accounting.matco.datatable')"/>
    </DashboardLayout>
</template>

<script setup>
    import { ref } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';

    const pageTitle = 'MATCO Accounting Report';

    const pageLocation = [
        {
            name: 'Report',
            link: route('report.index')
        },
        {
            name: pageTitle,
            link: route('accounting.matco.index')
        }
    ]
    const columns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'contact', name: 'contact', label: 'Contact'},
            {data: 'CompanyName', name: 'CompanyName', label: 'Company Name'},
            {data: 'address', name: 'address', label: 'Company Address'},
            {data: 'CompanyEMail', name: 'companyemail', label: 'Company Email'},
            {data: 'CompanyPhone', name: 'CompanyPhone', label: 'Company Phone'},
            {data: 'billing', name: 'billing', label: 'Billing Address'},
            {data: 'merchantaccount', name: 'merchantaccount', label: 'Merchant'},
            {data: 'newpackagetype', name: 'newpackagetype', label: 'Package'},
            {data: 'flatprice', name: 'flatprice', label: 'Flat Price'},
        ]
    );
</script>
