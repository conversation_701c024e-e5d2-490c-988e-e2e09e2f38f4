<template>
    <head>
        <title>ShopBoss - {{ pageTitle }}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle" :dateRange="reportDateRange" :key="computedRoute"/>

        <div class="d-flex justify-content-between align-items-center mb-2">
            <PageLocation :items="pageLocation"/>
            <div class="d-flex flex-row-reverse mb-3 align-items-end">
                <DatePicker :date="dateRange" @update:dateRange="updateDateRange"/>
                <div class="form-check pe-3">
                    <input class="form-check-input" type="checkbox" v-model="suspended" id="flexCheckDefault">
                    <label class="form-check-label" for="flexCheckDefault">
                        Show Suspended Shops
                    </label>
                </div>
            </div>
        </div>

        <ReactiveReportComponent :columns="columns" :route="computedRoute" :key="computedRoute"/>
    </DashboardLayout>
</template>

<script setup>
    import { ref, computed } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import { getCurrentMonthRange, formatDate } from '@/utils/utils.js'
    import DatePicker from '@/components/utils/DatePicker.vue';

    const pageTitle = 'Clawback Report';

    const pageLocation = [
        {
            name: 'Report',
            link: route('report.index')
        },
        {
            name: pageTitle,
            link: route('clawback.report.index')
        }
    ]

    const columns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'companyname', name: 'companyname', label: 'Shop Name'},
            {data: 'salesrep', name: 'salesrep', label: 'Sales Rep'},
            {data: 'newpackagetype', name: 'newpackagetype', label: 'Package'},
            {data: 'dateofacceptance', name: 'dateofacceptance', label: 'D.O.A',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.dateofacceptance ? formatDate(row.dateofacceptance) : '';
                    }
                    return row.dateofacceptance || '';
                }
            },
            {data: 'firstPayDate', name: 'firstPayDate', label: 'First Pay Date',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.firstPayDate ? formatDate(row.firstPayDate) : '';
                    }
                    return row.firstPayDate || '';
                }
            },
            {data: 'lastPayDate', name: 'lastPayDate', label: 'Last Pay Date',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.lastPayDate ? formatDate(row.lastPayDate) : '';
                    }
                    return row.lastPayDate || '';
                }
            },
            {data: 'lastPayAmount', name: 'lastPayAmount', label: 'Last Pay Amount'},
            {data: 'lastpaymentdate', name: 'lastpaymentdate', label: 'Last Pay Date',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.lastpaymentdate ? formatDate(row.lastpaymentdate) : '';
                    }
                    return row.lastpaymentdate || '';
                }
            },
            {data: 'status', name: 'status', label: 'Status'},
            {data: 'totalPayments', name: 'totalPayments', label: 'Total Payments'},
            {data: 'lastRODate', name: 'lastRODate', label: 'Last RO Date',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.lastRODate ? formatDate(row.lastRODate) : '';
                    }
                    return row.lastRODate || '';
                }
            },
            {
                data: 'shopid',
                name: 'link',
                label: 'Comments',
                render: function(data) {
                    return `<a href="/dashboard/report/clawback/${data}" class="btn btn-primary">View</a>`;
                },
                orderable: false,
                searchable: false
            }
        ]
    );

    const dateRange = ref(getCurrentMonthRange('us'));
    const reportDateRange = ref(getCurrentMonthRange());
    const suspended = ref(false);

    const updateDateRange = (newDateRange) => {
        dateRange.value = newDateRange.map(date => formatDate(date));
        reportDateRange.value = newDateRange;
    };

    const computedRoute = computed(() => {
        const [startDate, endDate] = reportDateRange.value;
        return route('clawback.report.datatable', {start_date: startDate, end_date: endDate, suspended: suspended.value});
    });
</script>

<style scoped>
    td .multiline {
        white-space: pre-line;
    }
    .btn {
        margin-bottom: 10px;
    }
</style>
