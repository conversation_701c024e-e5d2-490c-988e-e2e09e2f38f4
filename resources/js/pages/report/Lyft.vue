<template>
    <head>
        <title>ShopBoss - {{pageTitle}}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle"/>

        <div class="d-flex justify-content-between align-items-center mb-2 main-filters-flexed">
            <PageLocation :items="pageLocation"/>

            <div class="d-flex align-items-end filters-flexed">
                <DatePicker :date="dateRange" @update:dateRange="updateDateRange"/>
            </div>
        </div>

        <ReactiveReportComponent :columns="columns" :route="computedRoute" :key="computedRoute"/>
    </DashboardLayout>
</template>

<script setup>
    import { ref, computed } from 'vue';
    import { Link } from '@inertiajs/vue3';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import DatePicker from '@/components/utils/DatePicker.vue';
    import { route } from 'ziggy-js';
    import { getCurrentMonthRange, formatDate } from '@/utils/utils.js'

    const pageTitle = 'Lyft Report';

    const pageLocation = [
        {
            name: 'Report',
            link: '/dashboard/report'
        },
        {
            name: 'Shop List',
            link: '/dashboard/report/shop-list'
        },
        {
            name: 'Billing',
            link: '/dashboard/report/billing'
        },
        {
            name: pageTitle,
            link: '/dashboard/report/lyft'
        }
    ]

    const columns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'paymentamt', name: 'paymentamt', label: 'Payment Amount'},
            {data: 'transdate', name: 'transdate', label: 'Transaction Date',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.transdate ? formatDate(row.transdate, true) : '';
                    }
                    return row.transdate || '';
                }
            },
        ]
    );

    const dateRange = ref(getCurrentMonthRange('us'));
    const reportDateRange = ref(getCurrentMonthRange());

    const updateDateRange = (newDateRange) => {
        dateRange.value = newDateRange.map(date => formatDate(date));
        reportDateRange.value = newDateRange;
    };

    const computedRoute = computed(() => {
        const [startDate, endDate] = reportDateRange.value;
        return route('lyft.datatable', {start_date: startDate, end_date: endDate});
    });
</script>
