<template>
    <head>
        <title>ShopBoss - {{ pageTitle }}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle" :dateRange="reportDateRange" :key="computedRoute"/>

        <div class="d-flex justify-content-between align-items-center mb-2">
            <PageLocation :items="pageLocation"/>
            <DatePicker :date="dateRange" @update:dateRange="updateDateRange"/>
        </div>

        <ReactiveReportComponent :columns="columns" :route="computedRoute" :key="computedRoute"/>
    </DashboardLayout>
</template>

<script setup>
    import { ref, computed } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import { getCurrentMonthRange, formatDate } from '@/utils/utils.js'
    import DatePicker from '@/components/utils/DatePicker.vue';

    const pageTitle = 'Package Upgrades Report';

    const pageLocation = [
        {
            name: 'Report',
            link: route('report.index')
        },
        {
            name: pageTitle,
            link: route('package.upgrades.index')
        }
    ]

    const columns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'companyname', name: 'companyname', label: 'Company Name'},
            {data: 'companyphone', name: 'companyphone', label: 'Company Phone'},
            {data: 'companyemail', name: 'companyemail', label: 'Company Email'},
            {data: 'name', name: 'name', label: 'Add On'},
            {data: 'dateadded', name: 'dateadded', label: 'Date',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.dateadded ? formatDate(row.dateadded) : '';
                    }
                    return row.dateadded || '';
                }
            },
            {data: 'price', name: 'price', label: 'Price'},
            {data: 'vhc_number', name: 'vhc_number', label: 'VHC Number'},
        ]
    );

    const dateRange = ref(getCurrentMonthRange('us'));
    const reportDateRange = ref(getCurrentMonthRange());

    const updateDateRange = (newDateRange) => {
        dateRange.value = newDateRange.map(date => formatDate(date));
        reportDateRange.value = newDateRange;
    };

    const computedRoute = computed(() => {
        const [startDate, endDate] = reportDateRange.value;
        return route('package.upgrades.datatable', {start_date: startDate, end_date: endDate});
    });
</script>

<style scoped>
    td .multiline {
        white-space: pre-line;
    }
    .btn {
        margin-bottom: 10px;
    }
</style>
