<template>
    <head>
        <title>ShopBoss - {{ pageTitle }}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle" :dateRange="reportDateRange" :key="computedRoute"/>

        <div class="d-flex justify-content-between align-items-center mb-2">
            <PageLocation :items="pageLocation"/>
            <div class="d-flex flex-row-reverse mb-3 align-items-end">
                <DatePicker :date="dateRange" @update:dateRange="updateDateRange"/>
                <div class="form-check pe-3">
                    <input class="form-check-input" type="checkbox" v-model="notInDate" id="flexCheckDefault">
                    <label class="form-check-label" for="flexCheckDefault">
                        Not Created In Date Range
                    </label>
                </div>
            </div>
        </div>

        <ReactiveReportComponent :columns="columns" :route="computedRoute" :key="computedRoute"/>
    </DashboardLayout>
</template>

<script setup>
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import { ref, computed } from 'vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import DatePicker from '@/components/utils/DatePicker.vue';
    import { getCurrentMonthRange, calculateElapsedTime, formatDate } from '@/utils/utils.js'

    const pageTitle = 'Churn Report';

    const pageLocation = [
        {
            name: 'Report',
            link: route('report.index')
        },
        {
            name: pageTitle,
            link: route('retention.churn.index')
        }
    ]

    const columns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'datestarted', name: 'datestarted', label: 'Date Started',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.datestarted ? formatDate(row.datestarted) : '';
                    }
                    return row.datestarted || '';
                }
            },
            {data: 'CompanyName', name: 'CompanyName', label: 'Company Name'},
            {data: 'CompanyEmail', name: 'CompanyEmail', label: 'Company Email'},
            {data: 'CompanyPhone', name: 'CompanyPhone', label: 'Company Phone'},
            {data: 'category', name: 'category', label: 'Category', searchable: false, orderable: false},
            {data: 'datein', name: 'datein', label: 'LRO',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.datein ? formatDate(row.datein) : '';
                    }
                    return row.datein || '';
                }
            },
            {data: 'datesuspended', name: 'datesuspended', label: 'Suspended Date'},
            {data: 'newpackagetype', name: 'newpackagetype', label: 'Package'},
            {data: 'lastpaymentdate', name: 'lastpaymentdate', label: 'LPD',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.lastpaymentdate ? formatDate(row.lastpaymentdate) : '';
                    }
                    return row.lastpaymentdate || '';
                }
            },
            {data: 'flatprice', name: 'flatprice', label: 'Price'},
        ]
    );

    const dateRange = ref(getCurrentMonthRange('us'));
    const reportDateRange = ref(getCurrentMonthRange());
    const notInDate = ref(false);

    const updateDateRange = (newDateRange) => {
        dateRange.value = newDateRange.map(date => formatDate(date));
        reportDateRange.value = newDateRange;
    };

    const computedRoute = computed(() => {
        const [startDate, endDate] = reportDateRange.value;
        return route('retention.churn.datatable', {start_date: startDate, end_date: endDate, not_in_date: notInDate.value});
    });
</script>

<style scoped>
    td .multiline {
        white-space: pre-line;
    }
    .btn {
        margin-bottom: 10px;
    }
</style>
