<template>
    <head>
        <title>ShopBoss - Reports</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="'Reports'"/>

        <div :class="userCanAccessSection ? 'reports' : 'report'">
            <div class="card" v-if="userCanAccessSection">
                <div class="card-body">
                    <h5 class="pb-3 mb-3 border-bottom d-flex gap-2">
                        <i class="bx bx-wallet"></i>
                        Accounting Reports
                    </h5>
                    <div class="d-flex flex-wrap gap-3">
                        <Link :class="linkClasses" :href="route('churn.index')">Churn</Link>
                        <Link :class="linkClasses" :href="route('accounting.index')">Accounting</Link>
                        <Link :class="linkClasses" :href="route('accounting.ms.index')">Accounting Enterprise</Link>
                        <Link :class="linkClasses" :href="route('bluesnap.index')">BlueSnap</Link>
                        <Link :class="linkClasses" :href="route('bluesnap.ms.index')">BlueSnap Enterprise</Link>
                        <Link :class="linkClasses" :href="route('accounting.matco.index')">MATCO Accounting</Link>
                        <Link :class="linkClasses" :href="route('accounting.matco.ms.index')">MATCO Accounting Enterprise</Link>
                    </div>
                </div>
            </div>

            <div class="card" v-if="userCanAccessSection">
                <div class="card-body">
                    <h5 class="pb-3 mb-3 border-bottom d-flex gap-2">
                        <i class="bx bx-line-chart-down"></i>
                        Cavan Reports
                    </h5>
                    <div class="d-flex flex-wrap gap-3">
                        <Link :class="linkClasses" :href="route('ro.data.index')">RO Data</Link>
                        <Link :class="linkClasses" :href="route('daily.totals.index')">Daily Totals</Link>
                        <Link :class="linkClasses" :href="route('weekly.totals.index')">Weekly Totals</Link>
                        <Link :class="linkClasses" :href="route('ideal.customer.index')">Ideal Customer</Link>
                        <Link :class="linkClasses" :href="route('custom.report.list.index')">Custom Report List</Link>
                        <Link :class="linkClasses" :href="route('kpi.index')">KPI</Link>
                    </div>
                </div>
            </div>

            <div class="card" v-if="userCanAccessSection">
                <div class="card-body">
                    <h5 class="pb-3 mb-3 border-bottom d-flex gap-2">
                        <i class='bx bx-notepad'></i>
                        Luke Reports
                    </h5>
                    <div class="d-flex flex-wrap gap-3">
                        <Link :class="linkClasses" :href="route('matco.churn.list.index')">Matco Churn List</Link>
                        <Link :class="linkClasses" :href="route('matco.scan.list.index')">Matco Scan List</Link>
                    </div>
                </div>
            </div>

            <div class="card" v-if="userCanAccessSection">
                <div class="card-body">
                    <h5 class="pb-3 mb-3 border-bottom d-flex gap-2">
                        <i class='bx bx-bar-chart-alt-2'></i>
                        Sales Reports
                    </h5>
                    <div class="d-flex flex-wrap gap-3">
                        <Link :class="linkClasses" :href="route('sales.index')">Sales Report</Link>
                        <!-- <Link :class="linkClasses" :href="route('dvi.ss.users.index')">DVI SS SHOPBOSS Users Report</Link> -->
                        <Link :class="linkClasses" :href="route('clawback.index')">Clawback</Link>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <h5 class="pb-3 mb-3 border-bottom d-flex gap-2">
                        <i class='lni lni-spiner-solid'></i>
                        Retention Reports
                    </h5>
                    <div class="d-flex flex-wrap gap-3">
                        <Link :class="linkClasses" :href="route('retention.churn.index')">Churn</Link>
                        <Link :class="linkClasses" :href="route('churn.list.retention.index')">Churn List</Link>
                        <Link :class="linkClasses" :href="route('declined.transactions.index')">Declined Transactions</Link>
                        <Link :class="linkClasses" :href="route('suspended.shops.list.index')">Suspended Shops List</Link>
                        <Link :class="linkClasses" :href="route('motor.usage.prodemand.index')">Motor Usage W/ProDemand</Link>
                        <Link :class="linkClasses" :href="route('shop.category.index')">Shop Category</Link>
                        <Link :class="linkClasses" :href="route('readonly.shops.index')">Read Only Shops</Link>
                        <Link :class="linkClasses" :href="route('pph.internal.index')">PPH Internal</Link>
                        <Link :class="linkClasses" :href="route('lro.churn.index')">LRO Churn</Link>
                    </div>
                </div>
            </div>

            <div class="card" v-if="userCanAccessSection">
                <div class="card-body">
                    <h5 class="pb-3 mb-3 border-bottom d-flex gap-2">
                        <i class='bx bx-badge'></i>
                        Other Reports
                    </h5>
                    <div class="d-flex flex-wrap gap-3">
                        <Link :class="linkClasses" :href="route('package.changelog.index')">Package Changelog</Link>
                        <Link :class="linkClasses" :href="route('package.upgrades.index')">Package Upgrades</Link>
                        <Link :class="linkClasses" :href="route('trial.shops.index')">Trial Shops</Link>
                        <Link :class="linkClasses" :href="route('dvi.compare.index')">DVI Compare</Link>
                        <Link :class="linkClasses" :href="route('motor.usage.index')">Motor Usage</Link>
                        <Link :class="linkClasses" :href="route('motor.export.index')">Motor Export</Link>
                        <Link :class="linkClasses" :href="route('accounting.link.shops.index')">Accounting Link Shops</Link>
                    </div>
                </div>
            </div>

            <div class="card" v-if="userCanAccessSection">
                <div class="card-body">
                    <h5 class="pb-3 mb-3 border-bottom d-flex gap-2">
                        <i class='bx bx-pie-chart-alt-2'></i>
                        Custom Reports
                    </h5>
                    <div class="d-flex flex-wrap gap-3">
                        <Link :class="linkClasses" :href="route('custom-reports.index')">Custom Reports</Link>
                        <Link :class="linkClasses" :href="route('billing.index')">Billing Report</Link>
                        <Link :class="linkClasses" :href="route('employees.index')">Updated Login Username Report</Link>
                        <Link :class="linkClasses" :href="route('dlc-dashboard-tracking.index')">10DLC Dashboard Tracking</Link>
                        <Link :class="linkClasses" :href="route('bandwith-messages.index')">Bandwith Messages Report</Link>
                        <!-- <Link :class="linkClasses" :href="route('mandrill-messages.index')">Mandrill Messages Report</Link> -->
                    </div>
                </div>
            </div>
        </div>
    </DashboardLayout>
</template>

<script setup>
    import { Link,usePage } from '@inertiajs/vue3'
    import { computed } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';

    const { props } = usePage();

    const linkClasses = ['btn', 'btn-outline-secondary', 'px-3', 'rounded-0'];

    const userRole = props.auth.user.role;
    const userCanAccessSection = computed(() => ['superadmin', 'admin'].includes(userRole));
</script>

<style scoped>
    .reports{
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 30px;
        align-items: start
    }

    .reports .card{
        margin: 0;
    }

    @media only screen and (max-width: 1200px) {
        .reports{
            grid-template-columns: 1fr;
        }
    }
</style>
