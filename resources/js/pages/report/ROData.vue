<template>
    <head>
        <title>ShopBoss - {{ pageTitle }}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle" :dateRange="reportDateRange" :key="computedRoute"/>

        <div class="d-flex justify-content-between align-items-center mb-2">
            <PageLocation :items="pageLocation"/>
            <DatePicker :date="dateRange" @update:dateRange="updateDateRange"/>
        </div>

        <ReactiveReportComponent :columns="columns" :route="computedRoute" :key="computedRoute"/>
    </DashboardLayout>
</template>

<script setup>
    import { ref, computed, watchEffect } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import DatePicker from '@/components/utils/DatePicker.vue';
    import { getCurrentMonthRange, asDollars, formatDate } from '@/utils/utils.js';

    const pageTitle = 'RO Data Report';

    const pageLocation = [
        {
            name: 'Report',
            link: route('report.index')
        },
        {
            name: pageTitle,
            link: route('ro.data.index')
        }
    ];

    const columns = ref([]);
    const dateRange = ref(getCurrentMonthRange('us'));
    const reportDateRange = ref(getCurrentMonthRange());

    const generateDynamicColumns = (startDate, endDate) => {
        const start = new Date(startDate);
        const end = new Date(endDate);

        const dynamicColumns = [];
        while (start <= end) {
            const month = start.getMonth() + 1;
            const year = start.getFullYear();
            const monthYear = `${year}_${String(month).padStart(2, '0')}`;
            const formattedLabel = start.toLocaleString('default', { month: 'short' }) + '-' + year;

            dynamicColumns.push({ data: `cnt_${monthYear}`, name: `cnt_${monthYear}`, label: `Count ${formattedLabel}`,
                render: function (data, type, row) {
                    return row[`cnt_${monthYear}`] > 0 ? (row[`cnt_${monthYear}`]) : '';
                }
            });
            dynamicColumns.push({
                data: `tro_${monthYear}`, name: `tro_${monthYear}`, label: `Revenue ${formattedLabel}`,
                render: function (data, type, row) {
                    return row[`tro_${monthYear}`] > 0 ? asDollars(row[`tro_${monthYear}`]) : '';
                }
            });

            start.setMonth(start.getMonth() + 1);
        }

        return dynamicColumns;
    };

    const updateColumns = () => {
        const [startDate, endDate] = reportDateRange.value;
        columns.value = [
            { data: 'shopid', name: 'shopid', label: 'Shop ID' },
            { data: 'CompanyName', name: 'CompanyName', label: 'Company Name' },
            { data: 'newpackagetype', name: 'newpackagetype', label: 'Package' },
            { data: 'status', name: 'status', label: 'Status' },
            {
                data: 'datesuspended', name: 'datesuspended', label: 'Suspended Date',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.datesuspended ? formatDate(row.datesuspended) : '';
                    }
                    return row.datesuspended || '';
                }
            },
            ...generateDynamicColumns(startDate, endDate)
        ];
    };

    const updateDateRange = (newDateRange) => {
        dateRange.value = newDateRange.map(date => formatDate(date));
        reportDateRange.value = newDateRange;
        updateColumns();
    };

    const computedRoute = computed(() => {
        const [startDate, endDate] = reportDateRange.value;
        return route('ro.data.datatable', {start_date: startDate, end_date: endDate});
    });

    watchEffect(() => {
        updateColumns();
    });

</script>

<style scoped>
    td .multiline {
        white-space: pre-line;
    }
    .btn {
        margin-bottom: 10px;
    }
</style>
