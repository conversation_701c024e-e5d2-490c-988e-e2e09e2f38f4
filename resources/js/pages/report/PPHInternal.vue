<template>
    <head>
        <title>ShopBoss - {{ pageTitle }}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle" :dateRange="reportDateRange" :key="computedRoute"/>

        <div class="d-flex justify-content-between align-items-center mb-2">
            <PageLocation :items="pageLocation"/>
            <DatePicker :date="dateRange" @update:dateRange="updateDateRange"/>
        </div>

        <ReactiveReportComponent :columns="columns" :route="computedRoute" :key="computedRoute"/>
    </DashboardLayout>
</template>

<script setup>
    import { ref, computed } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import { getCurrentMonthRange, formatDate } from '@/utils/utils.js'
    import DatePicker from '@/components/utils/DatePicker.vue';

    const pageTitle = 'PPH Internal Report';

    const pageLocation = [
        {
            name: 'Report',
            link: '/dashboard/report'
        },
        {
            name: pageTitle,
            link: '/dashboard/report/pph/internal'
        }
    ]

    const columns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'companyname', name: 'companyname', label: 'Company Name'},
            {data: 'companyphone', name: 'companyphone', label: 'Phone'},
            {data: 'companycity', name: 'companycity', label: 'City'},
            {data: 'companystate', name: 'companystate', label: 'State'},
            {data: 'timezone', name: 'timezone', label: 'Timezone'},
            {data: 'companyemail', name: 'companyemail', label: 'Email'},
            {data: 'newpackagetype', name: 'newpackagetype', label: 'Package'},
            {data: 'category', name: 'category', label: 'category'},
            {data: 'totalro', name: 'totalro', label: 'AVG ARO'},
            {data: 'pph', name: 'pph', label: 'AVG PPH'},
        ]
    );

    const dateRange = ref(getCurrentMonthRange('us'));
    const reportDateRange = ref(getCurrentMonthRange());

    const updateDateRange = (newDateRange) => {
        dateRange.value = newDateRange.map(date => formatDate(date));
        reportDateRange.value = newDateRange;
    };

    const computedRoute = computed(() => {
        const [startDate, endDate] = reportDateRange.value;
        return `/dashboard/report/pph/internal/datatable?start_date=${startDate}&end_date=${endDate}`;
    });
</script>

<style scoped>
    td .multiline {
        white-space: pre-line;
    }
    .btn {
        margin-bottom: 10px;
    }
</style>
