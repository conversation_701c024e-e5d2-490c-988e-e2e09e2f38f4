<template>
    <head>
        <title>ShopBoss - {{pageTitle}}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle"/>

        <div class="d-flex justify-content-between align-items-center mb-2 main-filters-flexed">
            <PageLocation :items="pageLocation"/>

            <div class="d-flex align-items-end filters-flexed">
                <div class="form-check pe-3" v-if="!shopIsMatco && userRole != 'customer support' && userRole != 'customer success'">
                    <Link class="custom-button" :href="route('lyft.index')">Lyft Transactions</Link>
                </div>
            </div>
        </div>

        <ReactiveReportComponent :columns="columns" :route="route('billing.datatable')"/>
    </DashboardLayout>
</template>

<script setup>
    import { ref } from 'vue';
    import { Link } from '@inertiajs/vue3';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import { formatDate } from '@/utils/utils.js'

    const pageTitle = 'Billing Report';

    const pageLocation = [
        {
            name: 'Report',
            link: route('report.index')
        },
        {
            name: 'Shop List',
            link: route('report.shoplist.index')
        },
        {
            name: pageTitle,
            link: route('billing.index')
        }
    ]

    const columns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'companyname', name: 'companyname', label: 'Company'},
            {data: 'howpaying', name: 'howpaying', label: 'Method'},
            {data: 'dateofacceptance', name: 'dateofacceptance', label: 'Accept Date',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.dateofacceptance ? formatDate(row.dateofacceptance) : '';
                    }
                    return row.dateofacceptance || '';
                }
            },
            {data: 'datestarted', name: 'datestarted', label: 'Date Started',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.datestarted ? formatDate(row.datestarted) : '';
                    }
                    return row.datestarted || '';
                }
            },
            {data: 'bankaccount', name: 'bankaccount', label: 'Bank Account'},
            {data: 'routing', name: 'routing', label: 'Bank ID'},
            {data: 'flatprice', name: 'flatprice', label: 'Amount', render: $.fn.dataTable.render.number('.', ',', 0, '$')},
        ]
    );
</script>

<style scoped>
    .custom-button{
        border: 1px solid #0d6efd;
        border-radius: 10px;
        padding: 5px;
        transition: .4s;
    }
    .custom-button:hover{
        background-color: #0d6efd;
        color: white;
    }
</style>
