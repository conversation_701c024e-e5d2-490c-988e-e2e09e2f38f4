<template>
    <head>
        <title>ShopBoss - {{pageTitle}}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle"/>
        <PageLocation :items="pageLocation"/>
        <ReactiveReportComponent :columns="columns" :route="route('shop.category.datatable')"/>
    </DashboardLayout>
</template>

<script setup>
    import { ref } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';

    const pageTitle = 'Shop Category Report';

    const pageLocation = [
        {
            name: 'Report',
            link: route('report.index')
        },
        {
            name: pageTitle,
            link: route('shop.category.index')
        }
    ]

    const columns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'CompanyName', name: 'CompanyName', label: 'Company Name'},
            {data: 'newpackagetype', name: 'newpackagetype', label: 'Package'},
            {data: 'category', name: 'category', label: 'Category'},
            {data: 'level', name: 'level', label: 'Level'},
            {data: 'vhc_number', name: 'vhc_number', label: 'VHC Number'},
        ]
    );
</script>
