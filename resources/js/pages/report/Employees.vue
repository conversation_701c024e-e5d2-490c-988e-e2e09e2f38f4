<template>
    <head>
        <title>ShopBoss - {{pageTitle}}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle"/>
        <PageLocation :items="pageLocation"/>

        <div class="d-flex justify-content-end mb-2">
            <div class="form-check pe-3">
                <input class="form-check-input" type="checkbox" v-model="employeesCompleted">
                <label class="form-check-label" for="flexCheckDefault">
                    Employees Completed
                </label>
            </div>
        </div>

        <ReactiveReportComponent
            :columns="employeeColumns"
            :route="route('employees.data', {'employeesCompleted' : employeesCompleted})"
            :key="employeesCompleted"
        />

        <div class="d-flex justify-content-end mb-2" style="margin-top: 40px;">
            <div class="form-check pe-3">
                <input class="form-check-input" type="checkbox" v-model="shopsCompleted">
                <label class="form-check-label" for="flexCheckDefault">
                    Shops Completed
                </label>
            </div>
        </div>

        <ReactiveReportComponent
            :columns="shopsCompleted ? shopCompletedColumns : shopNotCompletedColumns"
            :route="route('employees.shops', {'shopsCompleted' : shopsCompleted})"
            :totals="shopsCompleted ? [completedShops] : [notCompletedShops] "
            id="shopsdata"
            :key="shopsCompleted"
        />
    </DashboardLayout>
</template>

<script setup>
    import { ref } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import { formatDate } from '@/utils/utils.js'
    import { usePage } from '@inertiajs/vue3';

    const pageTitle = 'Updated Login Username Report';
    const { props } = usePage();
    const completedShops = "Total Shops Completed: " + props.completedShops;
    const notCompletedShops = "Total Shops Not Completed: " + props.notCompletedShops;

    const pageLocation = [
        {
            name: 'Report',
            link: '/dashboard/report'
        },
        {
            name: pageTitle,
            link: '/dashboard/report/accounting'
        }
    ]

    const employeesCompleted = ref(true);
    const shopsCompleted = ref(true);

    const employeeColumns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'employee', name: 'employee', label: 'Employee'},
            {data: 'username', name: 'username', label: 'Username'},
            {data: 'datehired', name: 'datehired', label: 'Date Hired',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.datehired ? formatDate(row.datehired) : '';
                    }
                    return row.datehired || '';
                }
            },
        ]
    );

    const shopCompletedColumns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'companyName', name: 'companyName', label: 'Company Name'},
        ]
    );

    const shopNotCompletedColumns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'companyName', name: 'companyName', label: 'Company Name',
                render: function (data, type, row) {
                    if (type === 'display') {
                        const parser = new DOMParser();
                        const decodedCompanyName = parser.parseFromString(row.companyName, "text/html").body.textContent;
                        return decodedCompanyName || '';
                    }
                    return row.companyName || '';
                }
            },
            {data: 'employee', name: 'employee', label: 'Employee'},
            {data: 'datehired', name: 'datehired', label: 'Date Hired',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.datehired ? formatDate(row.datehired) : '';
                    }
                    return row.datehired || '';
                }
            }
        ]
    );
</script>

<style scoped>
    tr td{
        text-transform: capitalize;
    }
    tr td:nth-child(3){
        text-transform: none;
    }
    #shopsdata_wrapper tr td:nth-child(3){
        text-transform: capitalize;
    }
</style>
