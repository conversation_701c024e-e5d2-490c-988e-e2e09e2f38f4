<template>
    <head>
        <title>ShopBoss - Churn </title>
    </head>

    <DashboardLayout>
        <div class="d-flex justify-content-between">
            <PageLocation :items="pageLocation"/>
            <div>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newCommentModal">New Comment</button>
            </div>
        </div>

        <div class="card" v-for="comment in comments" :key="comment">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <p class="mb-0">{{ comment.comment }}</p>
                    <p class="mb-0 text-nowrap">{{ formatDate(comment.commentdatetime) }}</p>
                </div>
            </div>
        </div>
    </DashboardLayout>

    <div class="modal fade" id="newCommentModal" tabindex="-1" style="display: none;" aria-hidden="true">
        <MessageAlertComponent :messageType="alertMessage.messageType" :title="alertMessage.title" :message="alertMessage.message" :messageCount="alertMessage.messageCount"/>
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">New Comment</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="input-group">
                        <span class="input-group-text">Comment</span>
                        <textarea v-model="newComment.comment" class="form-control" aria-label="With textarea"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" @click="addComment">Add</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
    import { reactive, ref } from 'vue'
    import { usePage, router } from '@inertiajs/vue3';

    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import { formatDate } from '@/utils/utils.js'
    import MessageAlertComponent from '@/components/alerts/MessageAlertComponent.vue';

    const { props } = usePage();

    const pageLocation = [
        {
            name: 'Report',
            link: route('report.index')
        },
        {
            name: 'Churn',
            link: route('churn.index')
        }
    ]

    const comments = ref(props.comments || [])
    const shopid = props.shopid

    const newComment = reactive({
        comment: "",
        shopid: shopid
    })

    const alertMessage = ref({
        messageType: "",
        title: "",
        message: "",
        messageCount: 0,
    })

    function addAlertMessage(title, message, messageType) {
        alertMessage.value.title = title;
        alertMessage.value.message = message;
        alertMessage.value.messageType = messageType;
        alertMessage.value.messageCount += 1;
    }

    const addComment = () => {
        axios.post(route('churn.store', shopid), newComment)
        .then(response => {
            comments.value.unshift(response.data.data)
            newComment.comment = ""

            addAlertMessage("Comment Added", "Your comment has been successfully added.", "success")
        }).catch(error => {
            addAlertMessage("Comment Not Added", "There was an error adding your comment. Please try again.", "error")
        });
    };
</script>



