<template>
    <head>
        <title>ShopBoss - {{title}}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="title" />

        <PageLocation :items="pageLocation"/>

        <div class="card">
            <div class="card-body p-4">
                <h4 class="text-center">User Info</h4>
                <hr>
                <form @submit.prevent="user.post(route('user.store'))">
                    <div class="custom-grid">
                        <InputComponent
                            label="Name"
                            iconClass="bx bx-user"
                            v-model="user.name"
                            :error="user.errors.name"
                            placeholder="Name"
                            :required="true"
                        />

                        <InputComponent
                            label="Email"
                            iconClass="bx bx-envelope"
                            v-model="user.email"
                            :error="user.errors.email"
                            placeholder="Email"
                            :required="true"
                        />

                        <Select2Component
                            :data="['superadmin', 'admin', 'matco', 'customer success', 'customer support', 'sales']"
                            v-model="user.role"
                            label="Role"
                            iconClass="bx bx-shield-quarter"
                            :error="user.errors.role"
                            :required="true"
                        />

                        <PasswordComponent
                            label="Password"
                            iconClass="bx bx-key"
                            v-model="user.password"
                            :error="user.errors.password"
                            :required="true"
                        />
                    </div>

                    <div class="d-grid justify-content-center mt-3">
                        <button class="btn btn-primary" type="submit" :disabled="user.processing">
                            <span v-if="user.processing">
                                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                Saving
                            </span>
                            <span v-else>Create</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </DashboardLayout>




    <div v-if="user.wasSuccessful">
        <BaseAlertComponent
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'New User'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </div>
</template>

<script setup>
    import { computed, watch } from 'vue';
    import { usePage, Link, useForm } from '@inertiajs/vue3';

    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import BaseAlertComponent from '@/components/alerts/BaseAlertComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import InputComponent from '@/components/utils/InputComponent.vue';
    import PasswordComponent from '@/components/utils/PasswordComponent.vue';
    import Select2Component from '@/components/utils/Select2Component.vue'

    const { props } = usePage();
    const title = 'New User'

    const pageLocation = [
        {
            name: 'Users',
            link: route('user.index')
        },
        {
            name: title,
            link: ''
        }
    ]

    const user = useForm({
        name: '',
        email: '',
        password: '',
        role: 'admin'
    })

    watch(() => user.wasSuccessful, () => {
        user.name = ''
        user.password = ''
        user.email = ''
    })
</script>

<style scoped>
    .custom-grid{
        display: grid;
        grid-template-columns: 1fr 1fr;
        column-gap: 30px;
    }
    @media screen and (max-width: 760px){
        .custom-grid{
            grid-template-columns: 1fr;
        }
    }
</style>
