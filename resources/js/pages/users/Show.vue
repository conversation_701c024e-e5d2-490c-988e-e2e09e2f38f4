<template>
    <head>
        <title>ShopBoss - {{title}}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="title" />

        <div class="d-flex justify-content-between align-items-center mb-3">
            <PageLocation :items="pageLocation"/>
            <Link :href="route('user.create')" class="btn btn-primary">New User</Link>
        </div>

        <div class="card">
            <div class="card-body p-4">
                <h4 class="text-center">User Info</h4>
                <hr>
                <form @submit.prevent="user.put(route('user.update',{'user': props.user.id}))">
                    <div class="custom-grid">
                        <InputComponent
                            label="Name"
                            iconClass="bx bx-user"
                            v-model="user.name"
                            :error="user.errors.name"
                            placeholder="Name"
                            :required="true"
                        />

                        <InputComponent
                            label="Email"
                            iconClass="bx bx-envelope"
                            v-model="user.email"
                            :error="user.errors.email"
                            placeholder="Email"
                            :required="true"
                        />

                        <Select2Component
                            :data="['superadmin', 'admin', 'matco', 'customer success', 'customer support', 'sales']"
                            v-model="user.role"
                            label="Role"
                            iconClass="bx bx-shield-quarter"
                            :error="user.errors.role"
                            :required="true"
                        />

                        <PasswordComponent
                            label="Password"
                            placeholder="New Password"
                            iconClass="bx bx-key"
                            v-model="user.password"
                            :error="user.errors.password"
                        />
                    </div>

                    <div class="d-grid justify-content-center mt-3">
                        <ButtonComponent name="Update " class="primary" :processing="user.processing" :disabled="user.processing"/>
                    </div>
                </form>
            </div>
        </div>
    </DashboardLayout>

    <BaseAlertComponent v-if="user.wasSuccessful"
        :messageType="$page.props.flash.message ? 'success' : 'error'"
        :title="'User'"
        :message="$page.props.flash.message || $page.props.flash.error"
    />
</template>

<script setup>
    import { computed } from 'vue';
    import { usePage, Link, useForm } from '@inertiajs/vue3';

    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import BaseAlertComponent from '@/components/alerts/BaseAlertComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import InputComponent from '@/components/utils/InputComponent.vue';
    import PasswordComponent from '@/components/utils/PasswordComponent.vue';
    import Select2Component from '@/components/utils/Select2Component.vue'
    import ButtonComponent from '@/components/utils/ButtonComponent.vue'

    const { props } = usePage();
    const title = 'Update ' + props.user.name + ' Details'

    const pageLocation = [
        {
            name: 'Users',
            link: route('user.index')
        },
        {
            name: 'Update User',
            link: ''
        }
    ]

    const user = useForm({
        name: props.user.name,
        email: props.user.email,
        password: '',
        role: props.user.role
    })
</script>

<style scoped>
    .custom-grid{
        display: grid;
        grid-template-columns: 1fr 1fr;
        column-gap: 30px;
    }
    @media screen and (max-width: 760px){
        .custom-grid{
            grid-template-columns: 1fr;
        }
    }
</style>
