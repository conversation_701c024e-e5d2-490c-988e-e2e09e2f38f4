<template>
    <head>
        <title>ShopBoss - {{title}}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="title" />

        <div class="d-flex justify-content-end mb-3" v-if="userRole === 'superadmin'">
            <Link :href="route('user.create')" class="btn btn-primary">New User</Link>
        </div>

        <UsersTableComponent :users="props.users" :userRole="userRole"/>
    </DashboardLayout>
</template>

<script setup>
    import { usePage, Link } from '@inertiajs/vue3';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import UsersTableComponent from '@/components/users/UsersTableComponent.vue';

    const title = 'Admin Users'

    const { props } = usePage();

    const userRole = props.auth.user.role
</script>
