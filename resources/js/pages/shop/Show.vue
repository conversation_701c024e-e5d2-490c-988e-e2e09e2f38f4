<template>
    <head>
        <title>ShopBoss - Shop {{companyName}} Detail</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="companyName"/>
        <ModalsComponent />
        <ShopDetailsComponent/>

        <div class="custom-grid">
            <ReactiveReportComponent title="Company Comments" :columns="commentColumns" :route="commentDataTableRoute" :hideIfDataEmpty="true"/>

            <EmployeesTableComponent title="Employees" :columns="userColumns" :router="userDataTableRoute" :hideIfDataEmpty="true" :userRole="userRole"/>

            <ReactiveReportComponent title="Custom Reports" :columns="reportColumns" :route="reportDataTableRoute" :hideIfDataEmpty="true"/>

            <ReactiveReportComponent title="Payments" :columns="paymentColumns" :route="paymentDataTableRoute" :hideIfDataEmpty="true"/>

            <InvoicesTableComponent title="Invoices" :columns="invoiceColumns" :route="invoiceDataTableRoute" :hideIfDataEmpty="true"/>

            <EpicorTableComponent title="Epicor Logins" :suppliers="props.epicorSuppliers" :hideIfDataEmpty="true" :userRole="userRole"/>

            <CardknoxTableComponent title="Cardknox Devices" :suppliers="props.cardknoxDevices" :hideIfDataEmpty="true" :userRole="userRole"/>

            <Payments360Component :hideIfDataEmpty="true" :userRole="userRole"/>
         </div>
    </DashboardLayout>
</template>

<script setup>
    import { ref } from 'vue';
    import { usePage } from '@inertiajs/vue3';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import ShopDetailsComponent from '@/components/shop/ShopDetailsComponent.vue';
    import ModalsComponent from '@/components/shop/ModalsComponent.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import EpicorTableComponent from '@/components/shop/EpicorTableComponent.vue';
    import CardknoxTableComponent  from '@/components/shop/CardknoxTableComponent.vue';
    import Payments360Component  from '@/components/shop/Payments360Component.vue';
    import InvoicesTableComponent from '@/components/shop/InvoicesTableComponent.vue';
    import EmployeesTableComponent from '@/components/shop/EmployeesTableComponent.vue';
    import { formatDate } from '@/utils/utils.js'

    const { props } = usePage();

    const userRole = props.auth.user.role

    const shopid = props.companyDetails.shopid
    const companyName = props.companyDetails.companyName

    const commentDataTableRoute = "/dashboard/shop/comments/datatable/" + shopid;
    const commentColumns = ref(
        [
            {data: 'comment', name: 'comment', label: 'Comment'},
            {data: 'ts', name: 'ts', label: 'Date', order: 'desc', maxWidth: 90,
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.ts ? formatDate(row.ts) : '';
                    }
                    return row.ts || '';
                }
            },
        ]
    );

    const userDataTableRoute = "/dashboard/shop/users/datatable/" + shopid;
    const userColumns = ref(
        [
            {data: 'employee', name: 'employee', label: 'Login'},
            {data: 'mode', name: 'mode', label: 'Mode'},
            {data: 'jobdesc', name: 'jobdesc', label: 'Position'},
        ]
    );

    const reportDataTableRoute = "/dashboard/shop/reports/datatable/" + shopid;
    const reportColumns = ref(
        [
            {data: 'name', name: 'name', label: 'Report'},
            {data: 'ts', name: 'ts', label: 'Date', order: 'desc', maxWidth: 90,
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.ts ? formatDate(row.ts) : '';
                    }
                    return row.ts || '';
                }
            },
        ]
    );

    const paymentDataTableRoute = "/dashboard/shop/payments/datatable/" + shopid;
    const paymentColumns = ref(
        [
            {data: 'tracenumber', name: 'tracenumber', label: 'Transaction ID'},
            {data: 'amount', name: 'amount', label: 'Amount'},
            {data: 'approvalcode', name: 'approvalcode', label: 'Approval Code'},
            {data: 'paymentdate', name: 'paymentdate', label: 'Date', order: 'desc', maxWidth: 90,
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.paymentdate ? formatDate(row.paymentdate) : '';
                    }
                    return row.paymentdate || '';
                }
            },
        ]
    );

    const invoiceDataTableRoute = "/dashboard/shop/invoices/datatable/" + shopid;
    const invoiceColumns = ref(
        [
            {data: 'id', name: 'id', label: 'Invoice ID'},
            {data: 'amt', name: 'amt', label: 'Amount'},
            {data: 'invoicedate', name: 'invoicedate', label: 'Date', order: 'desc',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.invoicedate ? formatDate(row.invoicedate) : '';
                    }
                    return row.invoicedate || '';
                }
            },
        ]
    );
</script>

<style scoped>
    .custom-grid {
        display: grid;
        grid-gap: 20px;
        grid-template-columns: 1fr 1fr;
        margin-top: 50px;
    }

    @media only screen and (max-width: 1800px) {
        .custom-grid {
            display: block;
        }
    }
</style>
