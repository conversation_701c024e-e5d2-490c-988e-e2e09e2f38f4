<template>
    <head>
        <title>ShopBoss - {{title}}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="title" class="mb-4"/>


        <form @submit.prevent="newShop.post(route('new-shop.store'))">
            <div class="card">
                <div class="card-body p-4">
                    <h5 class="text-center">Shop Contact Information</h5>
                    <hr>

                    <div class="custom-grid mt-4">
                        <SalesRepSelect2Component
                            :data="props.salesRep"
                            v-model="newShop.salesRep"
                            label="Sales Rep"
                            iconClass="bx bx-user"
                            :error="newShop.errors.salesRep"
                        />

                        <InputComponent
                            v-model="newShop.name"
                            placeholder="Shop Name"
                            label="Shop Name"
                            iconClass="bx bx-store"
                            :error="newShop.errors.name"
                        />

                        <InputComponent
                            v-model="newShop.firstName"
                            placeholder="First Name"
                            label="First Name"
                            iconClass="bx bx-user"
                            :error="newShop.errors.firstName"
                        />

                        <InputComponent
                            v-model="newShop.lastName"
                            placeholder="Last Name"
                            label="Last Name"
                            iconClass="bx bx-user"
                            :error="newShop.errors.lastName"
                        />

                        <CustomInputComponent2 label="Username" inputId="username" iconClass="bx bx-user-circle" :error="newShop.errors.username">
                            <template #input>
                                <input type="text" v-model="newShop.username" class="form-control" id="username" placeholder="Username" autocomplete="do-not-autofill">
                                <span class="btn btn-secondary" @click="suggestUsername">
                                    Suggest
                                </span>
                            </template>
                        </CustomInputComponent2>

                        <GeneratedPasswordComponent
                            v-model="newShop.password"
                            label="Password"
                            :error="newShop.errors.password"
                        />

                        <InputComponent
                            v-model="newShop.email"
                            placeholder="Shop Email"
                            label="Shop Email"
                            iconClass="bx bx-envelope"
                            type="email"
                            :error="newShop.errors.email"
                        />

                        <InputComponent
                            v-model="newShop.phone"
                            placeholder="Phone"
                            label="Phone"
                            iconClass="bx bx-phone"
                            type="phone"
                            :error="newShop.errors.phone"
                            @input="formatPhoneNumber"
                        />

                        <InputComponent
                            v-model="newShop.address"
                            placeholder="Shop Address"
                            label="Shop Address"
                            iconClass="bx bx-home"
                            :error="newShop.errors.address"
                        />

                        <SelectTwoComponent
                            :data="['United States', 'Canada']"
                            v-model="newShop.country"
                            label="Country"
                            iconClass="bx bx-flag"
                            :error="newShop.errors.country"
                        />

                        <VLSelect2Component
                            :data="timeZones"
                            v-model="newShop.timeZone"
                            label="Time Zone"
                            iconClass="bx bx-time"
                            :error="newShop.errors.timeZone"
                        />

                        <InputComponent
                            v-model="newShop.zip"
                            placeholder="Zip/Postal Code"
                            label="Zip/Postal Code"
                            iconClass="bx bx-map"
                            :error="newShop.errors.zip"
                        />

                        <InputComponent
                            v-model="newShop.city"
                            placeholder="City"
                            label="City"
                            iconClass="bx bx-building"
                            :error="newShop.errors.city"
                        />

                        <VLSelect2Component
                            :data="statesProvinces[newShop.country]"
                            v-model="newShop.state"
                            label="State"
                            iconClass="bx bx-map-pin"
                            :error="newShop.errors.state"
                            :key="newShop.country"
                        />
                    </div>

                    <h5 class="text-center mt-4">Shop Settings</h5>
                    <hr>

                    <div class="custom-grid mt-4">
                        <VLSelect2Component
                            :data="shopTypes"
                            v-model="newShop.type"
                            label="Shop Type"
                            iconClass="bx bx-store-alt"
                            :error="newShop.errors.type"
                        />

                        <InputComponent
                            v-model="newShop.laborRate"
                            placeholder="Labor Rate"
                            label="Labor Rate"
                            iconClass="bx bx-dollar"
                            :error="newShop.errors.laborRate"
                            :type="'number'"
                        />

                        <CustomInputComponent label="Parts Tax Rate" inputId="partsTaxRate" iconClass="%" :error="newShop.errors.partsTaxRate">
                            <template #input>
                                    <input class="form-control" type="text" v-model="newShop.partsTaxRate">
                            </template>
                        </CustomInputComponent>

                        <CustomInputComponent label="Labor Tax Rate" inputId="laborsTaxRate" iconClass="%" :error="newShop.errors.laborTaxRate">
                            <template #input>
                                    <input class="form-control" type="text" v-model="newShop.laborTaxRate">
                            </template>
                        </CustomInputComponent>

                        <CustomInputComponent label="Sublet Tax Rate" inputId="subletTaxRate" iconClass="%" :error="newShop.errors.subletTaxRate">
                            <template #input>
                                    <input class="form-control" type="text" v-model="newShop.subletTaxRate">
                            </template>
                        </CustomInputComponent>

                        <InputComponent
                            v-model="newShop.includeTestData"
                            placeholder="Include Test Data"
                            label="Include Test Data"
                            iconClass="bx bx-checkbox"
                            :error="newShop.errors.includeTestDate"
                            :type="'checkbox'"
                        />

                        <InputComponent
                            v-model="newShop.matco"
                            placeholder="Matco"
                            label="Matco"
                            iconClass="bx bx-checkbox"
                            :error="newShop.errors.matco"
                            :type="'checkbox'"
                        />
                    </div>

                    <h5 class="text-center mt-4">Misc Options</h5>
                    <hr>

                    <div class="custom-grid">
                        <VLSelect2Component
                            :data="referralSources"
                            v-model="newShop.referralSource"
                            label="Referral Source"
                            iconClass="bx bx-share-alt"
                            :error="newShop.errors.referralSource"
                        />

                        <InputComponent
                            v-model="newShop.referralCode"
                            placeholder="Referral Code"
                            label="Referral Code"
                            iconClass="bx bx-link"
                            :error="newShop.errors.referralCode"
                        />

                        <PasswordComponent
                            v-model="newShop.salesRepPassword"
                            label="Sales Rep Password"
                            :error="newShop.errors.salesRepPassword"

                        />

                        <VLSelect2Component
                            :data="packages"
                            v-model="newShop.package"
                            label="Package"
                            iconClass="bx bx-package"
                            :error="newShop.errors.package"
                        />
                    </div>


                    <div class="button-trial">
                        <ButtonComponent name="Create Account/Trial" class="primary" :processing="newShop.processing"/>
                        <h5 class="mt-2">By clicking this button you are creating a new account in the Shop Boss system</h5>
                    </div>
                </div>
            </div>
        </form>


        <BaseAlertComponent v-if="newShop.wasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Trial Shop'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </DashboardLayout>
</template>

<script setup>
    import { ref, onMounted, watch } from 'vue';
    import { usePage, router, useForm } from '@inertiajs/vue3';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import SalesRepSelect2Component from '@/components/shop/SalesRepSelect2Component.vue'
    import VLSelect2Component from '@/components/shop/VLSelect2Component.vue'
    import SelectTwoComponent from '@/components/utils/Select2Component.vue'
    import InputComponent from '@/components/utils/InputComponent.vue'
    import CustomInputComponent from '@/components/shop/CustomInputComponent.vue'
    import CustomInputComponent2 from '@/components/utils/CustomInputComponent.vue'
    import ButtonComponent from '@/components/utils/ButtonComponent.vue'
    import PasswordComponent from '@/components/utils/PasswordComponent.vue'
    import GeneratedPasswordComponent from '@/components/shop/GeneratedPasswordComponent.vue'
    import axios from 'axios';
    import BaseAlertComponent from '@/components/alerts/BaseAlertComponent.vue';

    const title = "Shop Boss Create Account/Trial Form"

    const { props } = usePage();

    const newShop = useForm({
        salesRep: null,
        name: null,
        firstName: null,
        lastName: null,
        username: null,
        password: null,
        password_confirmation: null,
        email: null,
        phone: null,
        address: null,
        country: 'United States',
        timeZone: null,
        zip: null,
        city: null,
        state: null,
        type: null,
        laborRate: null,
        laborTaxRate: null,
        partsTaxRate: null,
        subletTaxRate: null,
        includeTestData: false,
        matco: false,
        referralSource: null,
        referralCode: null,
        salesRepPassword: null,
        package: "platinum"
    })

    const timeZones = [
        { value: "pst", label: "Pacific - PST" },
        { value: "azst", label: "Arizona - AZST" },
        { value: "mst", label: "Mountain - MST" },
        { value: "cst", label: "Central - CST" },
        { value: "est", label: "Eastern - EST" },
        { value: "ast", label: "Atlantic - AST" },
        { value: "astnd", label: "Atlantic no DST - ASTND" },
        { value: "akst", label: "Alaska - AKST" },
        { value: "hst", label: "Hawaii - HST" },
        { value: "eat", label: "East Africa - EAT" },
        { value: "chst", label: "Chamorro - CHST" }
    ]

    const statesProvinces = {
        "United States": [
            { value: "AL", label: "Alabama" },
            { value: "AK", label: "Alaska" },
            { value: "AZ", label: "Arizona" },
            { value: "AR", label: "Arkansas" },
            { value: "CA", label: "California" },
            { value: "CO", label: "Colorado" },
            { value: "CT", label: "Connecticut" },
            { value: "DE", label: "Delaware" },
            { value: "DC", label: "District Of Columbia" },
            { value: "FL", label: "Florida" },
            { value: "GA", label: "Georgia" },
            { value: "HI", label: "Hawaii" },
            { value: "ID", label: "Idaho" },
            { value: "IL", label: "Illinois" },
            { value: "IN", label: "Indiana" },
            { value: "IA", label: "Iowa" },
            { value: "KS", label: "Kansas" },
            { value: "KY", label: "Kentucky" },
            { value: "LA", label: "Louisiana" },
            { value: "ME", label: "Maine" },
            { value: "MD", label: "Maryland" },
            { value: "MA", label: "Massachusetts" },
            { value: "MI", label: "Michigan" },
            { value: "MN", label: "Minnesota" },
            { value: "MS", label: "Mississippi" },
            { value: "MO", label: "Missouri" },
            { value: "MT", label: "Montana" },
            { value: "NE", label: "Nebraska" },
            { value: "NV", label: "Nevada" },
            { value: "NH", label: "New Hampshire" },
            { value: "NJ", label: "New Jersey" },
            { value: "NM", label: "New Mexico" },
            { value: "NY", label: "New York" },
            { value: "NC", label: "North Carolina" },
            { value: "ND", label: "North Dakota" },
            { value: "OH", label: "Ohio" },
            { value: "OK", label: "Oklahoma" },
            { value: "OR", label: "Oregon" },
            { value: "PA", label: "Pennsylvania" },
            { value: "RI", label: "Rhode Island" },
            { value: "SC", label: "South Carolina" },
            { value: "SD", label: "South Dakota" },
            { value: "TN", label: "Tennessee" },
            { value: "TX", label: "Texas" },
            { value: "UT", label: "Utah" },
            { value: "VT", label: "Vermont" },
            { value: "VA", label: "Virginia" },
            { value: "WA", label: "Washington" },
            { value: "WV", label: "West Virginia" },
            { value: "WI", label: "Wisconsin" },
            { value: "WY", label: "Wyoming" },
        ],
        "Canada": [
            { value: "AB", label: "Alberta" },
            { value: "BC", label: "British Columbia" },
            { value: "MB", label: "Manitoba" },
            { value: "NB", label: "New Brunswick" },
            { value: "NL", label: "Newfoundland and Labrador" },
            { value: "NT", label: "Northwest Territories" },
            { value: "NS", label: "Nova Scotia" },
            { value: "NU", label: "Nunavut" },
            { value: "ON", label: "Ontario" },
            { value: "PE", label: "Prince Edward Island" },
            { value: "QC", label: "Quebec" },
            { value: "SK", label: "Saskatchewan" },
            { value: "YT", label: "Yukon" },
        ],
    }

    const shopTypes = [
        {value: "General Repair", label: "General Repair"},
        {value: "Tire Shop", label: "Tire Shop"},
        {value: "Transmission", label: "Transmission"},
        {value: "Specialist", label: "Specialist"},
        {value: "Fleet", label: "Fleet"},
        {value: "Custom", label: "Custom"},
        {value: "Mobile", label: "Mobile"},
        {value: "Truck", label: "Truck"},
        {value: "Other", label: "Other"},
    ]

    const referralSources = [
        { label: "AAPEX - Joe's Garage", value: "aapexjoesgarage" },
        { label: "Bing", value: "bing" },
        { label: "Capterra", value: "capterra" },
        { label: "Facebook", value: "facebook" },
        { label: "Garage Rehab", value: "garagerehab" },
        { label: "Google", value: "google" },
        { label: "NASCAR", value: "nascar" },
        { label: "Ratchet and Wrench", value: "ratchetandwrench" },
        { label: "Spotlight Video", value: "spotlight" },
        { label: "YouTube", value: "youtube" },
        { label: "Referral", value: "referral" },
        { label: "Other", value: "other" }
    ]

    const packages = [
        { label: "Silver", value: "silver" },
        { label: "Gold", value: "gold" },
        { label: "Platinum", value: "platinum" },
        { label: "Premier", value: "premier" },
    ]


    function suggestUsername(){
        let error = false;
        const requiredFields = {
            firstName: 'You need to fill first name!',
            lastName: 'You need to fill last name!'
        };

        Object.keys(requiredFields).forEach(field => {
            if (!newShop[field]) {
                newShop.errors[field] = requiredFields[field];
                error = true;
            } else {
                newShop.errors[field] = '';
            }
        });

        if ( !error ){
            axios.get(route('new-shop.generateUsername', {
                firstname: newShop.firstName,
                lastname: newShop.lastName
            }))
            .then(response => {
                newShop.username = response.data
            })
            .catch(error => {
                console.error('Error fetching username:', error);
            });
        }
    }

    function formatPhoneNumber(event) {
      let phoneNumber = event.target.value.replace(/\D/g, '');

      if (phoneNumber.length <= 3) {
        phoneNumber = `(${phoneNumber}`;
      } else if (phoneNumber.length <= 6) {
        phoneNumber = `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3)}`;
      } else {
        phoneNumber = `(${phoneNumber.slice(0, 3)}) ${phoneNumber.slice(3, 6)}-${phoneNumber.slice(6, 10)}`;
      }

      newShop.phone = phoneNumber;
    }

    function fetchCityAndState() {
        if ( !newShop.zip || newShop.zip.length !== 5 ) return;
        if ( newShop.country === 'Canada' ) return;

        axios.get(route('zip.decode', { zip: newShop.zip }))
            .then(response => {
                newShop.city = response.data.city;
                newShop.state = response.data.state;
            })
            .catch(error => {
                console.error('Error fetching sales reps:', error);
            });
    }

    watch(() => newShop.country, (newValue, oldValue) => {
        if (newValue !== oldValue) {
            fetchCityAndState();
        }

        if (newValue === 'Canada'){
            newShop.city = ''
            newShop.state = ''
            newShop.zip = ''
        }
    });

    watch(() => newShop.zip, (newValue, oldValue) => {
        if (newValue !== oldValue) {
            fetchCityAndState();
        }
    });
</script>


<style scoped>
    .custom-grid{
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }

    .button-trial{
        display: grid;
        align-items: center;
        justify-content: center;
        margin-top: 20px;
    }

    @media only screen and (max-width: 1400px){
        .custom-grid{
            grid-template-columns: 1fr;
        }
    }

    .button-trial h5{
        font-size: 14px;
    }
</style>
