<script setup lang="ts">
import { ref } from 'vue';
import NavbarComponent from '../components/navigation/NavbarComponent.vue';
import SidebarComponent from '../components/navigation/SidebarComponent.vue';

const isToggled = ref(false);
const isSidebarHovered = ref(false);

const toggleMenu = () => {
  isToggled.value = !isToggled.value;
  if (!isToggled.value) {
    isSidebarHovered.value = false;
  }
};

const handleMouseEnter = () => {
  if (isToggled.value) {
    isSidebarHovered.value = true;
  }
};

const handleMouseLeave = () => {
  if (isToggled.value) {
    isSidebarHovered.value = false;
  }
};
</script>

<template>
  <div>
    <div>
      <div :class="['wrapper', { toggled: isToggled, 'sidebar-hovered': isSidebarHovered }]">
        <NavbarComponent
            @toggleMenu="toggleMenu"
        />
        <SidebarComponent
          :isToggled="isToggled"
          :isSidebarHovered="isSidebarHovered"
          @toggleMenu="toggleMenu"
          @mouseEnter="handleMouseEnter"
          @mouseLeave="handleMouseLeave"
        />

        <div class="page-wrapper">
          <div class="page-content">
            <slot />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
