# ShopBoss Admin Panel

## Overview
ShopBoss Admin Panel is a comprehensive application designed for efficiently managing shop operations. Built with Laravel 11 and utilizing Inertia.js with Vue.js, the application offers a modern, responsive user experience for shop management, including detailed reporting, shop data management, and user administration.

## Features

### Login Page
- **Authentication Improvements:** Enhanced user security and login experience.

### Dashboard
- **Custom Charts:** Provides insightful shop production summaries.
- **Visualization Updates:** Various improvements to dashboard charts and usability.

### Shop Management
- **Shop Detail Page:** Comprehensive management of shop data and functionalities.
- **Widgets and Features:** Added new widgets and improved management features, including updates to shop details and packages.

### Reports Page
- **Report Organization:** Comprehensive list of reports including Accounting, Cavan, Luke, Sales, Retention, Other, and Custom Reports.
- **Reporting Enhancements:** Improved reporting functionalities and finalized various report types.

### Admin User Management
- **CRUD Operations:** Streamlined administration of admin user roles and permissions.

### Component Updates
- **Message Components:** Added and updated components for status and alert notifications, improving user feedback and communication.

### Code Quality
- **Code Enhancements:** Improved validation rules and overall code maintainability and performance.

## Technology Stack
- **Backend:** Laravel 11
- **Frontend:** Inertia.js with Vue.js

## Installation

1. **Clone the Repository:**

    ```bash
    git clone https://bitbucket.org/your-repository-url.git
    cd your-repository-folder
    ```

2. **Install Dependencies:**

    ```bash
    composer install
    npm install
    ```

3. **Set Up Environment:**

    ```bash
    cp .env.example .env
    ```

4. **Generate Application Key:**

    ```bash
    php artisan key:generate
    ```

5. **Update the .env file with the correct database connection settings.**


6. **Compile Assets:**

    ```bash
    npm run dev
    ```

7. **Start the Server:**

    ```bash
    php artisan serve
    ```

## Usage
Access the application via your browser at [http://localhost:8000](http://localhost:8000). Use the login page to authenticate and access the various features of the admin panel.

## Contributing

1. **Fork the Repository**
2. **Create a Feature Branch:**

    ```bash
    git checkout -b feature/your-feature-name
    ```

3. **Commit Your Changes:**

    ```bash
    git add .
    git commit -m "Add your commit message"
    ```

4. **Push to the Branch:**

    ```bash
    git push origin feature/your-feature-name
    ```

5. **Create a Pull Request**

## License
This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgements
- **Laravel 11:** For providing a powerful backend framework.
- **Inertia.js:** For enabling seamless server-side rendering with Vue.js.
- **Vue.js:** For a responsive and modern frontend framework.
