<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\SmsNumbers>
 */
class SmsNumbersFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id' => $this->faker->unique()->randomNumber(2),
            'shopid' => $this->faker->randomNumber(4),
            'smsnum' => $this->faker->randomNumber(2),
            'version' => '1',
        ];
    }
}
