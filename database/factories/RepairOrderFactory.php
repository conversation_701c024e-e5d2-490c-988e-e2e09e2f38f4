<?php

namespace Database\Factories\RepairOrders;

use App\Models\Company;
use App\Models\Customer;
use App\Models\RepairOrder;
use App\Models\Vehicle;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class RepairOrderFactory extends Factory
{
    protected $model = RepairOrder::class;

    public function definition(): array
    {
        return [
            'ROID' => $this->faker->randomNumber(9) + $this->faker->randomNumber(4),
            'Writer' => $this->faker->name(),
            'DateIn' => Carbon::now()->format('Y-m-d'),
            'TimeIn' => Carbon::now()->format('H:i:s'),
            'TaxRate' => $this->faker->randomNumber(2),
            'PurchaseOrderNumber' => $this->faker->text(5),
            'Customer' => $this->faker->name(),
            'VehInfo' => $this->faker->sentence(5),
            'vehyear' => $this->faker->year(),
            'vehmake' => $this->faker->word(),
            'vehmodel' => $this->faker->word(),
            'vehlicense' => $this->faker->text(5),
            'vehstate' => $this->faker->text(5),
            'VehLicNum' => $this->faker->text(5),
            'WrittenBy' => $this->faker->randomNumber(),
            'Status' => '1INSPECTION',
            'StatusDate' => Carbon::now()->format('Y-m-d'),
            'TotalLbrHrs' => $this->faker->randomNumber(2),
            'TotalLbr' => $this->faker->randomNumber(2),
            'TotalPrts' => $this->faker->randomNumber(2),
            'TotalSublet' => $this->faker->randomNumber(2),
            'TotalRO' => $this->faker->randomNumber(2),
            'CustomerAddress' => $this->faker->address(),
            'CustomerCSZ' => $this->faker->text(5),
            'CustomerPhone' => '**********',
            'customercity' => $this->faker->city(),
            'customerstate' => $this->faker->text(5),
            'customerzip' => $this->faker->postcode(),
            'VehicleMiles' => (string) $this->faker->randomNumber(5),
            'MilesOut' => (string) $this->faker->randomNumber(5),
            'Vin' => $this->faker->text(5),
            'CustomerWork' => $this->faker->text(5),
            'MajorComplaint' => $this->faker->text(5),
            'DatePromised' => Carbon::now(),
            'Comments' => $this->faker->sentence(10),
            'DiscountAmt' => $this->faker->randomNumber(2),
            'DiscountPercent' => $this->faker->randomNumber(2),
            'WarrMos' => $this->faker->text(5),
            'WarrMiles' => $this->faker->text(5),
            'SalesTax' => $this->faker->randomNumber(2),
            'PartsCost' => $this->faker->randomNumber(2),
            'ROType' => $this->faker->text(5),
            'VehEngine' => $this->faker->text(5),
            'Cyl' => $this->faker->text(5),
            'VehTrans' => $this->faker->text(5),
            'HowPaid' => $this->faker->text(5),
            'AmtPaid1' => $this->faker->randomNumber(2),
            'CheckNum1' => $this->faker->text(5),
            'HowPaid2' => $this->faker->text(5),
            'AmtPaid2' => $this->faker->randomNumber(2),
            'CheckNum2' => $this->faker->text(5),
            'EstimateAmt' => $this->faker->randomNumber(2),
            'NoFollow' => $this->faker->text(5),
            'AccountPaid' => $this->faker->text(5),
            'HazardousWaste' => $this->faker->randomNumber(2),
            'Source' => $this->faker->text(5),
            'Refsource' => $this->faker->text(5),
            'Rev1Amt' => $this->faker->randomNumber(2),
            'Rev1Date' => $this->faker->text(5),
            'Rev1Phone' => '**********',
            'Rev1Time' => $this->faker->text(5),
            'Rev1By' => $this->faker->text(5),
            'Rev2Amt' => $this->faker->randomNumber(2),
            'Rev2Date' => $this->faker->text(5),
            'Rev2Phone' => '**********',
            'Rev2Time' => $this->faker->text(5),
            'Rev2By' => $this->faker->text(5),
            'CellPhone' => '**********',
            'storagefee' => $this->faker->randomNumber(2),
            'cellprovider' => $this->faker->text(5),
            'UserFee1' => $this->faker->randomNumber(2),
            'UserFee2' => $this->faker->randomNumber(2),
            'UserFee3' => $this->faker->randomNumber(2),
            'userfee1label' => 'Fake Label 1',
            'userfee2label' => 'Fake Label 2',
            'userfee3label' => 'Fake Label 3',
            'UserFee1amount' => $this->faker->randomNumber(2),
            'UserFee2amount' => $this->faker->randomNumber(2),
            'UserFee3amount' => $this->faker->randomNumber(2),
            'userfee1percent' => $this->faker->randomNumber(2),
            'userfee2percent' => $this->faker->randomNumber(2),
            'userfee3percent' => $this->faker->randomNumber(2),
            'userfee1type' => $this->faker->randomElement(['%', '$']),
            'userfee2type' => $this->faker->randomElement(['%', '$']),
            'userfee3type' => $this->faker->randomElement(['%', '$']),
            'LastFirst' => $this->faker->name(),
            'customerfirst' => $this->faker->text(5),
            'customerlast' => $this->faker->text(5),
            'DriveType' => $this->faker->text(5),
            'TotalFees' => $this->faker->randomNumber(2),
            'Fax' => $this->faker->text(5),
            'Subtotal' => $this->faker->randomNumber(2),
            'CB' => $this->faker->text(5),
            'DateInspection' => Carbon::now(),
            'DateAuthorization' => Carbon::now(),
            'DateParts' => Carbon::now(),
            'DateWork' => Carbon::now(),
            'DateInProcess' => Carbon::now(),
            'DateHold' => Carbon::now(),
            'DateFinal' => Carbon::now(),
            'DateDelivered' => Carbon::now(),
            'DateClosed' => Carbon::now(),
            'OrigRO' => $this->faker->randomNumber(2),
            'OrigTech' => $this->faker->name(),
            'PartsOrdered' => $this->faker->text(5),
            'FinalDate' => Carbon::now(),
            'Exported' => $this->faker->text(5),
            'LaborTaxRate' => $this->faker->randomNumber(2),
            'SubletTaxRate' => $this->faker->randomNumber(2),
            'complainttable' => $this->faker->text(5),
            'gp' => $this->faker->randomNumber(2),
            'contact' => $this->faker->text(5),
            'balance' => $this->faker->randomNumber(2),
            'fleetno' => $this->faker->text(5),
            'email' => $this->faker->unique()->safeEmail(),
            'recommendedrepairs' => $this->faker->text(5),
            'customvehicle1' => $this->faker->text(5),
            'customvehicle2' => $this->faker->text(5),
            'customvehicle3' => $this->faker->text(5),
            'customvehicle4' => $this->faker->text(5),
            'customvehicle5' => $this->faker->text(5),
            'customvehicle6' => $this->faker->text(5),
            'customvehicle7' => $this->faker->text(5),
            'customvehicle8' => $this->faker->text(5),
            'customvehicle1label' => $this->faker->text(5),
            'customvehicle2label' => $this->faker->text(5),
            'customvehicle3label' => $this->faker->text(5),
            'customvehicle4label' => $this->faker->text(5),
            'customvehicle5label' => $this->faker->text(5),
            'customvehicle6label' => $this->faker->text(5),
            'customvehicle7label' => $this->faker->text(5),
            'customvehicle8label' => $this->faker->text(5),
            'tagnumber' => $this->faker->text(5),
            'qb' => $this->faker->text(5),
            'rodisc' => $this->faker->text(5),
            'warrdisc' => $this->faker->text(5),
            'datetimepromised' => $this->faker->text(5),
            'spousename' => $this->faker->name(gender: 'F'),
            'spousework' => $this->faker->text(5),
            'spousecell' => $this->faker->text(5),
            'overridestatusdate' => $this->faker->text(5),
            'tirepressureinlf' => $this->faker->text(5),
            'tirepressureinrf' => $this->faker->text(5),
            'tirepressureinlr' => $this->faker->text(5),
            'tirepressureinrr' => $this->faker->text(5),
            'tirepressureoutlf' => $this->faker->text(5),
            'tirepressureoutrf' => $this->faker->text(5),
            'tirepressureoutlr' => $this->faker->text(5),
            'tirepressureoutrr' => $this->faker->text(5),
            'treaddepthlf' => $this->faker->text(5),
            'treaddepthrf' => $this->faker->text(5),
            'treaddepthlr' => $this->faker->text(5),
            'treaddepthrr' => $this->faker->text(5),
            'discounttaxable' => $this->faker->text(5),
            'ponumber' => $this->faker->text(5),
            'origshopid' => $this->faker->text(5),
            'inspectioncomplete' => Carbon::now(),
            'invoiceemailed' => Carbon::now(),
            'estimateemailed' => Carbon::now(),
            'inspectionemailed' => Carbon::now(),
            'updatesent' => Carbon::now(),
            'coupon' => $this->faker->text(5),
            'taxpartsprice' => $this->faker->text(5),
            'waiter' => $this->faker->text(5),
            'DefaultTech' => $this->faker->name(),
            'pph' => $this->faker->randomNumber(2),
            'canadiantax' => $this->faker->text(5),
            'signed' => $this->faker->text(5),
            'recall' => $this->faker->text(5),
            'ts' => Carbon::now(),

            'shopid' => Company::factory(),
            'CustomerID' => Customer::factory(),
            'VehID' => Vehicle::factory(),
        ];
    }
}
