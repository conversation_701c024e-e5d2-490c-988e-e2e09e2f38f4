<?php

namespace Database\Factories\Shared;

use App\Models\Company;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class CompanyFactory extends Factory
{
    protected $model = Company::class;

    public function definition(): array
    {
        $companyName = $this->faker->randomElement([
            'Flux Capacitor Tech',
            'McFly Innovations',
            'Twin Pines Labs',
            'Docs Time Travel Solutions',
            'Hill Valley Enterprises',
            'Hoverboard Dynamics LLC',
            '88 MPH Technologies',
            'BiffCo Enterprises',
            'Plutonium Supplies Inc.',
            'DeLorean Motor Experiences',
        ]);

        return [
            'shopid' => (string) $this->faker->unique()->randomNumber(5, true),
            'CompanyName' => $companyName,
            'CompanyAddress' => $this->faker->randomElement([
                '88 Twin Pines Mall Blvd',
                '1640 Riverside Drive',
                '100 Hill Valley High St',
                '12 Lone Pine Lane',
                '1955 Lyon Estates Ave',
                '2015 McFly Ct',
                '1985 DeLorean Drive',
                '1955 Biffs Casino Rd',
                '1985 Clock Tower Square',
                '1885 Hill Valley Ranch Rd',
            ]),

            'CompanyCity' => 'Hill Valley',
            'CompanyState' => 'CA',
            'CompanyCountry' => 'United States',
            'CompanyZip' => '91905',
            'CompanyPhone' => '916'.$this->faker->numberBetween(1000000, 9999999),
            'CompanyFax' => '916'.$this->faker->numberBetween(1000000, 9999999),
            'CompanyURL' => 'http://'.strtolower(str_replace(' ', '-', $companyName)).'.com',
            'CompanyEmail' => strtolower(str_replace(' ', '-', $companyName)).'@example.com',
            'BarNo' => $this->faker->numerify('#####'),
            'EPANo' => $this->faker->numerify('#####'),
            'EinNo' => $this->faker->numerify('#####'),
            'HourlyRate' => $this->faker->randomFloat(4, 10, 80),
            'Hourlyrate2' => $this->faker->randomFloat(4, 10, 80),
            'hourlyrate3' => $this->faker->randomFloat(4, 10, 80),
            'hourlyrate4' => $this->faker->randomFloat(4, 10, 80),
            'hourlyrate5' => $this->faker->randomFloat(4, 10, 80),
            'hourlyrate6' => $this->faker->randomFloat(4, 10, 80),
            'hourlyrate7' => $this->faker->randomFloat(4, 10, 80),
            'hourlyrate8' => $this->faker->randomFloat(4, 10, 80),
            'hourlyrate9' => $this->faker->randomFloat(4, 10, 80),
            'hourlyrate10' => $this->faker->randomFloat(4, 10, 80),
            'hourlyrate1label' => 'Standard Rate',
            'AdminPassword' => $this->faker->password(5),
            'UserPassword' => $this->faker->password(5),
            'ShopMgr' => $this->faker->randomElement(['yes', 'no']),
            'installcode' => $this->faker->randomElement(['yes', 'no']),
            'DefaultTaxRate' => $this->faker->randomFloat(2, 1, 20),
            'DefaultLaborTaxRate' => $this->faker->randomFloat(2, 1, 20),
            'DefaultSubletTaxRate' => $this->faker->randomFloat(2, 1, 20),
            'DefaultWarrMos' => $this->faker->numberBetween(1, 24),
            'DefaultWarrMiles' => '50000',
            'UserFee1' => $this->faker->randomElement([
                'Shop Supplies Fee',
                'Environmental Disposal Fee',
                'Diagnostic Fee',
            ]),
            'UserFee2' => $this->faker->randomElement([
                'Shop Supplies Fee',
                'Environmental Disposal Fee',
                'Diagnostic Fee',
            ]),
            'UserFee3' => $this->faker->randomElement([
                'Shop Supplies Fee',
                'Environmental Disposal Fee',
                'Diagnostic Fee',
            ]),
            'userfee1type' => $this->faker->randomElement(['%', '$']),
            'userfee2type' => $this->faker->randomElement(['%', '$']),
            'userfee3type' => $this->faker->randomElement(['%', '$']),
            'UserFee1amount' => $this->faker->randomFloat(2, 10, 80),
            'UserFee2amount' => $this->faker->randomFloat(2, 10, 80),
            'UserFee3amount' => $this->faker->randomFloat(2, 10, 80),
            'userfee1percent' => $this->faker->randomFloat(2, 10, 80),
            'userfee2percent' => $this->faker->randomFloat(2, 10, 80),
            'userfee3percent' => $this->faker->randomFloat(2, 10, 80),
            'userfee1max' => $this->faker->randomFloat(2, 10, 80),
            'userfee2max' => $this->faker->randomFloat(2, 10, 80),
            'userfee3max' => $this->faker->randomFloat(2, 10, 80),
            'userfee1taxable' => $this->faker->randomElement(['Taxable', 'Non-Taxable']),
            'userfee2taxable' => $this->faker->randomElement(['Taxable', 'Non-Taxable']),
            'userfee3taxable' => $this->faker->randomElement(['Taxable', 'Non-Taxable']),
            'userfee1applyon' => $this->faker->randomElement(['parts', 'labor', 'all']),
            'userfee2applyon' => $this->faker->randomElement(['parts', 'labor', 'all']),
            'userfee3applyon' => $this->faker->randomElement(['parts', 'labor', 'all']),
            'HazardousWaste' => $this->faker->randomFloat(2, 10, 80),
            'CustomUserField1' => $this->faker->words(5, true),
            'CustomUserField2' => $this->faker->words(5, true),
            'CustomUserField3' => $this->faker->words(5, true),
            'StorageFee' => $this->faker->randomFloat(2, 10, 80),
            'itemizefeesprintedro' => $this->faker->randomElement(['yes', 'no']),
            'RequireBalanceRO' => $this->faker->randomElement(['yes', 'no']),
            'RequirePayments' => $this->faker->randomElement(['yes', 'no']),
            'RequireBalanceCT' => $this->faker->randomElement(['yes', 'no']),
            'RequireSource' => $this->faker->randomElement(['yes', 'no']),
            'RequireServiceWriter' => $this->faker->randomElement(['yes', 'no']),
            'RequireOutMileage' => $this->faker->randomElement(['yes', 'no']),
            'requiremileagestatus' => 'Final',
            'RequireComments' => $this->faker->randomElement(['yes', 'no']),
            'RequireComplaint' => $this->faker->randomElement(['yes', 'no']),
            'SatHours' => '4',
            'SunHours' => '0',
            'DaysOpen' => '5',
            'UsePartsMatrix' => $this->faker->randomElement(['yes', 'no']),
            'CompanyID' => $this->faker->randomNumber(),
            'DailyHours' => '8',
            'pswarrdays' => '0',
            'ReOpenRO' => $this->faker->text(5),
            'definvmsgemail' => $this->faker->text(5),
            'showinstockparts' => $this->faker->randomElement(['yes', 'no']),
            'MailPassword' => $this->faker->randomElement(['yes', 'no']),
            'tm' => 'Donate',
            'usestddiscounts' => $this->faker->text(5),
            'rodisclosure' => $this->faker->text(5),
            'rowarrdisclosure' => $this->faker->text(5),
            'psdisclosure' => $this->faker->text(5),
            'status' => 'ACTIVE',
            'contact' => 'Shop Contact',
            'lgcookie' => $this->faker->text(5),
            'alldatausername' => $this->faker->text(5),
            'alldatapassword' => $this->faker->text(5),
            'estguide' => $this->faker->randomElement(['Yes', 'No']),
            'showpayments' => $this->faker->randomElement(['yes', 'no']),
            'printtechstory' => $this->faker->randomElement(['Yes', 'No']),
            'defaultpartsearch' => 'Both',
            'logo' => 'shopboss-logo-nut.png',
            'affiliateid' => $this->faker->text(5),
            'showlaborhoursonro' => $this->faker->randomElement(['yes', 'no']),
            'companyform' => 'LLC',
            'fiscalmonth' => 'January',
            'accountingmethod' => 'Cash',
            'accountingactivated' => $this->faker->randomElement(['Yes', 'No']),
            'partsdiscountonro' => $this->faker->randomElement(['yes', 'no']),
            'vehiclefield1label' => 'Vehicle Field Label',
            'vehiclefield2label' => 'Vehicle Field Label',
            'vehiclefield3label' => 'Vehicle Field Label',
            'vehiclefield4label' => 'Vehicle Field Label',
            'vehiclefield5label' => 'Vehicle Field Label',
            'vehiclefield6label' => 'Vehicle Field Label',
            'vehiclefield7label' => 'Vehicle Field Label',
            'vehiclefield8label' => 'Vehicle Field Label',
            'milesinlabel' => 'Miles In',
            'milesoutlabel' => 'Miles Out',
            'quotelabel' => 'Quote',
            'replacerowithtag' => $this->faker->randomElement(['yes', 'no']),
            'quotedisclosure' => 'Thank you for the opportunity to earn your business.  Please note that all prices, labor rates and labor times are accurate as of the date of this quote, but are subject to change.',
            'customropage' => $this->faker->randomElement(['yes', 'no']),
            'feesonquote' => $this->faker->randomElement(['yes', 'no']),
            'billingaddress' => '9915 BUSINESSPARK AVENUE SUITE C',
            'billingcity' => 'SAN DIEGO',
            'billingstate' => 'CA',
            'last4' => '1234',
            'billingzip' => '92123',
            'cc' => $this->faker->text(5),
            'expdate' => $this->faker->text(5),
            'bankaccount' => $this->faker->text(5),
            'routing' => $this->faker->text(5),
            'blastname' => $this->faker->text(5),
            'bfirstname' => $this->faker->text(5),
            'datestarted' => Carbon::now(),
            'trialexpiration' => Carbon::now(),
            'birthdate' => $this->faker->text(5),
            'active' => $this->faker->text(5),
            'package' => $this->faker->text(5),
            'dateofacceptance' => Carbon::now(),
            'howpaying' => $this->faker->text(5),
            'freelimit' => $this->faker->randomNumber(),
            'defaultcalendar' => $this->faker->text(5),
            'internalemail' => $this->faker->text(5),
            'nextcontactdate' => Carbon::now(),
            'showpartsbuttons' => $this->faker->text(5),
            'printvitotals' => $this->faker->text(5),
            'defaulthistorysearch' => $this->faker->text(5),
            'ignorespaceslashdashpartsearch' => $this->faker->text(5),
            'lastpaymentdate' => Carbon::now(),
            'autoaddrecs' => $this->faker->text(5),
            'showropayments' => $this->faker->text(5),
            'useaccounting' => $this->faker->text(5),
            'invoicetitle' => $this->faker->text(5),
            'estimatetitle' => $this->faker->text(5),
            'tooltip' => $this->faker->text(5),
            'showinactiveemployees' => $this->faker->text(5),
            'merchantaccount' => $this->faker->text(5),
            'merchantid' => $this->faker->text(5),
            'merchantpassword' => $this->faker->text(5),
            'terminalid' => $this->faker->text(5),
            'paytoken' => $this->faker->text(5),
            'lat' => $this->faker->latitude(),
            'longitude' => $this->faker->longitude(),
            'merchantaccounttype' => $this->faker->text(5),
            'inspectionaspopup' => $this->faker->text(5),
            'chargeshopfeeson' => $this->faker->text(5),
            'partoverridecode' => $this->faker->text(5),
            'duplicate' => $this->faker->text(5),
            'alldatarepair' => $this->faker->text(5),
            'defaultinspectionvalue' => $this->faker->text(5),
            'initialdoa' => Carbon::now(),
            'printpopup' => $this->faker->text(5),
            'new' => $this->faker->text(5),
            'autoclockouttech' => $this->faker->text(5),
            'showtirepressure' => $this->faker->text(5),
            'requiremileagetoprintro' => $this->faker->text(5),
            'shopnotice' => $this->faker->text(5),
            'useim' => $this->faker->text(5),
            'requiretirepressure' => $this->faker->text(5),
            'shopblurb' => $this->faker->text(5),
            'requiretirepressuretoprintro' => $this->faker->text(5),
            'inventoryappreciation' => $this->faker->text(5),
            'requiretechclockout' => $this->faker->text(5),
            'carfaxlocation' => $this->faker->text(5),
            'showpartnumberonprintedro' => $this->faker->text(5),
            'showpartnumberonprintedps' => $this->faker->text(5),
            'printbar' => $this->faker->text(5),
            'esign' => $this->faker->text(5),
            'showinspectionratings' => $this->faker->text(5),
            'showgp' => $this->faker->text(5),
            'datesuspended' => Carbon::now(),
            'churn' => $this->faker->text(5),
            'churndate' => Carbon::now(),
            'whochurned' => $this->faker->text(5),
            'churnreason' => $this->faker->text(5),
            'showstatistics' => $this->faker->text(5),
            'deleteapptwhenconverting' => $this->faker->text(5),
            'lineitemcomplete' => $this->faker->text(5),
            'historical' => $this->faker->text(5),
            'printadvisorcomments' => $this->faker->text(5),
            'billingemail' => $this->faker->text(5),
            'newro' => $this->faker->text(5),
            'cookieexpire' => $this->faker->text(5),
            'showtimeclockonwipdata' => $this->faker->text(5),
            'showpaymentonwip' => $this->faker->text(5),
            'showemailestimateonwip' => $this->faker->text(5),
            'showemailinvoiceonwip' => $this->faker->text(5),
            'showinspectiononwip' => $this->faker->text(5),
            'showinpectionemailonwip' => $this->faker->text(5),
            'dispatch' => $this->faker->text(5),
            'prebuild' => $this->faker->text(5),
            'coupon' => $this->faker->text(5),
            'authnetclientkey' => $this->faker->text(5),
            'shopip' => $this->faker->text(5),
            'postpartstoaccounting' => $this->faker->text(5),
            'requireinspectiontofinal' => $this->faker->text(5),
            'flatprice' => $this->faker->randomFloat(2, 10, 80),
            'paymentdate' => $this->faker->text(5),
            'mmtype' => $this->faker->text(5),
            'showpricesintechmode' => $this->faker->text(5),
            'showpartscostonro' => $this->faker->text(5),
            'showlaborratesonro' => $this->faker->text(5),
            'alldataaccounttype' => $this->faker->text(5),
            'shopsource' => $this->faker->text(5),
            'repairstatusurl' => $this->faker->text(5),
            'overwriteqbtrans' => $this->faker->text(5),
            'showtechoninvoice' => $this->faker->text(5),
            'firsttime' => $this->faker->text(5),
            'printpayments' => $this->faker->text(5),
            'timezone' => Carbon::now(),
            'dvi' => $this->faker->text(5),
            'showgponwip' => $this->faker->text(5),
            'showorigshopid' => $this->faker->text(5),
            'vehissuespage' => $this->faker->text(5),
            'showonlyclosedonhistory' => $this->faker->text(5),
            'calendarlayout' => $this->faker->text(5),
            'referralcode' => $this->faker->text(5),
            'referredby' => $this->faker->text(5),
            'bankname' => $this->faker->text(5),
            'sortvi' => $this->faker->text(5),
            'nexpartusername' => $this->faker->text(5),
            'nexpartpassword' => $this->faker->text(5),
            'worldpac' => $this->faker->text(5),
            'apptreminder' => $this->faker->text(5),
            'taxonfees' => $this->faker->text(5),
            'enterpriseid' => $this->faker->randomNumber(),
            'hazwastetaxable' => $this->faker->text(5),
            'storagetaxable' => $this->faker->text(5),
            'fullscreenissues' => $this->faker->text(5),
            'usepartsregistry' => $this->faker->text(5),
            'useschmaint' => $this->faker->text(5),
            'showdeclined' => $this->faker->text(5),
            'facebook' => $this->faker->text(5),
            'facebookaddsb' => $this->faker->text(5),
            'startinspection' => $this->faker->text(5),
            'newpackagetype' => $this->faker->text(5),
            'usezipexplode' => $this->faker->text(5),
            'scrolltotalswindow' => $this->faker->text(5),
            'masterinterface' => $this->faker->text(5),
            'hst' => $this->faker->randomFloat(2, 10, 80),
            'pst' => $this->faker->randomFloat(2, 10, 80),
            'gst' => $this->faker->randomFloat(2, 10, 80),
            'qst' => $this->faker->randomFloat(2, 10, 80),
            'chargehst' => $this->faker->text(5),
            'chargepst' => $this->faker->text(5),
            'chargegst' => $this->faker->text(5),
            'chargeqst' => $this->faker->text(5),
            'hstapplyon' => $this->faker->text(5),
            'pstapplyon' => $this->faker->text(5),
            'gstapplyon' => $this->faker->text(5),
            'qstapplyon' => $this->faker->text(5),
            'updateinvonadd' => $this->faker->text(5),
            'autoshowta' => $this->faker->text(5),
            'showtextemail' => $this->faker->text(5),
            'showcarfaxonly' => $this->faker->text(5),
            'showtechoverhours' => $this->faker->text(5),
            'showsourceonprintedro' => $this->faker->text(5),
            'defaultrochild' => $this->faker->text(5),
            'userpreviousmileage' => $this->faker->text(5),
            'labortypeahead' => $this->faker->text(5),
            'labormatrixtype' => $this->faker->text(5),
            'showadvisoroninvoice' => $this->faker->text(5),
            'prepoppaymentreceiveacct' => $this->faker->text(5),
            'showpromiseonwip' => $this->faker->text(5),
            'readonly' => $this->faker->text(5),
            'multitechclock' => $this->faker->text(5),
            'firstlastonwip' => $this->faker->text(5),
            'sortwipbylastfirst' => $this->faker->text(5),
            'showaddressonschedule' => $this->faker->text(5),
            'schedulesendreminderdefault' => $this->faker->text(5),
            'copyemailtoshop' => $this->faker->text(5),
            'showcustphonewo' => $this->faker->text(5),
            'printcommlog' => $this->faker->text(5),
            'sourcerequired' => $this->faker->text(5),
            'showrevapps' => $this->faker->text(5),
            'requirerevapp' => $this->faker->text(5),
            'showinvoicenumber' => $this->faker->text(5),
            'calendardefault' => $this->faker->text(5),
            'dashexpiration' => $this->faker->text(5),
            'gponwiplist' => $this->faker->text(5),
            'cfpid' => $this->faker->text(5),
            'showwaiting' => $this->faker->text(5),
            'profitboost' => $this->faker->text(5),
            'demo' => $this->faker->text(5),
            'matco' => $this->faker->text(5),
            'protractor' => $this->faker->text(5),
            'showwtk' => $this->faker->text(5),
            'showvehcolor' => $this->faker->text(5),
            'showhrsonwip' => $this->faker->text(5),
            'invoicedimension' => $this->faker->text(5),
            'bluesnapid' => $this->faker->text(5),
            'showstatsonwip' => $this->faker->text(5),
            'inspectexpiration' => Carbon::now(),
            'showpartsoninvoice' => $this->faker->text(5),
            'conamereports' => $this->faker->text(5),
            'school' => $this->faker->text(5),
            'annual' => $this->faker->text(5),
            'showsigonwip' => $this->faker->text(5),
            'salesrep' => $this->faker->text(5),
            'showpcodeoninvoice' => $this->faker->text(5),
            'ts' => Carbon::now(),
            'bufferhours' => $this->faker->text(5),
            'newuidate' => $this->faker->text(5),
            'onboardinghours' => $this->faker->randomFloat(2, 10, 80),
        ];
    }

    public function shopboss(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                'shopid' => '6062',
            ];
        });
    }
}
