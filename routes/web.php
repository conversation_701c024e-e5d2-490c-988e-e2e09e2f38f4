<?php

use App\Http\Controllers\API\ZipController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\ResetPasswordController;
use App\Http\Controllers\Dashboard\DashbordChartController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\Reports\AccountingLinkShopsController;
use App\Http\Controllers\Reports\AccountingReportController;
use App\Http\Controllers\Reports\BandwithMessagesController;
use App\Http\Controllers\Reports\BillingController;
use App\Http\Controllers\Reports\BlueSnapController;
use App\Http\Controllers\Reports\ChurnController;
// Reports
use App\Http\Controllers\Reports\ChurnListRetentionController;
use App\Http\Controllers\Reports\ChurnRetentionController;
use App\Http\Controllers\Reports\ClawbackController;
use App\Http\Controllers\Reports\CustomReportListController;
use App\Http\Controllers\Reports\CustomReportsController;
use App\Http\Controllers\Reports\DailyTotalsController;
use App\Http\Controllers\Reports\DeclinedBlueSnapTransactionsController;
use App\Http\Controllers\Reports\DLCDashboardTrackingController;
use App\Http\Controllers\Reports\DVICompareController;
use App\Http\Controllers\Reports\DVIReportForSSShopUsersController;
use App\Http\Controllers\Reports\EmployeesController;
use App\Http\Controllers\Reports\IdealCustomerController;
use App\Http\Controllers\Reports\KPIController;
use App\Http\Controllers\Reports\LROChurnController;
use App\Http\Controllers\Reports\LyftController;
use App\Http\Controllers\Reports\MandrillMessagesController;
use App\Http\Controllers\Reports\MatcoChurnListController;
use App\Http\Controllers\Reports\MatcoScanListController;
use App\Http\Controllers\Reports\MotorExportController;
use App\Http\Controllers\Reports\MotorUsageController;
use App\Http\Controllers\Reports\MotorUsageWithProdemandController;
use App\Http\Controllers\Reports\PachageChangelogController;
use App\Http\Controllers\Reports\PachageUpgradesController;
use App\Http\Controllers\Reports\PPHInternalController;
use App\Http\Controllers\Reports\ReadOnlyShopsController;
use App\Http\Controllers\Reports\RODataController;
use App\Http\Controllers\Reports\SalesReportController;
use App\Http\Controllers\Reports\ShopCategoryController;
use App\Http\Controllers\Reports\ShopListController;
use App\Http\Controllers\Reports\SuspendedShopsListController;
use App\Http\Controllers\Reports\TrialShopsController;
use App\Http\Controllers\Reports\WeeklyTotalsController;
use App\Http\Controllers\Shop\APIKeyController;
use App\Http\Controllers\Shop\NewShopController;
use App\Http\Controllers\Shop\ShopController;
use App\Http\Controllers\User\UsersController;
use App\Http\Middleware\RoleMiddleware;
// API
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Route;

Route::get('/', [LoginController::class, 'login'])->name('login');

Route::post('/login', [LoginController::class, 'store'])->name('login.post');
Route::post('/logout', [LoginController::class, 'destroy'])->name('logout.post');
Route::inertia('/login', 'auth/Login')->name('login');

Route::get('/forgot-password', [ResetPasswordController::class, 'resetPassword'])->name('forgot-password');
Route::post('/forgot-password', [ResetPasswordController::class, 'sendEmail'])->name('forgot-password');
Route::get('/reset-password/{token}', [ResetPasswordController::class, 'resetForm'])->name('password.reset');
Route::post('/reset-password', [ResetPasswordController::class, 'resetHandler'])->name('password.update');

// AUTH ROUTES
Route::group(['middleware' => ['auth'], 'prefix' => '/dashboard'], function () {

    // Only superadmin and admin can access to this routes
    Route::group(['middleware' => RoleMiddleware::class.':superadmin,admin'], function () {
        // Dashbord
        Route::get('/', [DashboardController::class, 'index'])->name('dashboard.index');

        // DASHBOARD CHARTS
        Route::prefix('chart/details')->name('chart.details.')->group(function () {
            Route::get('/ro', [DashbordChartController::class, 'RepairOrderChartDetails'])->name('ro');
            Route::get('/accounts', [DashbordChartController::class, 'AccountsChartDetails'])->name('accounts');
            Route::get('/ss-shops', [DashbordChartController::class, 'ssShops'])->name('ss-shops');
            Route::get('/ms-shops', [DashbordChartController::class, 'msShops'])->name('ms-shops');
            Route::get('/aro', [DashbordChartController::class, 'ARO'])->name('aro');
            Route::get('/boss-pay', [DashbordChartController::class, 'bossPay'])->name('boss-pay');
        });

        // USERS
        Route::apiResource('user', UsersController::class)->only(['index', 'show', 'update', 'create', 'destroy', 'store']);

        // REPORTS
        Route::group(['prefix' => '/report'], function () {
            // Accounting Report
            Route::group(['prefix' => '/accounting'], function () {
                Route::get('/', [AccountingReportController::class, 'index'])->name('accounting.index');
                Route::get('/ms', [AccountingReportController::class, 'indexMS'])->name('accounting.ms.index');
                Route::get('datatable', [AccountingReportController::class, 'datatable'])->name('accounting.datatable');
                Route::get('/ms/datatable', [AccountingReportController::class, 'datatableMS'])->name('accounting.ms.datatable');
                // MATCO ACCOUNTING REPORT
                Route::get('/matco', [AccountingReportController::class, 'accountingMatco'])->name('accounting.matco.index');
                Route::get('/matco/datatable', [AccountingReportController::class, 'accountingMatcoDatatable'])->name('accounting.matco.datatable.index');
                Route::get('/matco/ms', [AccountingReportController::class, 'accountingMatcoMS'])->name('accounting.matco.ms.index');
                Route::get('/matco/ms/datatable', [AccountingReportController::class, 'accountingMatcoMSDatatable'])->name('accounting.matco.ms.datatable.index');
            });

            // BlueSnap ID Report
            Route::group(['prefix' => '/bluesnap'], function () {
                Route::get('/', [BlueSnapController::class, 'index'])->name('bluesnap.index');
                Route::get('/ms', [BlueSnapController::class, 'indexMS'])->name('bluesnap.ms.index');
                Route::get('datatable', [BlueSnapController::class, 'datatable'])->name('bluesnap.datatable');
                Route::get('/ms/datatable', [BlueSnapController::class, 'datatableMS'])->name('bluesnap.ms.datatable');
            });

            // Churn Report
            Route::group(['prefix' => '/churn'], function () {
                Route::get('/', [ChurnController::class, 'index'])->name('churn.index');
                Route::get('datatable', [ChurnController::class, 'datatable'])->name('churn.datatable');
                Route::get('{shopid}', [ChurnController::class, 'show'])->name('churn.show');
                Route::post('{shopid}', [ChurnController::class, 'store'])->name('churn.store');
            });

            // RO Data Report
            Route::group(['prefix' => '/ro/data'], function () {
                Route::get('/', [RODataController::class, 'index'])->name('ro.data.index');
                Route::get('datatable', [RODataController::class, 'datatable'])->name('ro.data.datatable');
            });

            // Daily Totals Report
            Route::group(['prefix' => '/daily/totals'], function () {
                Route::get('/', [DailyTotalsController::class, 'index'])->name('daily.totals.index');
                Route::get('datatable', [DailyTotalsController::class, 'datatable'])->name('daily.totals.datatable');
            });

            // Weekly Totals Report
            Route::group(['prefix' => '/weekly/totals'], function () {
                Route::get('/', [WeeklyTotalsController::class, 'index'])->name('weekly.totals.index');
                Route::get('datatable', [WeeklyTotalsController::class, 'datatable'])->name('weekly.totals.datatable');
            });

            // Ideal Customer Report
            Route::group(['prefix' => '/ideal/customer'], function () {
                Route::get('/', [IdealCustomerController::class, 'index'])->name('ideal.customer.index');
                Route::get('datatable', [IdealCustomerController::class, 'datatable'])->name('ideal.customer.datatable');
            });

            // Custom Report List
            Route::group(['prefix' => '/custom/report/list'], function () {
                Route::get('/', [CustomReportListController::class, 'index'])->name('custom.report.list.index');
                Route::get('datatable', [CustomReportListController::class, 'datatable'])->name('custom.report.list.datatable');
            });

            // Matco Churn List
            Route::group(['prefix' => '/matco/churn/list'], function () {
                Route::get('/', [MatcoChurnListController::class, 'index'])->name('matco.churn.list.index');
                Route::get('datatable', [MatcoChurnListController::class, 'datatable'])->name('matco.churn.list.datatable');
            });

            // Matco Scan List
            Route::group(['prefix' => '/matco/scan/list'], function () {
                Route::get('/', [MatcoScanListController::class, 'index'])->name('matco.scan.list.index');
                Route::get('datatable', [MatcoScanListController::class, 'datatable'])->name('matco.scan.list.datatable');
            });

            // Sales Report
            Route::group(['prefix' => '/sales'], function () {
                Route::get('/', [SalesReportController::class, 'index'])->name('sales.index');
                Route::get('datatable', [SalesReportController::class, 'datatable'])->name('sales.datatable');
            });

            // DVI Report - Not Completed
            Route::group(['prefix' => '/dvi/ss/users'], function () {
                Route::get('/', [DVIReportForSSShopUsersController::class, 'index'])->name('dvi.ss.users.index');
                Route::get('datatable', [DVIReportForSSShopUsersController::class, 'datatable'])->name('dvi.ss.users.datatable');
            });

            // Clawback Report
            Route::group(['prefix' => '/clawback'], function () {
                Route::get('/', [ClawbackController::class, 'index'])->name('clawback.index');
                Route::get('datatable', [ClawbackController::class, 'datatable'])->name('clawback.datatable');
                Route::get('{shopid}', [ClawbackController::class, 'show'])->name('clawback.show');
                Route::post('{shopid}', [ClawbackController::class, 'store'])->name('clawback.store');
            });

            // Package Changelog Report
            Route::group(['prefix' => '/package/changelog'], function () {
                Route::get('/', [PachageChangelogController::class, 'index'])->name('package.changelog.index');
                Route::get('datatable', [PachageChangelogController::class, 'datatable'])->name('package.changelog.datatable');
            });

            // Package Upgrades Report
            Route::group(['prefix' => '/package/upgrades'], function () {
                Route::get('/', [PachageUpgradesController::class, 'index'])->name('package.upgrades.index');
                Route::get('datatable', [PachageUpgradesController::class, 'datatable'])->name('package.upgrades.datatable');
            });

            // Trial Shops Report
            Route::group(['prefix' => '/trial/shops'], function () {
                Route::get('/', [TrialShopsController::class, 'index'])->name('trial.shops.index');
                Route::get('datatable', [TrialShopsController::class, 'datatable'])->name('trial.shops.datatable');
            });

            // DVI Comparison Report
            Route::group(['prefix' => '/dvi/compare'], function () {
                Route::get('/', [DVICompareController::class, 'index'])->name('dvi.compare.index');
                Route::get('datatable', [DVICompareController::class, 'datatable'])->name('dvi.compare.datatable');
            });

            // Motor Usage Report
            Route::group(['prefix' => '/motor/usage'], function () {
                Route::get('/', [MotorUsageController::class, 'index'])->name('motor.usage.index');
                Route::get('datatable', [MotorUsageController::class, 'datatable'])->name('motor.usage.datatable');
            });

            // Motor Export Report
            Route::group(['prefix' => '/motor/export'], function () {
                Route::get('/', [MotorExportController::class, 'index'])->name('motor.export.index');
                Route::get('datatable', [MotorExportController::class, 'datatable'])->name('motor.export.datatable');
            });

            // Accounting Link Shops Report
            Route::group(['prefix' => '/accounting/link/shops'], function () {
                Route::get('/', [AccountingLinkShopsController::class, 'index'])->name('accounting.link.shops.index');
                Route::get('datatable', [AccountingLinkShopsController::class, 'datatable'])->name('accounting.link.shops.datatable');
            });

            // Billing Report
            Route::group(['prefix' => '/billing'], function () {
                Route::get('/', [BillingController::class, 'index'])->name('billing.index');
                Route::get('datatable', [BillingController::class, 'datatable'])->name('billing.datatable');
            });

            // Lyft Report
            Route::group(['prefix' => '/lyft'], function () {
                Route::get('/', [LyftController::class, 'index'])->name('lyft.index');
                Route::get('datatable', [LyftController::class, 'datatable'])->name('lyft.datatable');
            });

            Route::group(['prefix' => '/kpi'], function () {
                Route::get('/', [KPIController::class, 'index'])->name('kpi.index');
                Route::get('/standard', [KPIController::class, 'standardKPI'])->name('kpi.standard');
                Route::get('/main', [KPIController::class, 'mainKPI'])->name('kpi.main');
                Route::get('/accounts', [KPIController::class, 'accounts'])->name('kpi.accounts');
            });

            Route::group(['prefix' => '/employees'], function () {
                Route::get('/', [EmployeesController::class, 'index'])->name('employees.index');
                Route::get('data', [EmployeesController::class, 'data'])->name('employees.data');
                Route::get('shops', [EmployeesController::class, 'shops'])->name('employees.shops');
            });

            // 10DLC Dashoard Tracking Report
            Route::group(['prefix' => '/dlc-dashboard-tracking'], function () {
                Route::get('/', [DLCDashboardTrackingController::class, 'index'])->name('dlc-dashboard-tracking.index');
                Route::get('datatable', [DLCDashboardTrackingController::class, 'datatable'])->name('dlc-dashboard-tracking.datatable');
            });

            // Employees Report
            Route::group(['prefix' => '/employees'], function () {
                Route::get('/', [EmployeesController::class, 'index'])->name('employees.index');
                Route::get('data', [EmployeesController::class, 'data'])->name('employees.data');
                Route::get('shops', [EmployeesController::class, 'shops'])->name('employees.shops');
            });

            // 10DLC Dashoard Tracking Report
            Route::group(['prefix' => '/dlc-dashboard-tracking'], function () {
                Route::get('/', [DLCDashboardTrackingController::class, 'index'])->name('dlc-dashboard-tracking.index');
                Route::get('datatable', [DLCDashboardTrackingController::class, 'datatable'])->name('dlc-dashboard-tracking.datatable');
            });

            // Bandwith Messages Report
            Route::group(['prefix' => '/bandwith-messages'], function () {
                Route::get('/', [BandwithMessagesController::class, 'index'])->name('bandwith-messages.index');
                Route::get('/datatable', [BandwithMessagesController::class, 'datatable'])->name('bandwith-messages.datatable');
            });

            // Mandrill Messages Report
            // Route::group(['prefix' => '/mandrill-messages'], function () {
            //     Route::get('/', [MandrillMessagesController::class, 'index'])->name('mandrill-messages.index');
            //     Route::get('/datatable', [MandrillMessagesController::class, 'datatable'])->name('mandrill-messages.datatable');
            // });
        });
    });

    // Only superadmin, admin and customer support can access to this routes
    Route::group(['middleware' => RoleMiddleware::class.':superadmin,admin,customer support'], function () {
        // Custom Shared Reports
        Route::group(['prefix' => '/report/custom-reports'], function () {
            Route::get('/', [CustomReportsController::class, 'index'])->name('custom-reports.index');
            Route::get('datatable', [CustomReportsController::class, 'datatable'])->name('custom-reports.datatable');
            Route::get('/shared/datatable', [CustomReportsController::class, 'sharedDatatable'])->name('custom-reports.shared.datatable');
            Route::put('/edit-shared-report', [CustomReportsController::class, 'editSharedReport'])->name('custom-reports.editSharedReport');
            Route::delete('/{id}', [CustomReportsController::class, 'deleteSharedReport'])->name('custom-reports.deleteSharedReport');
            Route::put('/add-to-shop', [CustomReportsController::class, 'addToShop'])->name('custom-reports.addToShop');
            Route::put('/edit-for-shop', [CustomReportsController::class, 'editSharedReportForShop'])->name('custom-reports.editForShop');
            Route::delete('/customreports/{id}', [CustomReportsController::class, 'deleteSharedReportForShop'])->name('custom-reports.deleteForShop');
            Route::post('/add-to-shared', [CustomReportsController::class, 'addToShared'])->name('custom-reports.addToShared');
        });
    });

    // Only superadmin, admin and customer success can access to this routes
    Route::group([
        'prefix' => '/report',
        'middleware' => RoleMiddleware::class.':superadmin,admin,customer success',
    ], function () {
        // Report Page
        Route::get('/', [DashboardController::class, 'report'])->name('report.index');

        // RETENTION REPORTS

        // Churn Retention Report
        Route::group(['prefix' => '/retention/churn'], function () {
            Route::get('/', [ChurnRetentionController::class, 'index'])->name('retention.churn.index');
            Route::get('datatable', [ChurnRetentionController::class, 'datatable'])->name('retention.churn.datatable');
        });

        // Churn List Retention Report
        Route::group(['prefix' => '/churn/list/retention'], function () {
            Route::get('/', [ChurnListRetentionController::class, 'index'])->name('churn.list.retention.index');
            Route::get('datatable', [ChurnListRetentionController::class, 'datatable'])->name('churn.list.retention.datatable');
        });

        // Declined Transactions Report
        Route::group(['prefix' => '/declined/transactions'], function () {
            Route::get('/', [DeclinedBlueSnapTransactionsController::class, 'index'])->name('declined.transactions.index');
            Route::get('datatable', [DeclinedBlueSnapTransactionsController::class, 'datatable'])->name('declined.transactions.datatable');
        });

        // Suspended Shops List Report
        Route::group(['prefix' => '/suspended/shops/list'], function () {
            Route::get('/', [SuspendedShopsListController::class, 'index'])->name('suspended.shops.list.index');
            Route::get('datatable', [SuspendedShopsListController::class, 'datatable'])->name('suspended.shops.list.datatable');
        });

        // Motor Usage Report With ProDemand & Shop Category
        Route::group(['prefix' => '/motor/usage/prodemand'], function () {
            Route::get('/', [MotorUsageWithProdemandController::class, 'index'])->name('motor.usage.prodemand.index');
            Route::get('datatable', [MotorUsageWithProdemandController::class, 'datatable'])->name('motor.usage.prodemand.datatable');
        });

        // LRO Churn Report
        Route::group(['prefix' => '/lro/churn'], function () {
            Route::get('/', [LROChurnController::class, 'index'])->name('lro.churn.index');
            Route::get('datatable', [LROChurnController::class, 'datatable'])->name('lro.churn.datatable');
        });

        // Shop Category Report
        Route::group(['prefix' => '/shop/category'], function () {
            Route::get('/', [ShopCategoryController::class, 'index'])->name('shop.category.index');
            Route::get('datatable', [ShopCategoryController::class, 'datatable'])->name('shop.category.datatable');
        });

        // Read Only Shops Report
        Route::group(['prefix' => '/readonly/shops'], function () {
            Route::get('/', [ReadOnlyShopsController::class, 'index'])->name('readonly.shops.index');
            Route::get('datatable', [ReadOnlyShopsController::class, 'datatable'])->name('readonly.shops.datatable');
        });

        // PPH Internal Report
        Route::group(['prefix' => '/pph/internal'], function () {
            Route::get('/', [PPHInternalController::class, 'index'])->name('pph.internal.index');
            Route::get('datatable', [PPHInternalController::class, 'datatable'])->name('pph.internal.datatable');
        });
        // END OF RETENTION REPORTS
    });

    // SHOP LIST REPORT
    Route::group(['prefix' => '/report/shop-list'], function () {
        Route::get('/', [ShopListController::class, 'index'])->name('report.shoplist.index');
        Route::get('datatable', [ShopListController::class, 'datatable'])->name('report.shoplist.datatable');
    });

    // SHOP PAGE ROUTES
    Route::apiResource('shop', ShopController::class)->only(['index', 'show', 'update']);
    Route::group(['prefix' => '/shop'], function () {
        Route::get('comments/datatable/{shopid}', [ShopController::class, 'commentsDatatable'])->name('comments.datatable');
        Route::get('users/datatable/{shopid}', [ShopController::class, 'usersDatatable'])->name('users.datatable');
        Route::get('reports/datatable/{shopid}', [ShopController::class, 'reportsDatatable'])->name('reports.datatable');
        Route::get('payments/datatable/{shopid}', [ShopController::class, 'paymentsDatatable'])->name('payments.datatable');
        Route::get('invoices/datatable/{shopid}', [ShopController::class, 'invoicesDatatable'])->name('invoices.datatable');

        Route::get('/gainers/loosers', [ShopController::class, 'gainersOrLoosers'])->name('shop.gainers-loosers');
        Route::get('/new/old', [ShopController::class, 'newOrOldCompanies'])->name('shop.new-old');

        // Form Modals
        Route::put('/roid/{shopid}', [ShopController::class, 'updateROID'])->name('shop.update.roid');
        Route::post('/partstech/{shopid}', [ShopController::class, 'storePartsTech'])->name('shop.store.partstech');
        Route::delete('/partstech/{id}', [ShopController::class, 'deletePartsTech'])->name('shop.delete.partstech');
        Route::delete('/partstech/{shopid}', [ShopController::class, 'deletePartsTech'])->name('shop.delete.partstech');
        Route::put('/linked-shops/{shopid}', [ShopController::class, 'updateLinkedShops'])->name('shop.update.linked-shops');
        Route::put('/boss-board-access/{shopid}', [ShopController::class, 'updateBossBoardAccess'])->name('shop.update.boss-board-access');
        Route::put('/dvi/{shopid}', [ShopController::class, 'updateDVI'])->name('shop.update.dvi');
        Route::put('/readonly/{shopid}', [ShopController::class, 'updateReadonly'])->name('shop.update.readonly');
        Route::put('/force-log-out/{shopid}', [ShopController::class, 'updateForceLogOut'])->name('shop.update.force-log-out');
        Route::put('/trial-expiration/{shopid}', [ShopController::class, 'updateTrialExpiration'])->name('shop.update.trial-expiration');
        Route::put('/churn/{shopid}', [ShopController::class, 'updateChurn'])->name('shop.update.churn');
        Route::get('/paylink/{shopid}', [ShopController::class, 'getPayBalanceLink'])->name('shop.get.pay-link');
        Route::put('/matco-toggle/{shopid}', [ShopController::class, 'updateMatcoToggle'])->name('shop.update.matco-toggle');
        Route::put('/school-toggle/{shopid}', [ShopController::class, 'updateSchoolToggle'])->name('shop.update.school-toggle');
        Route::put('/tpms-credentials/{shopid}', [ShopController::class, 'updateTPMSCredentials'])->name('shop.update.tpms-credentials');
        Route::post('/epicor/{shopid}', [ShopController::class, 'storeEpicor'])->name('shop.store.epicor');
        Route::delete('/epicor/{id}', [ShopController::class, 'deleteEpicor'])->name('shop.delete.epicor');
        Route::put('/payments360/{shopid}', [ShopController::class, 'updatePayments360'])->name('shop.update.payments360');
        Route::put('/cardknox-request/{shopid}', [ShopController::class, 'requestCardknoxKeys'])->name('shop.request.cardknox-keys');
        Route::put('/cardknox-keys/{shopid}', [ShopController::class, 'updateCardknoxKeys'])->name('shop.update.cardknox-keys');
        Route::put('/cardknox-activate/{shopid}', [ShopController::class, 'updateCardknoxActivate'])->name('shop.update.cardknox-activate');
        Route::put('/cardknox-terminal/{shopid}', [ShopController::class, 'updateCardknoxTerminal'])->name('shop.update.cardknox-terminal');
        Route::delete('/cardknox-terminal/{id}', [ShopController::class, 'deleteCardknoxTerminal'])->name('shop.delete.cardknox-terminal');
        Route::put('/package/{shopid}', [ShopController::class, 'updatePackage'])->name('shop.update.package');
        Route::put('/suspend/package/{shopid}', [ShopController::class, 'updateSuspendReactivate'])->name('shop.update.suspend-reactivate');
        Route::put('/update/employee-password', [ShopController::class, 'updateEmployeePassword'])->name('shop.update.employee-password');
        Route::put('/beta-features/{shopid}', [ShopController::class, 'updateBetaFeatures'])->name('shop.update.beta-features');
        Route::post('/comments/{shopid}', [ShopController::class, 'storeComment'])->name('shop.store.comment');
        Route::put('/onboarding/status/{shopid}', [ShopController::class, 'updateOnboardingCompletedStatus'])->name('shop.update.onboarding.status');
        Route::put('/onboarding/animation/{shopid}', [ShopController::class, 'updateOnboardingAnimationStatus'])->name('shop.update.onboarding.animation');
        Route::put('/switch-to-paid/{shopid}', [ShopController::class, 'updateShopToPaid'])->name('shop.update.paid');

        // API KEY
        Route::apiResource('/{shop}/api-key', APIKeyController::class)->only(['index', 'create', 'destroy', 'store']);
        Route::post('/{shop}/api-key/autoserv1', [APIKeyController::class, 'autoserv1'])->name('api-key.autoserv1');
        Route::get('/{shop}/api-key/datatable', [APIKeyController::class, 'datatable'])->name('api-key.datatable');
    });
    // END SHARED ROUTES BETWEEN ADMIN AND MATCO USERS

    // MATCO USER ROUTES
    Route::group(['middleware' => ['auth', RoleMiddleware::class.':matco']], function () {
        Route::get('/matco', [ShopListController::class, 'index'])->name('report.shoplist.matco.index');
    });
    // END MATCO USER ROUTES

    Route::get('/smsnums', [ShopController::class, 'getSMSNumbers'])->name('shop.getNumbers');
    Route::delete('/smsnums/{shopid}', [ShopController::class, 'deleteSMSNumber'])->name('shop.delete.smsnumber');
    Route::post('/smsnums', [ShopController::class, 'saveSMSNumber'])->name('shop.save.smsnumber');

    Route::apiResource('new-shop', NewShopController::class)->only(['index', 'store']);
    Route::get('new-shop/get-username', [NewShopController::class, 'generateUsername'])->name('new-shop.generateUsername');

    // GET ZIP
    Route::get('zip-decode/{zip}', [ZipController::class, 'zipDecode'])->name('zip.decode');
});

Route::get('/delete', [ShopController::class, 'Delete'])->name('shop.Delete');
