{"private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build"}, "devDependencies": {"axios": "^1.7.2", "laravel-vite-plugin": "^1.0", "vite": "^5.0"}, "dependencies": {"@inertiajs/inertia": "^0.11.1", "@inertiajs/vue3": "^1.2.0", "@vitejs/plugin-vue": "^5.1.4", "bootstrap": "^5.3.3", "crypto-js": "^4.2.0", "pinia": "^2.2.4", "vue": "^3.5.12", "vue-flatpickr-component": "^11.0.5", "vue3-apexcharts": "^1.5.3", "ziggy-js": "^2.2.1"}}