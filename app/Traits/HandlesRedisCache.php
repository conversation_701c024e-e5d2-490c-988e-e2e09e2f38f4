<?php

namespace App\Traits;

use Illuminate\Support\Facades\Redis;

trait HandlesRedisCache
{
    protected function getRedisKey($shopId, $table): string
    {
        return "shop:{$shopId}:$table";
    }

    protected function cacheToRedis($table, $data): void
    {
        $key = $this->getRedisKey($data['shopid'], $table);
        $companyData = array_change_key_case($data, CASE_LOWER);

        Redis::hmset($key, $companyData);

        Redis::expire($key, 60 * 60 * 24);
    }

    protected function forgetFromRedis($shopId, $table): void
    {
        Redis::del($this->getRedisKey($shopId, $table));
    }

    protected function getFromRedis($table, $shopId): ?array
    {
        $data = Redis::hgetall($this->getRedisKey($shopId, $table));

        return empty($data) ? null : $data;
    }
}
