<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BackofficeKeys extends Model
{
    use HasFactory;

    protected $table = 'backofficekeys';

    protected $primaryKey = 'id';

    protected $fillable = [
        'shopid',
        'active',
        'ts',
        'deactivated_on',
    ];

    const CREATED_AT = 'ts';

    const UPDATED_AT = null;
}
