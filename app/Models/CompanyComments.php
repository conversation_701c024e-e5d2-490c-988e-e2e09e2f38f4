<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyComments extends Model
{
    use HasFactory;

    protected $table = 'companycomments';

    public $timestamps = true;

    protected $fillable = [
        'shopid',
        'commentdatetime',
        'comment',
    ];

    const CREATED_AT = 'ts';

    const UPDATED_AT = 'commentdatetime';
}
