<?php

namespace App\Models;

use App\Traits\HandlesRedisCache;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Company extends Model
{
    use HandlesRedisCache, HasFactory;

    protected $table = 'company';

    const CREATED_AT = 'ts';

    const UPDATED_AT = null;

    protected $primaryKey = 'shopid';

    protected $casts = [
        'shopid' => 'string',
    ];

    protected $fillable = [
        'shopid',
        'companyname',
        'datestarted',
        'trialexpiration',
        'package',
        'active',
        'referredby',
        'affiliateid',
        'new',
        'timezone',
        'status',
        'contact',
        'companycountry',
        'companyaddress',
        'companycity',
        'companystate',
        'companyzip',
        'companyphone',
        'rodisclosure',
        'rowarrdisclosure',
        'companyemail',
        'defaulttaxrate',
        'defaultlabortaxrate',
        'defaultsublettaxrate',
        'hourlyrate',
        'dashexpiration',
        'shopip',
        'inspectexpiration',
        'matco',
        'protractor',
        'salesrep',
        'newpackagetype',
        'userfee1',
        'userfee1type',
        'userfee1amount',
        'userfee1max',
        'userfee1taxable',
        'userfee1applyon',
        'nexpartpassword',
        'profitboost',
        'onboardinghours',
        'carfaxlocation',
        'merchantaccount',
    ];

    public function SmsNumbers()
    {
        return $this->hasOne(SmsNumbers::class, 'shopid', 'shopid');
    }

    public function repairorders()
    {
        return $this->hasMany(RepairOrder::class, 'shopid', 'shopid');
    }

    public function payments()
    {
        return $this->hasMany(Payments::class, 'shopid', 'shopid');
    }

    protected static function booted()
    {
        static::created(function ($company) {
            $company->updateRedisCache();
        });

        static::updated(function ($company) {
            $company->updateRedisCache();
        });

        static::deleted(function ($company) {
            $company->forgetFromRedis($company->shopid, $company->getTable());
        });
    }

    public function updateRedisCache(): void
    {
        $this->cacheToRedis($this->table, $this->toArray());
    }
}
