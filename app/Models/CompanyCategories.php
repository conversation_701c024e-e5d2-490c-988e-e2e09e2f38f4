<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CompanyCategories extends Model
{
    use HasFactory;

    protected $table = 'companycategories';

    protected $primaryKey = 'id';

    protected $fillable = [
        'shopid',
        'category',
        'level',
    ];

    const CREATED_AT = null;

    const UPDATED_AT = null;
}
