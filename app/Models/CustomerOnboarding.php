<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CustomerOnboarding extends Model
{
    use HasFactory;

    protected $table = 'customer_onboarding';

    protected $primaryKey = 'shopid';

    protected $fillable = [
        'shopid',
        'general_info',
        'settings',
        'employees',
        'suppliers',
        'customize',
        'animation',
    ];

    public $incrementing = false;

    const CREATED_AT = null;

    const UPDATED_AT = null;
}
