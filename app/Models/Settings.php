<?php

namespace App\Models;

use App\Traits\HandlesRedisCache;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Settings extends Model
{
    use HandlesRedisCache, HasFactory;

    protected $table = 'settings';

    protected $primaryKey = 'shopid';

    protected $fillable = [
        'motor',
        'failedpayment',
        'pph',
        'oss',
    ];

    const CREATED_AT = null;

    const UPDATED_AT = null;

    protected static function booted()
    {
        static::created(function ($settings) {
            $settings->updateRedisCache();
        });

        static::updated(function ($settings) {
            $settings->updateRedisCache();
        });

        static::deleted(function ($settings) {
            $settings->forgetFromRedis($settings->shopid, $settings->getTable());
        });
    }

    public function updateRedisCache(): void
    {
        $this->cacheToRedis($this->table, $this->toArray());
    }
}
