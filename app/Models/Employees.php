<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Employees extends Model
{
    use HasFactory;

    protected $table = 'employees';

    protected $fillable = [
        'shopid',
        'employeefirst',
        'employeelast',
        'username',
        'employeeid',
        'defaultwriter',
        'active',
        'password',
        'passwordenc',
    ];

    protected $primaryKey = 'id';

    const CREATED_AT = 'ts';

    const UPDATED_AT = null;
}
