<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DataTransfer extends Model
{
    use HasFactory;

    protected $table = 'datatransfer';

    protected $primaryKey = 'id';

    protected $fillable = [
        'shopid',
        'type',
        'status',
        'billing_type',
    ];

    const CREATED_AT = 'added_on';

    const UPDATED_AT = 'updated_on';
}
