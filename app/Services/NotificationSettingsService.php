<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class NotificationSettingsService
{
    public function insertNotificationSettings($shopid)
    {
        DB::table('notification_settings')->insert([
            ['shopid' => $shopid, 'notification_type' => '68', 'popup_notify' => '1'],
            ['shopid' => $shopid, 'notification_type' => '178', 'popup_notify' => '1'],
            ['shopid' => $shopid, 'notification_type' => '181', 'popup_notify' => '1'],
            ['shopid' => $shopid, 'notification_type' => '80', 'popup_notify' => '1'],
            ['shopid' => $shopid, 'notification_type' => '83', 'popup_notify' => '1'],
            ['shopid' => $shopid, 'notification_type' => '86', 'popup_notify' => '1'],
        ]);
    }
}
