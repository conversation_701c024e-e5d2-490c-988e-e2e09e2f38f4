<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class ColorCodingService
{
    public function insertColorCoding($shopid)
    {
        $chex = substr(str_shuffle('0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'), 0, 18);

        DB::table('colorcoding')->insert([
            'shopid' => $shopid,
            'title' => 'DEFAULT LABEL',
            'colorhex' => $chex,
        ]);
    }
}
