<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class ROStatusesService
{
    public function insertROStatuses($shopid)
    {
        $roStatuses = [
            ['shopid' => $shopid, 'status' => '1INSPECTION', 'colorcode' => '#d5d52a', 'isdefault' => 'yes', 'displayorder' => 1],
            ['shopid' => $shopid, 'status' => '2APPROVAL', 'colorcode' => '#FFA500', 'isdefault' => 'yes', 'displayorder' => 2],
            ['shopid' => $shopid, 'status' => '3PARTS', 'colorcode' => '#ff0000', 'isdefault' => 'yes', 'displayorder' => 3],
            ['shopid' => $shopid, 'status' => '4ASSEMBLY', 'colorcode' => '#006400', 'isdefault' => 'yes', 'displayorder' => 4],
            ['shopid' => $shopid, 'status' => '5Q-CHECK', 'colorcode' => '#ffc0cb', 'isdefault' => 'yes', 'displayorder' => 5],
            ['shopid' => $shopid, 'status' => 'FINAL', 'colorcode' => '#0000ff', 'isdefault' => 'yes', 'displayorder' => 6],
            ['shopid' => $shopid, 'status' => 'CLOSED', 'colorcode' => '#000000', 'isdefault' => 'yes', 'displayorder' => 7],
        ];

        DB::table('rostatus')->insert($roStatuses);
    }
}
