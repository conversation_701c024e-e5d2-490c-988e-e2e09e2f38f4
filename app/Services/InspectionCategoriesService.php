<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class InspectionCategoriesService
{
    public function insertInspectionCategories($shopid, $category, $displayOrder, $inspid)
    {
        $catid = DB::table('inspectioncategories')->insertGetId([
            'shopid' => $shopid,
            'category' => $category,
            'displayorder' => $displayOrder,
            'inspectionid' => $inspid,
        ]);

        return $catid;
    }
}
