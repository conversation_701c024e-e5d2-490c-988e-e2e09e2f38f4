<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class SupplierService
{
    public function insertSupplier($shopid)
    {
        DB::table('supplier')->insert([
            [
                'shopid' => $shopid,
                'SupplierID' => 4,
                'SupplierName' => "O'Reilly Auto Parts",
                'SupplierAddress' => '13181 BLACK MOUNTAIN RD',
                'SupplierCity' => 'SAN DIEGO',
                'SupplierState' => 'CA',
                'SupplierZip' => '92129',
                'SupplierPhone' => '5555555555',
                'SupplierFax' => '',
                'SupplierContact' => 'REILLY',
                'Active' => 'YES',
            ],
            [
                'shopid' => $shopid,
                'SupplierID' => 5,
                'SupplierName' => 'ADVANCE AUTO PARTS',
                'SupplierAddress' => '920 EAST FIRST STREET',
                'SupplierCity' => 'SANTA ANA',
                'SupplierState' => 'CA',
                'SupplierZip' => '92701',
                'SupplierPhone' => '5555555555',
                'SupplierFax' => '',
                'SupplierContact' => 'AAP',
                'Active' => 'YES',
            ],
            [
                'shopid' => $shopid,
                'SupplierID' => 6,
                'SupplierName' => 'AUTO ZONE',
                'SupplierAddress' => '9152 MIRA MESA BL',
                'SupplierCity' => 'SAN DIEGO',
                'SupplierState' => 'CA',
                'SupplierZip' => '92126',
                'SupplierPhone' => '5555555555',
                'SupplierFax' => '',
                'SupplierContact' => 'AZ',
                'Active' => 'YES',
            ],
            [
                'shopid' => $shopid,
                'SupplierID' => 7,
                'SupplierName' => 'NAPA AUTO',
                'SupplierAddress' => '7440 CONVOY CT',
                'SupplierCity' => 'SAN DIEGO',
                'SupplierState' => 'CA',
                'SupplierZip' => '92111',
                'SupplierPhone' => '5555555555',
                'SupplierFax' => '',
                'SupplierContact' => 'NAPS',
                'Active' => 'YES',
            ],
        ]);
    }
}
