<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class InspectionModelsService
{
    private $debugData = [];

    public function insert($shopid, $debug = false)
    {
        try {
            DB::transaction(function () use ($shopid) {
                $this->debugData = ['steps' => [], 'queries' => [], 'stats' => []];
                $startTime = microtime(true);

                // Step 1: Get the preload model
                $model = $this->getPreloadModel();
                if (! $model) {
                    $this->logDebug('No preload model found');

                    return;
                }

                // Step 2: Create new model
                $newModelId = $this->createNewModel($shopid, $model);
                $this->logDebug('Created new model', ['new_model_id' => $newModelId]);

                // Step 3: Process categories
                $categories = $this->getCategories($model->id);
                $categoryMap = $this->processCategories($shopid, $newModelId, $categories);
                $this->logDebug('Processed categories', ['count' => count($categoryMap)]);

                // Step 4: Process sections
                $sections = $this->getSections(array_keys($categoryMap));
                $sectionMap = $this->processSections($shopid, $categoryMap, $sections);
                $this->logDebug('Processed sections', ['count' => count($sectionMap)]);

                // Step 5: Process items
                $items = $this->getItems(array_keys($categoryMap), array_keys($sectionMap));
                $itemMap = $this->processItems($shopid, $categoryMap, $sectionMap, $items);
                $this->logDebug('Processed items', ['count' => count($itemMap)]);

                // Step 6: Process measurements
                $this->processMeasurements($shopid, $itemMap);
                $this->logDebug('Processed measurements');

                // Step 7: Process findings
                $this->processFindings($shopid, $sections, $items, $itemMap);
                $this->logDebug('Processed findings');

                $this->debugData['stats']['execution_time'] = round(microtime(true) - $startTime, 2).'s';
                $this->debugData['stats']['memory_usage'] = round(memory_get_peak_usage() / 1024 / 1024, 2).'MB';
            });

            return $this->debugData;
        } catch (\Exception $e) {
            Log::error('Inspection model import failed: '.$e->getMessage());
            $this->debugData['error'] = $e->getMessage();

            return $this->debugData;
        }
    }

    private function getPreloadModel()
    {
        return DB::table('inspection_models')
            ->where('preload', 1)
            ->where('name', '100 point inspection')
            ->first();
    }

    private function createNewModel($shopid, $model)
    {
        return DB::table('inspection_models')->insertGetId([
            'shopid' => $shopid,
            'name' => $model->name,
            'isdefault' => 1,
            'enabled' => 1,
        ]);
    }

    private function getCategories($modelId)
    {
        return DB::table('inspection_categories')
            ->where('model_id', $modelId)
            ->orderBy('display_order')
            ->get();
    }

    private function processCategories($shopid, $newModelId, $categories)
    {
        $categoryMap = [];
        foreach ($categories as $category) {
            $newCategoryId = DB::table('inspection_categories')->insertGetId([
                'shopid' => $shopid,
                'model_id' => $newModelId,
                'name' => $category->name,
                'display_order' => $category->display_order,
            ]);
            $categoryMap[$category->id] = $newCategoryId;
        }

        return $categoryMap;
    }

    private function getSections($categoryIds)
    {
        return DB::table('inspection_sections')
            ->whereIn('category_id', $categoryIds)
            ->orderBy('section_type', 'desc')
            ->orderBy('display_order')
            ->get();
    }

    private function processSections($shopid, $categoryMap, $sections)
    {
        $sectionMap = [];
        foreach ($sections as $section) {
            $newSectionId = DB::table('inspection_sections')->insertGetId([
                'shopid' => $shopid,
                'category_id' => $categoryMap[$section->category_id],
                'name' => $section->name,
                'section_type' => $section->section_type,
                'display_order' => $section->display_order,
            ]);
            $sectionMap[$section->id] = $newSectionId;
        }

        return $sectionMap;
    }

    private function getItems($categoryIds, $sectionIds)
    {
        return DB::table('inspection_item_models')
            ->whereIn('category_id', $categoryIds)
            ->whereIn('section_id', $sectionIds)
            ->orderBy('display_order')
            ->get();
    }

    private function processItems($shopid, $categoryMap, $sectionMap, $items)
    {
        $itemMap = [];
        foreach ($items as $item) {
            $newItemId = DB::table('inspection_item_models')->insertGetId([
                'shopid' => $shopid,
                'category_id' => $categoryMap[$item->category_id],
                'section_id' => $sectionMap[$item->section_id],
                'name' => $item->name,
                'user_input_type' => $item->user_input_type,
                'measurement_values' => $item->measurement_values,
                'measurements_count' => $item->measurements_count,
                'measurement_name1' => $item->measurement_name1,
                'measurement_name2' => $item->measurement_name2,
                'public_measurement_name1' => $item->public_measurement_name1,
                'public_measurement_name2' => $item->public_measurement_name2,
                'display_order' => $item->display_order,
            ]);
            $itemMap[$item->id] = $newItemId;
        }

        return $itemMap;
    }

    private function processMeasurements($shopid, $itemMap)
    {
        $measurements = DB::table('inspection_item_model_measurements')
            ->whereIn('item_model_id', array_keys($itemMap))
            ->get();

        $measurementInserts = [];
        foreach ($measurements as $measurement) {
            $measurementInserts[] = [
                'shopid' => $shopid,
                'item_model_id' => $itemMap[$measurement->item_model_id],
                'measurement_item_model_id' => $itemMap[$measurement->measurement_item_model_id],
                'which_measurement' => $measurement->which_measurement,
            ];
        }

        if (! empty($measurementInserts)) {
            DB::table('inspection_item_model_measurements')->insert($measurementInserts);
        }
    }

    private function processFindings($shopid, $sections, $items, $itemMap)
    {
        $findingSections = $sections->where('section_type', 'finding');
        if ($findingSections->isEmpty()) {
            return;
        }

        $findingItems = $items->whereIn('section_id', $findingSections->pluck('id'));
        $findings = $this->getFindings($findingItems->pluck('id'));

        $uniqueFindings = $findings->groupBy('description');
        $existingFindings = $this->getExistingFindings($shopid, $uniqueFindings->keys());
        $findingMap = $this->createFindingMap($shopid, $uniqueFindings, $existingFindings);

        $this->createItemFindings($shopid, $findings, $itemMap, $findingMap);
        $this->processRecommendations($shopid, $findings, $findingMap);
    }

    private function getFindings($itemIds)
    {
        return DB::table('inspection_item_model_findings')
            ->join('inspection_findings', 'inspection_item_model_findings.finding_id', '=', 'inspection_findings.id')
            ->whereIn('inspection_item_model_findings.item_model_id', $itemIds)
            ->select(
                'inspection_findings.id as original_finding_id',
                'inspection_findings.description',
                'inspection_item_model_findings.item_model_id',
                'inspection_item_model_findings.display_order'
            )
            ->get();
    }

    private function getExistingFindings($shopid, $descriptions)
    {
        return DB::table('inspection_findings')
            ->where('shopid', $shopid)
            ->whereIn('description', $descriptions)
            ->pluck('id', 'description');
    }

    private function createFindingMap($shopid, $uniqueFindings, $existingFindings)
    {
        $findingMap = [];
        $newFindings = [];

        foreach ($uniqueFindings as $description => $findingGroup) {
            if ($existingFindings->has($description)) {
                $findingMap[$description] = $existingFindings[$description];
            } else {
                $newFindings[] = [
                    'shopid' => $shopid,
                    'description' => $description,
                ];
            }
        }

        if (! empty($newFindings)) {
            DB::table('inspection_findings')->insert($newFindings);
            $newlyInserted = DB::table('inspection_findings')
                ->where('shopid', $shopid)
                ->whereIn('description', collect($newFindings)->pluck('description'))
                ->pluck('id', 'description');
            $findingMap = array_merge($findingMap, $newlyInserted->toArray());
        }

        return $findingMap;
    }

    private function createItemFindings($shopid, $findings, $itemMap, $findingMap)
    {
        $itemFindingInserts = $findings->map(function ($finding) use ($shopid, $itemMap, $findingMap) {
            return [
                'shopid' => $shopid,
                'item_model_id' => $itemMap[$finding->item_model_id],
                'finding_id' => $findingMap[$finding->description],
                'display_order' => $finding->display_order,
            ];
        })->toArray();

        if (! empty($itemFindingInserts)) {
            DB::table('inspection_item_model_findings')->insert($itemFindingInserts);
        }
    }

    private function processRecommendations($shopid, $findings, $findingMap)
    {
        $recommendations = DB::table('inspection_finding_recommendations')
            ->whereIn('finding_id', $findings->pluck('original_finding_id')->unique())
            ->get();

        $recommendationInserts = $recommendations->map(function ($recommendation) use ($shopid, $findings, $findingMap) {
            $originalFinding = $findings->firstWhere('original_finding_id', $recommendation->finding_id);

            return [
                'shopid' => $shopid,
                'finding_id' => $findingMap[$originalFinding->description],
                'description' => $recommendation->description,
                'display_order' => $recommendation->display_order,
            ];
        })->toArray();

        if (! empty($recommendationInserts)) {
            DB::table('inspection_finding_recommendations')->insert($recommendationInserts);
        }
    }

    private function logDebug($message, $data = [])
    {
        $this->debugData['steps'][] = [
            'message' => $message,
            'data' => $data,
            'time' => microtime(true),
        ];
    }
}
