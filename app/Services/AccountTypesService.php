<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class AccountTypesService
{
    public function insertAccountTypes($shopid)
    {
        DB::table('accounttypes')->insert([
            ['type' => 'Current Assets', 'shopid' => $shopid],
            ['type' => 'Current Liabilities', 'shopid' => $shopid],
            ['type' => 'Equity', 'shopid' => $shopid],
            ['type' => 'Current Liabilities', 'shopid' => $shopid],
            ['type' => 'Income', 'shopid' => $shopid],
            ['type' => 'Cost of Goods Sold', 'shopid' => $shopid],
            ['type' => 'Expense', 'shopid' => $shopid],
        ]);
    }
}
