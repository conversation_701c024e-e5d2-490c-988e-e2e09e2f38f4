<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class CampaignsService
{
    public function insertCampaigns($shopid)
    {
        DB::table('campaigns')->insert([
            [
                'shopid' => $shopid,
                'campaign' => '1 Day Review Request',
                'startdate' => '2012-10-12',
                'description' => '0',
                'emailid' => 0,
                'active' => 'no',
                'type' => '1',
                'message' => 'html<p>Dear [CustomerName],&nbsp;<br><span style=\"background-color: var(--white); color: var(--textColor); font-family: var(--mdb-body-font-family); font-weight: var(--mdb-body-font-weight); text-align: var(--mdb-body-text-align);\"><br>We want to take a moment to thank you for choosing [ShopName] . We truly appreciate your support and hope you enjoyed your experience with us.&nbsp;</span><span style=\"background-color: var(--white); color: var(--textColor); font-family: var(--mdb-body-font-family); font-weight: var(--mdb-body-font-weight); text-align: var(--mdb-body-text-align);\">If you had a great experience , we’d be incredibly grateful if you could take a minute to leave us a 5-star review HERE. Your feedback not only helps us improve but also helps other customers make informed decisions when looking for their next vehicle repair.&nbsp;<br><br>If for any reason you feel like you did not receive 5 star service please give us a call at [ShopPhone]  and we are more than happy to address any concerns! Thank you again for being a valued customer and we look forward to hearing from you soon!&nbsp;<br><br><br></span></p>',
            ],
            [
                'shopid' => $shopid,
                'campaign' => '5 Day Follow Up',
                'startdate' => '2012-10-12',
                'description' => '0',
                'emailid' => 0,
                'active' => 'no',
                'type' => '5',
                'message' => "Dear [CustomerName],\r\nI would like to thank you for the opportunity to work on your [VehicleYear] [VehicleMake] [VehicleModel] recently.  Here at [ShopName] we want to make sure you are completely satisfied with our work.\r\n\r\nTo say thank you in a more tangible way, I am including an offer code for you to redeem at any time.\r\n\r\nOil and Filter Change\r\nCheck Air and Cabin Air Filters\r\nCheck Belts and Hoses\r\nInspect Brakes\r\nRoad Test\r\nTire rotation\r\n\r\nOnly $29.95!  Mention offer code 2995 when calling for your appointment!\r\n\r\nThanks again and we look forward to serving again soon!\r\n\r\nJohn\r\n[ShopName]\r\n[ShopPhone]\r\n[ShopLogo]\r\n\r\n\r\nIf you prefer not to receive our follow up emails, please follow this link to remove your email address from our list.  https://{$_SERVER['SERVER_NAME']}/remove.php?email=[CustomerEmail]",
            ],
            [
                'shopid' => $shopid,
                'campaign' => '30 Day Follow Up',
                'startdate' => '0000-00-00',
                'description' => '0',
                'emailid' => 0,
                'active' => 'no',
                'type' => '30',
                'message' => 'html<p>Hi&nbsp;[CustomerName],&nbsp;<br><br>It’s been a couple of weeks since your repair on your [VehicleYear] [VehicleMake] [VehicleModel]   , and we wanted to check in to see how everything is going. Here at&nbsp;[ShopName], we strive to ensure your complete satisfaction. If you have any questions or if there is anything we can assist you with, please don\'t hesitate to reach out to us.<br><br>To express our appreciation for you being a valued customer, we\'d like to offer you 10 percent off your next eligible repair on your next visit. Just mention this email when you stop in.&nbsp;<br><br>Thank you for trusting us with your business and we look forward to seeing you again soon!&nbsp;</p>',
            ],
            [
                'shopid' => $shopid,
                'campaign' => '60 Day Follow Up',
                'startdate' => '0000-00-00',
                'description' => '0',
                'emailid' => 0,
                'active' => 'no',
                'type' => '60',
                'message' => "Dear [CustomerName],\r\nI would like to thank you for the opportunity to work on your [VehicleYear] [VehicleMake] [VehicleModel] recently.  Here at [ShopName] we want to make sure you are completely satisfied with our work.\r\n\r\nTo say thank you in a more tangible way, I am including an offer code for you to redeem at any time.\r\n\r\nOil and Filter Change\r\nCheck Air and Cabin Air Filters\r\nCheck Belts and Hoses\r\nInspect Brakes\r\nRoad Test\r\n\r\nOnly $29.95!  Mention offer code 2995 when calling for your appointment!\r\n\r\nThanks again and we look forward to serving again soon!\r\n\r\nJohn\r\n[ShopName]\r\n[ShopPhone]\r\n[ShopLogo]\r\n\r\nIf you prefer not to receive our follow up emails, please follow this link to remove your email address from our list.  https://{$_SERVER['SERVER_NAME']}/remove.php?email=[CustomerEmail]",
            ],
            [
                'shopid' => $shopid,
                'campaign' => '90 Day Regular Maintenance Reminder',
                'startdate' => '0000-00-00',
                'description' => '0',
                'emailid' => 0,
                'active' => 'no',
                'type' => '90',
                'message' => 'html<p>Hi&nbsp;[CustomerName]  </p><p>We hope you’re enjoying your newly repaired vehicle! It’s been about 90 days since your last visit, and we wanted to check in.</p><p>Regular maintenance is crucial to keeping your car in top shape, and we’re here to help with any services you may need. Whether it’s a routine check-up, an oil change, or any other concern, our team is ready to assist you.</p><p>If you have any questions or would like to schedule an appointment, just reply to this email or give us a call at [ShopPhone]. We’re always here to ensure your vehicle runs smoothly!</p><p>Thank you for choosing [ShopName]. We look forward to seeing you again soon!</p>',
            ],
            [
                'shopid' => $shopid,
                'campaign' => '90 Day Service Special',
                'startdate' => '0000-00-00',
                'description' => '0',
                'emailid' => 0,
                'active' => 'no',
                'type' => '90',
                'message' => "[CustomerName][CustomerAddress]Dear [CustomerName],\r\nAt [ShopName], we want to be sure to offer our loyal customers with something to help out during the tough times.  Making ends meet in a difficult economy is challenging, so we would like to help.\r\n\r\nTo say thanks, we are including an offer code for you to redeem at any time..\r\n\r\nOil and Filter Change\r\nCheck Air and Cabin Air Filters\r\nCheck Belts and Hoses\r\nInspect Brakes\r\nRoad Test\r\n\r\nOnly $29.95!  Mention offer code 2995 when calling for your appointment!\r\n\r\nThanks again and we look forward to serving again soon!\r\n\r\nJohn\r\n[ShopName]\r\n[ShopPhone]\r\n[ShopLogo]\r\n\r\nIf you prefer not to receive our follow up emails, please follow this link to remove your email address from our list.  https://{$_SERVER['SERVER_NAME']}/remove.php?email=[CustomerEmail]",
            ],
        ]);
    }
}
