<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class SourcesService
{
    public function insertSources($shopid)
    {
        $sources = [
            ['shopid' => $shopid, 'Source' => 'Direct Mail'],
            ['shopid' => $shopid, 'Source' => 'Drive By'],
            ['shopid' => $shopid, 'Source' => 'AAA'],
            ['shopid' => $shopid, 'Source' => 'Google'],
            ['shopid' => $shopid, 'Source' => 'Yelp'],
            ['shopid' => $shopid, 'Source' => 'Referral'],
            ['shopid' => $shopid, 'Source' => 'Repeat'],
        ];

        DB::table('source')->insert($sources);
    }
}
