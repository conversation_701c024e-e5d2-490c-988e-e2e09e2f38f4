<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class PartsService
{
    public function insertParts($shopid)
    {
        $parts = [
            [
                'shopid' => $shopid,
                'PartID' => 1,
                'PartNumber' => '2311',
                'PartDesc' => 'BRAKE LINING',
                'PartPrice' => 76.5000,
                'Quantity' => 1,
                'ROID' => 1001,
                'Supplier' => 'MY PARTS HOUSE',
                'Cost' => 34.0000,
                'PartInvoiceNumber' => '',
                'PartCode' => 'NEW',
                'LineTTLPrice' => 76.5,
                'LineTTLCost' => 34,
                'Date' => Carbon::now(),
                'PartCategory' => 'BRAKE PART',
                'complaintid' => 3,
                'discount' => 0,
                'net' => 76.5,
                'tax' => 'YES',
            ],
            [
                'shopid' => $shopid,
                'PartID' => 2,
                'PartNumber' => '2311',
                'PartDesc' => 'BRAKE LINING',
                'PartPrice' => 76.5000,
                'Quantity' => 1,
                'ROID' => 1001,
                'Supplier' => 'MY PARTS HOUSE',
                'Cost' => 34.0000,
                'PartInvoiceNumber' => '45644',
                'PartCode' => 'NO CODES',
                'LineTTLPrice' => 72.67,
                'LineTTLCost' => 34,
                'Date' => Carbon::now(),
                'PartCategory' => 'BRAKE PART',
                'complaintid' => 3,
                'discount' => 5,
                'net' => 72.67,
                'tax' => 'YES',
            ],
            [
                'shopid' => $shopid,
                'PartID' => 10,
                'PartNumber' => 'OIL',
                'PartDesc' => '10W50',
                'PartPrice' => 6.0000,
                'Quantity' => 1,
                'ROID' => 1003,
                'Supplier' => 'MY PARTS HOUSE',
                'Cost' => 2.0000,
                'PartInvoiceNumber' => '',
                'PartCode' => 'NEW',
                'LineTTLPrice' => 6,
                'LineTTLCost' => 2,
                'Date' => Carbon::now(),
                'PartCategory' => 'GENERAL PART',
                'complaintid' => 32,
                'discount' => 0,
                'net' => 6,
                'tax' => '',
            ],
            [
                'shopid' => $shopid,
                'PartID' => 15,
                'PartNumber' => 'BRAKEPADS',
                'PartDesc' => 'BRAKE PADS',
                'PartPrice' => 60.7500,
                'Quantity' => 1,
                'ROID' => 1003,
                'Supplier' => 'MY PARTS HOUSE',
                'Cost' => 27.0000,
                'PartInvoiceNumber' => '',
                'PartCode' => 'NEW',
                'LineTTLPrice' => 60.75,
                'LineTTLCost' => 27,
                'Date' => Carbon::now(),
                'PartCategory' => 'BRAKE PART',
                'complaintid' => 34,
                'discount' => 0,
                'net' => 60.75,
                'tax' => 'YES',
            ],
            [
                'shopid' => $shopid,
                'PartID' => 20,
                'PartNumber' => 'BRAKEPADS',
                'PartDesc' => 'BRAKE PADS',
                'PartPrice' => 60.7500,
                'Quantity' => 1,
                'ROID' => 1003,
                'Supplier' => 'MY PARTS HOUSE',
                'Cost' => 27.0000,
                'PartInvoiceNumber' => '',
                'PartCode' => 'NEW',
                'LineTTLPrice' => 60.75,
                'LineTTLCost' => 27,
                'Date' => Carbon::now(),
                'PartCategory' => 'BRAKE PART',
                'complaintid' => 33,
                'discount' => 0,
                'net' => 60.75,
                'tax' => 'YES',
            ],
        ];

        DB::table('parts')->insert($parts);
    }
}
