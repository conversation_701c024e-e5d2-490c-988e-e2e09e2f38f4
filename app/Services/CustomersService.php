<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class CustomersService
{
    public function insertCustomers($shopid)
    {
        $customers = [
            [
                'shopid' => $shopid,
                'CustomerID' => 1,
                'LastName' => 'SMITH',
                'FirstName' => 'JOHN',
                'Address' => '1234 MAIN ST',
                'City' => 'POWAY',
                'State' => 'CA',
                'Zip' => '92064',
                'HomePhone' => '6195551141',
                'WorkPhone' => '6194451141',
                'CellPhone' => '1234444441',
                'Pager' => '',
                'Fax' => '1235554444',
                'EMail' => '<EMAIL>',
                'Wholesale' => '',
                'UserDefined1' => '',
                'UserDefined2' => '',
                'UserDefined3' => '',
                'Comments' => '',
                'ServiceReminder' => null,
                'Specials' => '',
                'Follow' => '',
                'Discount' => '',
                'contact' => '',
            ],
            [
                'shopid' => $shopid,
                'CustomerID' => 3,
                'LastName' => 'HILL',
                'FirstName' => 'BUNKER',
                'Address' => '4444 MAIN ST',
                'City' => 'SAN DIEGO',
                'State' => 'CA',
                'Zip' => '92101',
                'HomePhone' => '6195554111',
                'WorkPhone' => '',
                'CellPhone' => '',
                'Pager' => '',
                'Fax' => '',
                'EMail' => '',
                'Wholesale' => '',
                'UserDefined1' => '',
                'UserDefined2' => '',
                'UserDefined3' => '',
                'Comments' => '',
                'ServiceReminder' => null,
                'Specials' => '',
                'Follow' => '',
                'Discount' => '',
                'contact' => '',
            ],
            [
                'shopid' => $shopid,
                'CustomerID' => 11,
                'LastName' => 'WILLIAMS',
                'FirstName' => 'HANK',
                'Address' => '7748 WELLINGTON ST',
                'City' => 'LITTLETON',
                'State' => 'CO',
                'Zip' => '80122',
                'HomePhone' => '3035551212',
                'WorkPhone' => '3112322132',
                'CellPhone' => '1321321321',
                'Pager' => '',
                'Fax' => '3213213213',
                'EMail' => '',
                'Wholesale' => '',
                'UserDefined1' => '',
                'UserDefined2' => '',
                'UserDefined3' => '',
                'Comments' => '',
                'ServiceReminder' => null,
                'Specials' => '',
                'Follow' => '',
                'Discount' => '',
                'contact' => '',
            ],
        ];

        DB::table('customer')->insert($customers);
    }
}
