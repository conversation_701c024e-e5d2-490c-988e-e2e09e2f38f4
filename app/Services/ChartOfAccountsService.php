<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class ChartOfAccountsService
{
    public function insertChartOfAccounts($shopid)
    {
        DB::table('chartofaccounts')->insert([
            ['shopid' => $shopid, 'category' => 'Accounting and Bookkeeping', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Automobile Expenses', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Bank Service charges and Fees', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Books and Periodicals', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Business/Trade Conventions', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Business Gifts', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Business Meals', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Equipment', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Computer Hardware', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Computer Software', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Consulting Fees', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Depreciation and Amortization', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Dues and Subscriptions', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Education Expenses', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Internet', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Web Hosting and Email', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Insurance', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Legal and Attorney Fees', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'License Fees and Taxes', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Credit Card Processing Fees', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Office Furniture and Equipment', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Office Supplies', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Parking and Tolls', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Postage and Shipping', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Printing and Duplication', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Self-Employment Taxes', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Start-up Expenses', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'State and Local Business Taxes', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Telephone Expense', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Travel Expenses', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Utilities', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Advertising', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Rent', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Cost of Goods Sold', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Income from Work Performed', 'cattype' => 'Income', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Income from Part Sales', 'cattype' => 'Income', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Cost of Labor Sold', 'cattype' => 'Expense', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Other Income', 'cattype' => 'Income', 'core' => 'yes'],
            ['shopid' => $shopid, 'category' => 'Income Taxes', 'cattype' => 'Expense', 'core' => 'yes'],
        ]);
    }
}
