<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class PartsRegistryService
{
    public function insertPartsRegistry($shopid)
    {
        $parts = [
            [
                'shopid' => $shopid,
                'PartNumber' => '2311',
                'PartDesc' => 'BRAKE LINING',
                'PartPrice' => 76.5,
                'PartCode' => 'NEW',
                'PartCost' => 34,
                'PartSupplier' => 'MY PARTS HOUSE',
                'OnHand' => 0,
                'Allocatted' => 0,
                'NetOnHand' => 0,
                'ReOrderLevel' => 0,
                'MaxOnHand' => 0,
                'MaintainStock' => '',
                'OrderStatus' => '',
                'TransitStatus' => '',
                'PartCategory' => 'BRAKE PART',
                'discount' => 0,
                'net' => 0,
                'tax' => 'YES',
            ],
            [
                'shopid' => $shopid,
                'PartNumber' => '*********',
                'PartDesc' => 'BRAKE ROTORS',
                'PartPrice' => 78.75,
                'PartCode' => 'NEW',
                'PartCost' => 35,
                'PartSupplier' => 'MY PARTS HOUSE',
                'OnHand' => 0,
                'Allocatted' => 0,
                'NetOnHand' => 0,
                'ReOrderLevel' => 0,
                'MaxOnHand' => 0,
                'MaintainStock' => '',
                'OrderStatus' => '',
                'TransitStatus' => '',
                'PartCategory' => 'BRAKE PART',
                'discount' => 0,
                'net' => 0,
                'tax' => 'YES',
            ],
            [
                'shopid' => $shopid,
                'PartNumber' => 'OIL1511',
                'PartDesc' => 'OIL FILTER FOR AUDO',
                'PartPrice' => 10,
                'PartCode' => 'NEW',
                'PartCost' => 4,
                'PartSupplier' => 'MY PARTS HOUSE',
                'OnHand' => 0,
                'Allocatted' => 0,
                'NetOnHand' => 0,
                'ReOrderLevel' => 0,
                'MaxOnHand' => 0,
                'MaintainStock' => '',
                'OrderStatus' => '',
                'TransitStatus' => '',
                'PartCategory' => 'GENERAL PART',
                'discount' => 0,
                'net' => 0,
                'tax' => 'YES',
            ],
            [
                'shopid' => $shopid,
                'PartNumber' => 'OIL',
                'PartDesc' => '10W50',
                'PartPrice' => 6,
                'PartCode' => 'NEW',
                'PartCost' => 2,
                'PartSupplier' => 'MY PARTS HOUSE',
                'OnHand' => 0,
                'Allocatted' => 0,
                'NetOnHand' => 0,
                'ReOrderLevel' => 0,
                'MaxOnHand' => 0,
                'MaintainStock' => '',
                'OrderStatus' => '',
                'TransitStatus' => '',
                'PartCategory' => 'GENERAL PART',
                'discount' => 0,
                'net' => 0,
                'tax' => '',
            ],
            [
                'shopid' => $shopid,
                'PartNumber' => '4744M',
                'PartDesc' => 'MUFFLER',
                'PartPrice' => 192.5,
                'PartCode' => 'NEW',
                'PartCost' => 55,
                'PartSupplier' => 'MY PARTS HOUSE',
                'OnHand' => 0,
                'Allocatted' => 0,
                'NetOnHand' => 0,
                'ReOrderLevel' => 0,
                'MaxOnHand' => 0,
                'MaintainStock' => '',
                'OrderStatus' => '',
                'TransitStatus' => '',
                'PartCategory' => 'EXHAUST PART',
                'discount' => 0,
                'net' => 0,
                'tax' => 'YES',
            ],
            [
                'shopid' => $shopid,
                'PartNumber' => 'BRAKEPADS',
                'PartDesc' => 'BRAKE PADS',
                'PartPrice' => 60.75,
                'PartCode' => 'NEW',
                'PartCost' => 27,
                'PartSupplier' => 'MY PARTS HOUSE',
                'OnHand' => 0,
                'Allocatted' => 0,
                'NetOnHand' => 0,
                'ReOrderLevel' => 0,
                'MaxOnHand' => 0,
                'MaintainStock' => '',
                'OrderStatus' => '',
                'TransitStatus' => '',
                'PartCategory' => 'BRAKE PART',
                'discount' => 0,
                'net' => 0,
                'tax' => 'YES',
            ],
        ];

        DB::table('partsregistry')->insert($parts);
    }

    public function insertPartsRegistryWOTestData($shopid)
    {
        DB::table('partsregistry')->insert([
            'shopid' => $shopid,
            'PartNumber' => 'Oil',
            'PartDesc' => '10W30 Oil',
            'PartCost' => '0.00',
            'PartPrice' => '0.00',
            'PartSupplier' => 'JUST IN TIME',
        ]);
    }
}
