<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class ComplaintcatsService
{
    public function insertComplaintcats($shopid)
    {
        $data = [
            ['shopid' => $shopid, 'catname' => 'Battery'],
            ['shopid' => $shopid, 'catname' => 'Brakes'],
            ['shopid' => $shopid, 'catname' => 'Diagnostic'],
            ['shopid' => $shopid, 'catname' => 'Warranty'],
            ['shopid' => $shopid, 'catname' => 'Alignment'],
            ['shopid' => $shopid, 'catname' => 'Body'],
            ['shopid' => $shopid, 'catname' => 'Detailing'],
            ['shopid' => $shopid, 'catname' => 'Electrical'],
            ['shopid' => $shopid, 'catname' => 'Flush'],
            ['shopid' => $shopid, 'catname' => 'Heating/Cooling'],
            ['shopid' => $shopid, 'catname' => 'Interior'],
            ['shopid' => $shopid, 'catname' => 'Oil Change'],
            ['shopid' => $shopid, 'catname' => 'State Inspection'],
            ['shopid' => $shopid, 'catname' => 'Suspension'],
            ['shopid' => $shopid, 'catname' => 'Tires'],
            ['shopid' => $shopid, 'catname' => 'Transmission'],
            ['shopid' => $shopid, 'catname' => 'Drivetrain'],
        ];

        DB::table('complaintcats')->insert($data);
    }
}
