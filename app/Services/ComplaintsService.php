<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class ComplaintsService
{
    public function insertComplaints($shopid)
    {
        $complaints = [
            ['shopid' => $shopid, 'complaintid' => 1, 'roid' => 1002, 'complaint' => 'The Air Filter needs replacement.', 'techreport' => null, 'tech' => ''],
            ['shopid' => $shopid, 'complaintid' => 2, 'roid' => 1002, 'complaint' => 'The vehicle needs the front wheels aligned.', 'techreport' => null, 'tech' => ''],
            ['shopid' => $shopid, 'complaintid' => 3, 'roid' => 1001, 'complaint' => 'Oil and filter change', 'techreport' => '', 'tech' => ''],
            ['shopid' => $shopid, 'complaintid' => 32, 'roid' => 1003, 'complaint' => 'The vehicle needs the front wheels aligned.', 'techreport' => null, 'tech' => ''],
            ['shopid' => $shopid, 'complaintid' => 33, 'roid' => 1003, 'complaint' => 'The left front wheel bearing needs replacement.', 'techreport' => null, 'tech' => ''],
            ['shopid' => $shopid, 'complaintid' => 34, 'roid' => 1003, 'complaint' => 'The Accessory Drive Belt needs replacement.', 'techreport' => null, 'tech' => ''],
        ];

        DB::table('complaints')->insert($complaints);
    }
}
