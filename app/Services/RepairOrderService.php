<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class RepairOrderService
{
    public function insertRepairOrders($shopid)
    {
        $repairOrders = [
            [
                'shopid' => $shopid,
                'ROID' => 1001,
                'CustomerID' => 1,
                'Writer' => '',
                'DateIn' => Carbon::now()->format('Y-m-d'),
                'TimeIn' => '07:55:00',
                'TaxRate' => 7.75,
                'PurchaseOrderNumber' => '',
                'VehID' => 1,
                'Customer' => 'JOHN SMITH',
                'VehInfo' => '2003 MERCEDES-BENZ E-CLASS E320 SEDAN 4-DR',
                'vehyear' => '2003',
                'vehmake' => 'MERCEDES-BENZ',
                'vehmodel' => 'E-CLASS E320 SEDAN 4-DR',
                'vehlicense' => 'KDKDKDK',
                'vehstate' => 'CA',
                'VehLicNum' => 'KDKDKDK - CA',
                'WrittenBy' => 0,
                'Status' => 'Final',
                'StatusDate' => Carbon::now()->format('Y-m-d'),
                'TotalLbrHrs' => 0,
                'TotalLbr' => 178,
                'TotalPrts' => 149.17,
                'TotalSublet' => 0,
                'TotalRO' => 338.73,
                'CustomerAddress' => '1234 MAIN ST',
                'CustomerCSZ' => 'POWAY, CA. 92064',
                'CustomerPhone' => '**********',
                'customercity' => 'POWAY',
                'customerstate' => 'CA',
                'customerzip' => '92064',
                'VehicleMiles' => '',
                'MilesOut' => '',
                'Vin' => 'WDBUF65J13A233645',
                'CustomerWork' => '**********',
                'MajorComplaint' => '',
                'DatePromised' => '0000-00-00',
                'Comments' => '',
                'DiscountAmt' => 0,
                'DiscountPercent' => 0,
                'WarrMos' => 0,
                'WarrMiles' => 0,
                'SalesTax' => 11.56,
                'PartsCost' => 68,
                'ROType' => 'Customer Pay',
                'VehEngine' => '3.2L V6 SOHC 18V',
                'Cyl' => 6,
                'VehTrans' => 'AUTO',
                'HowPaid' => '',
                'AmtPaid1' => 0,
                'CheckNum1' => '',
                'HowPaid2' => '',
                'AmtPaid2' => 0,
                'CheckNum2' => '',
                'EstimateAmt' => 0,
                'NoFollow' => '',
                'AccountPaid' => '',
                'HazardousWaste' => '',
                'Source' => '',
                'Rev1Amt' => 0,
                'Rev1Date' => '',
                'Rev1Phone' => '',
                'Rev1Time' => '',
                'Rev1By' => '',
                'Rev2Amt' => 0,
                'Rev2Date' => '',
                'Rev2Phone' => '',
                'Rev2Time' => '',
                'Rev2By' => '',
                'CellPhone' => '**********',
                'UserFee1' => 0.0000,
                'UserFee2' => 0.0000,
                'UserFee3' => 0.0000,
                'LastFirst' => 'SMITH, JOHN',
                'customerfirst' => 'JOHN',
                'customerlast' => 'SMITH',
                'DriveType' => 'RWD',
                'TotalFees' => 0.0000,
                'Fax' => '',
                'Subtotal' => 327.17,
                'CB' => '',
                'DateInspection' => '0000-00-00',
                'DateAuthorization' => '0000-00-00',
                'DateParts' => '0000-00-00',
                'DateWork' => '0000-00-00',
                'DateInProcess' => '0000-00-00',
                'DateHold' => '0000-00-00',
                'DateFinal' => '0000-00-00',
                'DateDelivered' => '0000-00-00',
                'DateClosed' => '0000-00-00',
                'OrigRO' => '0000-00-00',
                'OrigTech' => '',
                'PartsOrdered' => '',
                'FinalDate' => '2025-04-03',
                'Exported' => '',
                'LaborTaxRate' => 0.0000,
                'SubletTaxRate' => 0.0000,
                'complainttable' => 'yes',
                'gp' => 0.0000,
                'contact' => '',
                'balance' => 200.01,
                'fleetno' => '',
            ],
            [
                'shopid' => $shopid,
                'ROID' => 1002,
                'CustomerID' => 1,
                'Writer' => 'RON SMITH',
                'DateIn' => Carbon::now()->format('Y-m-d'),
                'TimeIn' => '08:01:00',
                'TaxRate' => 7.75,
                'PurchaseOrderNumber' => '',
                'VehID' => 1,
                'Customer' => 'JOHN SMITH',
                'VehInfo' => '2003 MERCEDES-BENZ E-CLASS E320 SEDAN 4-DR',
                'vehyear' => '2003',
                'vehmake' => 'MERCEDES-BENZ',
                'vehmodel' => 'E-CLASS E320 SEDAN 4-DR',
                'vehlicense' => 'KDKDKDK',
                'vehstate' => 'CA',
                'VehLicNum' => 'KDKDKDK - CA',
                'WrittenBy' => 0,
                'Status' => '1INSPECTION',
                'StatusDate' => Carbon::now()->format('Y-m-d'),
                'TotalLbrHrs' => 0,
                'TotalLbr' => 0,
                'TotalPrts' => 0,
                'TotalSublet' => 0,
                'TotalRO' => 0,
                'CustomerAddress' => '1234 MAIN ST',
                'CustomerCSZ' => 'POWAY, CA. 92064',
                'CustomerPhone' => '**********',
                'customercity' => 'POWAY',
                'customerstate' => 'CA',
                'customerzip' => '92064',
                'VehicleMiles' => '',
                'MilesOut' => '',
                'Vin' => 'WDBUF65J13A233645',
                'CustomerWork' => '**********',
                'MajorComplaint' => 'The Air Filter needs replacement., The vehicle needs the front wheels aligned., ',
                'DatePromised' => '0000-00-00',
                'Comments' => '',
                'DiscountAmt' => 0,
                'DiscountPercent' => 0,
                'WarrMos' => 0,
                'WarrMiles' => 0,
                'SalesTax' => 0,
                'PartsCost' => 0,
                'ROType' => 'Customer Pay',
                'VehEngine' => '3.2L V6 SOHC 18V',
                'Cyl' => 6,
                'VehTrans' => 'AUTO',
                'HowPaid' => '',
                'AmtPaid1' => 0,
                'CheckNum1' => '',
                'HowPaid2' => '',
                'AmtPaid2' => 0,
                'CheckNum2' => '',
                'EstimateAmt' => 0,
                'NoFollow' => '',
                'AccountPaid' => '',
                'HazardousWaste' => '',
                'Source' => '',
                'Rev1Amt' => 0,
                'Rev1Date' => '',
                'Rev1Phone' => '',
                'Rev1Time' => '',
                'Rev1By' => '',
                'Rev2Amt' => 0,
                'Rev2Date' => '',
                'Rev2Phone' => '',
                'Rev2Time' => '',
                'Rev2By' => '',
                'CellPhone' => '**********',
                'UserFee1' => 0.0000,
                'UserFee2' => 0.0000,
                'UserFee3' => 0.0000,
                'LastFirst' => 'SMITH, JOHN',
                'customerfirst' => 'JOHN',
                'customerlast' => 'SMITH',
                'DriveType' => 'RWD',
                'TotalFees' => 0.0000,
                'Fax' => '',
                'Subtotal' => 0.0000,
                'CB' => '',
                'DateInspection' => '0000-00-00',
                'DateAuthorization' => '0000-00-00',
                'DateParts' => '0000-00-00',
                'DateWork' => '0000-00-00',
                'DateInProcess' => '0000-00-00',
                'DateHold' => '0000-00-00',
                'DateFinal' => '0000-00-00',
                'DateDelivered' => '0000-00-00',
                'DateClosed' => '0000-00-00',
                'OrigRO' => '0000-00-00',
                'OrigTech' => '',
                'PartsOrdered' => '',
                'FinalDate' => '0000-00-00',
                'Exported' => '',
                'LaborTaxRate' => 0.0000,
                'SubletTaxRate' => 0.0000,
                'complainttable' => 'yes',
                'gp' => 0.0000,
                'contact' => '',
                'balance' => 0.0000,
                'fleetno' => '',
            ],
            [
                'shopid' => $shopid,
                'ROID' => 1003,
                'CustomerID' => 8,
                'Writer' => 'RON SMITH',
                'DateIn' => Carbon::now()->format('Y-m-d'),
                'TimeIn' => '10:22:00',
                'TaxRate' => 7.75,
                'PurchaseOrderNumber' => '',
                'VehID' => 3,
                'Customer' => 'BUNKER HILL',
                'VehInfo' => '2004 JEEP WRANGLER X SPORT UTILITY 2-DR',
                'vehyear' => '2004',
                'vehmake' => 'JEEP',
                'vehmodel' => 'WRANGLER X SPORT UTILITY 2-DR',
                'vehlicense' => '456211',
                'vehstate' => 'CA',
                'VehLicNum' => '456211 - CA',
                'WrittenBy' => 0,
                'Status' => '1INSPECTION',
                'StatusDate' => Carbon::now()->format('Y-m-d'),
                'TotalLbrHrs' => 0,
                'TotalLbr' => 373.8,
                'TotalPrts' => 127.5,
                'TotalSublet' => 0,
                'TotalRO' => 510.72,
                'CustomerAddress' => '4444 MAIN ST',
                'CustomerCSZ' => 'SAN DIEGO, CA. 92101',
                'CustomerPhone' => '**********',
                'customercity' => 'SAN DIEGO',
                'customerstate' => 'CA',
                'customerzip' => '92101',
                'VehicleMiles' => '',
                'MilesOut' => '',
                'Vin' => '1J4FA39S74P775089',
                'CustomerWork' => '',
                'MajorComplaint' => 'The vehicle needs the front wheels aligned., The left front wheel bearing needs replacement., The Accessory Drive Belt needs replacement., ',
                'DatePromised' => '0000-00-00',
                'Comments' => '',
                'DiscountAmt' => 0,
                'DiscountPercent' => 0,
                'WarrMos' => 3,
                'WarrMiles' => 3000,
                'SalesTax' => 9.42,
                'PartsCost' => 56.0,
                'ROType' => 'Customer Pay',
                'VehEngine' => '4.0L L6 OHV 12V',
                'Cyl' => 6,
                'VehTrans' => 'AUTO',
                'HowPaid' => '',
                'AmtPaid1' => 0,
                'CheckNum1' => '',
                'HowPaid2' => '',
                'AmtPaid2' => 0,
                'CheckNum2' => '',
                'EstimateAmt' => 0,
                'NoFollow' => '',
                'AccountPaid' => '',
                'HazardousWaste' => '',
                'Source' => '',
                'Rev1Amt' => 0,
                'Rev1Date' => '',
                'Rev1Phone' => '',
                'Rev1Time' => '',
                'Rev1By' => '',
                'Rev2Amt' => 0,
                'Rev2Date' => '',
                'Rev2Phone' => '',
                'Rev2Time' => '',
                'Rev2By' => '',
                'CellPhone' => '',
                'UserFee1' => 0.0000,
                'UserFee2' => 0.0000,
                'UserFee3' => 0.0000,
                'LastFirst' => 'HILL, BUNKER',
                'customerfirst' => 'BUNKER',
                'customerlast' => 'HILL',
                'DriveType' => 'RWD',
                'TotalFees' => 0.0000,
                'Fax' => '',
                'Subtotal' => 501.30,
                'CB' => '',
                'DateInspection' => '0000-00-00',
                'DateAuthorization' => '0000-00-00',
                'DateParts' => '0000-00-00',
                'DateWork' => '0000-00-00',
                'DateInProcess' => '0000-00-00',
                'DateHold' => '0000-00-00',
                'DateFinal' => '0000-00-00',
                'DateDelivered' => '0000-00-00',
                'DateClosed' => '0000-00-00',
                'OrigRO' => '0000-00-00',
                'OrigTech' => '',
                'PartsOrdered' => '',
                'FinalDate' => '0000-00-00',
                'Exported' => '',
                'LaborTaxRate' => 0.0000,
                'SubletTaxRate' => 0.0000,
                'complainttable' => 'yes',
                'gp' => 0.0000,
                'contact' => '',
                'balance' => 510.72,
                'fleetno' => '',
            ],
        ];

        DB::table('repairorders')->insert($repairOrders);
    }
}
