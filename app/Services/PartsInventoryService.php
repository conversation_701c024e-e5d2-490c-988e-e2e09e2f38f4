<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class PartsInventoryService
{
    public function insertPartsInventory($shopid)
    {
        DB::table('partsinventory')->insert([
            'shopid' => $shopid,
            'PartNumber' => 'Filter',
            'PartDesc' => 'Oil Filter',
            'PartCost' => '0.00',
            'PartPrice' => '0.00',
            'OnHand' => '1',
            'PartSupplier' => 'JUST IN TIME',
        ]);
    }
}
