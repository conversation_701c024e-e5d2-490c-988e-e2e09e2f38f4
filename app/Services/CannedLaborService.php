<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class CannedLaborService
{
    public function insertCannedLabor($shopid, $cannedID)
    {
        DB::table('cannedlabor')->insert([
            [
                'shopid' => $shopid,
                'cannedjobsid' => $cannedID,
                'labor' => 'Oil Change',
                'laborhours' => '0.3',
            ],
            [
                'shopid' => $shopid,
                'cannedjobsid' => $cannedID,
                'labor' => 'Courtesy Brake and Multi-Point Inspection performed, lube chassis - where applicable, check and set tire pressure',
                'laborhours' => '0.0',
            ],
        ]);
    }
}
