<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class JobDescriptionsService
{
    public function insertJobDescriptions($shopid)
    {
        $jobDescs = [
            ['shopid' => $shopid, 'JobDesc' => 'General Labor'],
            ['shopid' => $shopid, 'JobDesc' => 'General Manager'],
            ['shopid' => $shopid, 'JobDesc' => 'Office (Clerical)'],
            ['shopid' => $shopid, 'JobDesc' => 'Office Manager'],
            ['shopid' => $shopid, 'JobDesc' => 'Owner'],
            ['shopid' => $shopid, 'JobDesc' => 'Service Advisor'],
            ['shopid' => $shopid, 'JobDesc' => 'Service Technician'],
        ];

        DB::table('jobdesc')->insert($jobDescs);
    }
}
