<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class CategoriesService
{
    public function insertCategories($shopid)
    {
        $categories = [
            ['shopid' => $shopid, 'Category' => 'GENERAL PART', 'Factor' => '3', 'Start' => '0.01', 'End' => '2.99'],
            ['shopid' => $shopid, 'Category' => 'DEALER PART', 'Factor' => '1.4', 'Start' => '0.01', 'End' => '2.99'],
            ['shopid' => $shopid, 'Category' => 'DEALER PART', 'Factor' => '1.4', 'Start' => '3', 'End' => '7.99'],
            ['shopid' => $shopid, 'Category' => 'DEALER PART', 'Factor' => '1.4', 'Start' => '8', 'End' => '39.99'],
            ['shopid' => $shopid, 'Category' => 'DEALER PART', 'Factor' => '1.4', 'Start' => '40', 'End' => '199.99'],
            ['shopid' => $shopid, 'Category' => 'DEALER PART', 'Factor' => '1.4', 'Start' => '200', 'End' => '5000'],
            ['shopid' => $shopid, 'Category' => 'GENERAL PART', 'Factor' => '2.5', 'Start' => '3', 'End' => '19.99'],
            ['shopid' => $shopid, 'Category' => 'GENERAL PART', 'Factor' => '2.25', 'Start' => '20', 'End' => '99.99'],
            ['shopid' => $shopid, 'Category' => 'GENERAL PART', 'Factor' => '2', 'Start' => '100', 'End' => '499.99'],
            ['shopid' => $shopid, 'Category' => 'GENERAL PART', 'Factor' => '1.75', 'Start' => '500', 'End' => '5000'],
            ['shopid' => $shopid, 'Category' => 'NON MATRIX', 'Factor' => '1', 'Start' => '0.01', 'End' => '5999.99'],
            ['shopid' => $shopid, 'Category' => '40% MARKUP', 'Factor' => '1.40', 'Start' => '0.01', 'End' => '5999'],
        ];

        DB::table('category')->insert($categories);
    }
}
