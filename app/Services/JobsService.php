<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class JobsService
{
    public function insertJobs($shopid)
    {
        $jobs = [
            ['shortdescription' => 'AC - Inspection', 'description' => 'The Air Conditioning System needs to be inspected for damage.', 'shopid' => $shopid],
            ['shortdescription' => 'AC - Not Cold', 'description' => 'The Air Conditioning System isn\'t cooling properly.', 'shopid' => $shopid],
            ['shortdescription' => 'AC - Service', 'description' => 'The Air Conditioning System needs service.', 'shopid' => $shopid],
            ['shortdescription' => 'Air Filter - NR', 'description' => 'The Air Filter needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Alarm - Problem', 'description' => 'The Alarm System is not working properly.', 'shopid' => $shopid],
            ['shortdescription' => 'Alarm - Problem', 'description' => 'The Alarm System is not working properly.', 'shopid' => $shopid],
            ['shortdescription' => 'Alignment - 4 Wheel', 'description' => 'The vehicle needs a four wheel alignment.', 'shopid' => $shopid],
            ['shortdescription' => 'Alignment - Front', 'description' => 'The vehicle needs the front wheels aligned.', 'shopid' => $shopid],
            ['shortdescription' => 'Alignment - Pulls Left', 'description' => 'The vehicle pulls to the left while driving.', 'shopid' => $shopid],
            ['shortdescription' => 'Alignment - Pulls Right', 'description' => 'The vehicle pulls to the right while driving.', 'shopid' => $shopid],
            ['shortdescription' => 'Antenna - NR', 'description' => 'The Radio Antenna needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Bearing - Left Front - NR', 'description' => 'The left front wheel bearing needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Bearing - Left Rear Axle - NR', 'description' => 'The Left Rear Axle Bearing needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Bearing - Right Front - NR', 'description' => 'The Right Front Wheel Bearing needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Bearing - Right Rear Axle - NR', 'description' => 'The Right Rear Axle Bearing needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Bearings - Front Wheel - Noise', 'description' => 'The Front Wheel Bearings are making noise.', 'shopid' => $shopid],
            ['shortdescription' => 'Bearings - Rear Axle - Noise', 'description' => 'The Rear Axle Bearings are making noise.', 'shopid' => $shopid],
            ['shortdescription' => 'Belt - Accessory Drive - NR', 'description' => 'The Accessory Drive Belt needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Belt - Air Cond - NR', 'description' => 'The Air Conditioning Drive Belt needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Belt - Alternator - NR', 'description' => 'The Alternator Drive Belt needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Belt - Power Steering - NR', 'description' => 'The Power Steering Drive belt needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Belt - Timing - NR', 'description' => 'The Timing Belt needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Body', 'description' => 'Paint Damage', 'shopid' => $shopid],
            ['shortdescription' => 'Body - Damage', 'description' => 'There is Body damage on the', 'shopid' => $shopid],
            ['shortdescription' => 'Body - Exterior Wash', 'description' => 'Exterior Wash.', 'shopid' => $shopid],
            ['shortdescription' => 'Body - Full Detail', 'description' => 'Full Detail.', 'shopid' => $shopid],
            ['shortdescription' => 'Brake Lights - Problem', 'description' => 'The Brake Lights are not working properly.', 'shopid' => $shopid],
            ['shortdescription' => 'Brake Line - Leaking', 'description' => 'The Brake Line is leaking on the', 'shopid' => $shopid],
            ['shortdescription' => 'Brake Service - Front', 'description' => 'The Front Brakes need service.', 'shopid' => $shopid],
            ['shortdescription' => 'Brake Service - Front/Rear', 'description' => 'The Front and Rear Brakes need service.', 'shopid' => $shopid],
            ['shortdescription' => 'Brake Service - Rear', 'description' => 'The Rear Brakes need service.', 'shopid' => $shopid],
            ['shortdescription' => 'Brakes - Grind', 'description' => 'The Brakes make a grinding noise when stopping.', 'shopid' => $shopid],
            ['shortdescription' => 'Brakes - Inspection', 'description' => 'The Brake System needs inspection.', 'shopid' => $shopid],
            ['shortdescription' => 'Brakes - Pull Left', 'description' => 'The Brakes pull to the left when stopping.', 'shopid' => $shopid],
            ['shortdescription' => 'Brakes - Pull Right', 'description' => 'The Brakes pull to the right when stopping.', 'shopid' => $shopid],
            ['shortdescription' => 'Brakes - Squeal - Driving', 'description' => 'The Brakes squeal when driving.', 'shopid' => $shopid],
            ['shortdescription' => 'Brakes - Squeal - Stopping', 'description' => 'The Brakes squeal when stopping.', 'shopid' => $shopid],
            ['shortdescription' => 'Catalytic Converter - NR', 'description' => 'The Catalytic Converter needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Clutch - Adjustment', 'description' => 'The Clutch needs adjustment.', 'shopid' => $shopid],
            ['shortdescription' => 'Clutch - Chattering', 'description' => 'The Clutch is chattering upon acceleration.', 'shopid' => $shopid],
            ['shortdescription' => 'Clutch - NR', 'description' => 'The Clutch needs repair or replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Clutch - Slipping', 'description' => 'The Clutch is slipping.', 'shopid' => $shopid],
            ['shortdescription' => 'Code - Alarm', 'description' => 'The Alarm Code is', 'shopid' => $shopid],
            ['shortdescription' => 'Code - Alarm', 'description' => 'The Alarm Code is', 'shopid' => $shopid],
            ['shortdescription' => 'Code - Radio', 'description' => 'The Radio Code is', 'shopid' => $shopid],
            ['shortdescription' => 'Code - Radio', 'description' => 'The Radio Code is', 'shopid' => $shopid],
            ['shortdescription' => 'Cooling Sys - Overheats/Driving', 'description' => 'The Cooling system overheats when driving.', 'shopid' => $shopid],
            ['shortdescription' => 'Cooling Sys - Overheats/Stopped', 'description' => 'The Cooling System overheats when stopped.', 'shopid' => $shopid],
            ['shortdescription' => 'Cooling System - Inspection', 'description' => 'The Cooling System needs to be inspected for damage.', 'shopid' => $shopid],
            ['shortdescription' => 'Cooling System - Service', 'description' => 'The Cooling System needs service.', 'shopid' => $shopid],
            ['shortdescription' => 'CV Joint - Left Front - Noise', 'description' => 'The Left Front CV Joint is making noise.', 'shopid' => $shopid],
            ['shortdescription' => 'CV Joint - Left Front - NR', 'description' => 'The Left Front CV Joint needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'CV Joint - Rt Front - Noise', 'description' => 'The Right Front CV Joint is making noise.', 'shopid' => $shopid],
            ['shortdescription' => 'CV Joint - Rt Front - NR', 'description' => 'The Right Front CV Joint needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Differential - Noise', 'description' => 'The Differential is making noise.', 'shopid' => $shopid],
            ['shortdescription' => 'Distributor Cap - NR', 'description' => 'The Distributor Cap needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Drive-ability - Electronic Diagnosis', 'description' => 'The vehicle needs an Electronic Analysis to diagnose engine running problems and vehicle drive-ability.', 'shopid' => $shopid],
            ['shortdescription' => 'Drive-ability - Engine Runs Rough', 'description' => 'The vehicle has drive-ability problems and the engine isn\'t running smoothly.', 'shopid' => $shopid],
            ['shortdescription' => 'Drive-ability - Won\'t Start', 'description' => 'The vehicle won\'t start.', 'shopid' => $shopid],
            ['shortdescription' => 'Engine - Black Smoke', 'description' => 'Accelerating', 'shopid' => $shopid],
            ['shortdescription' => 'Engine - Blue Smoke', 'description' => 'Accelerating', 'shopid' => $shopid],
            ['shortdescription' => 'Engine - Gray Smoke', 'description' => 'Accelerating', 'shopid' => $shopid],
            ['shortdescription' => 'Engine - Knock - Lower End', 'description' => 'The engine has a knock in the lower end.', 'shopid' => $shopid],
            ['shortdescription' => 'Engine - Knock/Tapping - Upper End', 'description' => 'The engine has a knock or tapping in the upper end.', 'shopid' => $shopid],
            ['shortdescription' => 'Engine - Rebuild', 'description' => 'The engine needs a complete rebuild.', 'shopid' => $shopid],
            ['shortdescription' => 'Exhaust Hangers - NR', 'description' => 'The Exhaust System Hangers need replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Exhaust System - Leak', 'description' => 'There is a leak in the Exhaust System.', 'shopid' => $shopid],
            ['shortdescription' => 'Exhaust System - NR', 'description' => 'The Exhaust System needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Flange Gasket - Leaking', 'description' => 'The Manifold Flange Gasket is leaking.', 'shopid' => $shopid],
            ['shortdescription' => 'Front Shocks - NR', 'description' => 'The Front Shocks need replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Front Springs - NR', 'description' => 'The Front Springs need replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Front Windshield - Damaged', 'description' => 'The Front Windshield is damaged.', 'shopid' => $shopid],
            ['shortdescription' => 'Front Windshield - NR', 'description' => 'The Front Windshield needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Fuel Filter - NR', 'description' => 'The Fuel Filter needs replacement', 'shopid' => $shopid],
            ['shortdescription' => 'Fuel Filter - NR', 'description' => 'The Fuel Filter needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Fuel Injection - Service', 'description' => 'The Fuel Injection system needs to be cleaned and serviced.', 'shopid' => $shopid],
            ['shortdescription' => 'Fuel Lines - Leaking', 'description' => 'The Fuel Lines are leaking.', 'shopid' => $shopid],
            ['shortdescription' => 'Fuel Pump - NR', 'description' => 'The Fuel Pump needs to be repaired or replaced.', 'shopid' => $shopid],
            ['shortdescription' => 'Fuel System', 'description' => 'Clean', 'shopid' => $shopid],
            ['shortdescription' => 'Fuel System - Leaking', 'description' => 'The Fuel System needs to be inspected for leaks.', 'shopid' => $shopid],
            ['shortdescription' => 'Headlight - LF - Problem', 'description' => 'The Left Front Headlight is not working properly.', 'shopid' => $shopid],
            ['shortdescription' => 'Headlight - RF - Problem', 'description' => 'The Right Front Headlight is not working properly.', 'shopid' => $shopid],
            ['shortdescription' => 'Headlights - Problem', 'description' => 'The Headlights are not working properly.', 'shopid' => $shopid],
            ['shortdescription' => 'Heater - Not Hot', 'description' => 'The Heating System not working properly.', 'shopid' => $shopid],
            ['shortdescription' => 'Heater Core - Leaking', 'description' => 'The Heater Core is leaking fluid.', 'shopid' => $shopid],
            ['shortdescription' => 'Heater Hose - NR', 'description' => 'The Heater Hose is bad and needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Heater Hoses - Leaking', 'description' => 'The Heater Hoses are leaking fluid.', 'shopid' => $shopid],
            ['shortdescription' => 'Horn - Problem', 'description' => 'The Horn is not working properly.', 'shopid' => $shopid],
            ['shortdescription' => 'Ignition - Problems', 'description' => 'The vehicle has Ignition problems.  It is not starting and/or running properly.', 'shopid' => $shopid],
            ['shortdescription' => 'Interior', 'description' => 'Full Detail', 'shopid' => $shopid],
            ['shortdescription' => 'Interior Lights - Problem', 'description' => 'The Interior Lights are not working properly.', 'shopid' => $shopid],
            ['shortdescription' => 'Labor & Parts', 'description' => 'Labor Services and Associated Parts.', 'shopid' => $shopid],
            ['shortdescription' => 'LOF - Service', 'description' => 'The vehicle needs a Chassis Lubrication', 'shopid' => $shopid],
            ['shortdescription' => 'Manifold - Cracked', 'description' => 'The Exhaust Manifold is cracked and leaking.', 'shopid' => $shopid],
            ['shortdescription' => 'Manifold Gasket - Leaking', 'description' => 'The Exhaust Manifold Gasket is leaking.', 'shopid' => $shopid],
            ['shortdescription' => 'Mirror - Left - NR', 'description' => 'The Left Outside Mirror needs repair or replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Mirror - Right - NR', 'description' => 'The Right Outside Mirror needs repair or replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Muffler - NR', 'description' => 'The Muffler needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Oil & Filter - Service', 'description' => 'The vehicle needs an Oil and Filter Service.', 'shopid' => $shopid],
            ['shortdescription' => 'Oxygen Sensor - NR', 'description' => 'The Oxygen Sensor needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Power Steering Hose - NR', 'description' => 'The Power Steering Hose needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Power Steering Pump - NR', 'description' => 'The Power Steering Pump needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Power Window - NR', 'description' => 'The Power Window mechanism is not working properly on the', 'shopid' => $shopid],
            ['shortdescription' => 'Radiator - Leaking', 'description' => 'The Radiator is leaking and needs inspection to determine the cause.', 'shopid' => $shopid],
            ['shortdescription' => 'Radiator - NR', 'description' => 'The Radiator is bad and needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Radiator - Rod Out', 'description' => 'The Radiator has blocked passages and needs to be rodded out.', 'shopid' => $shopid],
            ['shortdescription' => 'Radiator Hose - Bypass NR', 'description' => 'The Bypass Hose needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Radiator Hose - Lower NR', 'description' => 'The Lower Radiator Hose needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Radiator Hose - Upper NR', 'description' => 'The Upper Radiator Hose needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Radio - Problem', 'description' => 'The Radio is not working properly.', 'shopid' => $shopid],
            ['shortdescription' => 'Rear Shocks - NR', 'description' => 'The Rear Shocks need replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Rear Springs - NR', 'description' => 'The Rear Springs need replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Rear Window - Damage', 'description' => 'The Rear Window is damaged.', 'shopid' => $shopid],
            ['shortdescription' => 'RFCs & VPs', 'description' => 'Requests for Service & Vehicle Problems -', 'shopid' => $shopid],
            ['shortdescription' => 'Safety Inspection', 'description' => 'The Vehicle needs a Safety Inspection.', 'shopid' => $shopid],
            ['shortdescription' => 'Service - 15K', 'description' => 'The vehicle needs a 15000 mile Maintenance Service.', 'shopid' => $shopid],
            ['shortdescription' => 'Service - 30K', 'description' => 'The vehicle needs a 30000 mile Maintenance Service.', 'shopid' => $shopid],
            ['shortdescription' => 'Service - 60K', 'description' => 'The vehicle needs a 60000 mile Maintenance Service.', 'shopid' => $shopid],
            ['shortdescription' => 'Side Window  - Damaged', 'description' => 'The Side Window is damaged.', 'shopid' => $shopid],
            ['shortdescription' => 'Smog - Inspection/Certification', 'description' => 'The vehicle needs a Smog Inspection and State Certification.', 'shopid' => $shopid],
            ['shortdescription' => 'Smog Inspection', 'description' => 'The vehicle needs a Smog Inspection and State Certification.', 'shopid' => $shopid],
            ['shortdescription' => 'Smog Pump - NR', 'description' => 'The Smog Pump needs repair or replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Spark Plug Wires - NR', 'description' => 'The Spark Plug Wires need replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Spark Plugs - NR', 'description' => 'The Spark Plugs need replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Struts - NR', 'description' => 'The vehicle needs the Suspension Struts to be replaced.', 'shopid' => $shopid],
            ['shortdescription' => 'Tail Light - LR - Problem', 'description' => 'The Left Rear Tail Light is not working properly.', 'shopid' => $shopid],
            ['shortdescription' => 'Tail Light - RR - Problem', 'description' => 'The Right Rear Tail Light is not working properly.', 'shopid' => $shopid],
            ['shortdescription' => 'Tail Lights - Problem', 'description' => 'The Tail Lights are not working properly.', 'shopid' => $shopid],
            ['shortdescription' => 'Thermostat - NR', 'description' => 'The Thermostat needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Throw-Out Bearing - Noise', 'description' => 'The Clutch throw-out bearing is making noise.', 'shopid' => $shopid],
            ['shortdescription' => 'Throw-Out Bearing NR', 'description' => 'The Throw-Out Bearing needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Tire - Repair - LF', 'description' => 'The Left Front Tire needs repair.', 'shopid' => $shopid],
            ['shortdescription' => 'Tire - Repair - LR', 'description' => 'The Left Rear Tire needs repair.', 'shopid' => $shopid],
            ['shortdescription' => 'Tire - Repair - RF', 'description' => 'The Right Front Tire needs repair.', 'shopid' => $shopid],
            ['shortdescription' => 'Tire - Repair - RR', 'description' => 'The Right Rear Tire needs repair.', 'shopid' => $shopid],
            ['shortdescription' => 'Tires - Replace - 2 Front', 'description' => 'Both front tires need replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Tires - Replace - 2 Rear', 'description' => 'Both rear tires need replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Tires - Replace - All 4', 'description' => 'All the tires on the vehicle need replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Tires - Replace - LF', 'description' => 'The Left Front Tire needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Tires - Replace - LR', 'description' => 'The Left Rear Tire needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Tires - Replace - RF', 'description' => 'The Right Front Tire needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Tires - Replace - RR', 'description' => 'The Right Rear Tire needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Tires - Rotate - All 4', 'description' => 'The Tires need rotating.', 'shopid' => $shopid],
            ['shortdescription' => 'Tires - Warranty Repair', 'description' => 'Tire needs Warranty Repair.', 'shopid' => $shopid],
            ['shortdescription' => 'Tires - Warranty Replacement', 'description' => 'Tire needs Warranty Replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Transaxle - Inspection', 'description' => 'The Transaxle needs to be inspected for damage.', 'shopid' => $shopid],
            ['shortdescription' => 'Transaxle - No Drive', 'description' => 'The Transaxle doesn\'t move vehicle forward in DRIVE.', 'shopid' => $shopid],
            ['shortdescription' => 'Transaxle - No Reverse', 'description' => 'The Transaxle doesn\'t move vehicle backwards in REVERSE.', 'shopid' => $shopid],
            ['shortdescription' => 'Transaxle - Rebuild', 'description' => 'The Transaxle needs to be rebuilt.', 'shopid' => $shopid],
            ['shortdescription' => 'Transaxle - Service', 'description' => 'The Transaxle needs standard Maintenance Service.', 'shopid' => $shopid],
            ['shortdescription' => 'Transaxle - Shifting Problems', 'description' => 'The Transaxle has shifting problems.', 'shopid' => $shopid],
            ['shortdescription' => 'Transaxle - Slips in Drive', 'description' => 'The Transaxle slips when in DRIVE.', 'shopid' => $shopid],
            ['shortdescription' => 'Transaxle - Slips in Reverse', 'description' => 'The Transaxle slips when in REVERSE.', 'shopid' => $shopid],
            ['shortdescription' => 'Transmission - Inspection', 'description' => 'The Transmission needs to be inspected for damage.', 'shopid' => $shopid],
            ['shortdescription' => 'Transmission - No Drive', 'description' => 'The Transmission doesn\'t move vehicle forward in DRIVE.', 'shopid' => $shopid],
            ['shortdescription' => 'Transmission - No Reverse', 'description' => 'The Transmission doesn\'t move vehicle backwards in REVERSE.', 'shopid' => $shopid],
            ['shortdescription' => 'Transmission - Rebuild', 'description' => 'The Transmission needs to be rebuilt.', 'shopid' => $shopid],
            ['shortdescription' => 'Transmission - Service', 'description' => 'The Transmission needs standard Maintenance Service.', 'shopid' => $shopid],
            ['shortdescription' => 'Transmission - Service', 'description' => 'The Transmission needs a standard maintenance service.', 'shopid' => $shopid],
            ['shortdescription' => 'Transmission - Shifting Problems', 'description' => 'The Transmission has shifting problems.', 'shopid' => $shopid],
            ['shortdescription' => 'Transmission - Slips in Drive', 'description' => 'The Transmission slips when in DRIVE.', 'shopid' => $shopid],
            ['shortdescription' => 'Transmission - Slips in Reverse', 'description' => 'The Transmission slips when in REVERSE.', 'shopid' => $shopid],
            ['shortdescription' => 'Turn Signal Blinker - Problem', 'description' => 'The Turn Signals are not blinking properly.', 'shopid' => $shopid],
            ['shortdescription' => 'Turn Signal Lights - Problem', 'description' => 'The Turn Signal Lights are not working properly.', 'shopid' => $shopid],
            ['shortdescription' => 'U Joint - Front - NR', 'description' => 'The Front U Joint needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'U Joint - Rear - NR', 'description' => 'The Rear U Joint needs replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Washer Pump - Problem', 'description' => 'The Windshield Washer Pump is not working properly.', 'shopid' => $shopid],
            ['shortdescription' => 'Wiper Blades - NR', 'description' => 'The Wiper Blades need replacement.', 'shopid' => $shopid],
            ['shortdescription' => 'Wiper Motor - Problem', 'description' => 'The Wiper Motor is not working properly.', 'shopid' => $shopid],
            ['shortdescription' => 'Wiper Switch - Problem', 'description' => 'The Wiper Switch is not working properly.', 'shopid' => $shopid],
        ];

        DB::table('jobs')->insert($jobs);
    }
}
