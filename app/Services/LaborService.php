<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class LaborService
{
    public function insertLabor($shopid)
    {
        $laborRecords = [
            [
                'shopid' => $shopid,
                'LaborID' => 1,
                'ROID' => 1001,
                'HourlyRate' => 89,
                'LaborHours' => 2,
                'Labor' => 'REPLACE FRONT BRAKES',
                'Tech' => 'SMITH, RON',
                'LineTotal' => 178,
                'LaborOp' => '',
                'complaintid' => 3,
                'techrate' => 0,
            ],
            [
                'shopid' => $shopid,
                'LaborID' => 12,
                'ROID' => 1003,
                'HourlyRate' => 89,
                'LaborHours' => 0.5,
                'Labor' => 'Replace Air Filter',
                'Tech' => 'SMITH, RON',
                'LineTotal' => 44.5,
                'LaborOp' => '',
                'complaintid' => 32,
                'techrate' => 0,
            ],
            [
                'shopid' => $shopid,
                'LaborID' => 13,
                'ROID' => 1003,
                'HourlyRate' => 89,
                'LaborHours' => 0.2,
                'Labor' => 'Oil and Filter Change',
                'Tech' => 'SMITH, RON',
                'LineTotal' => 17.8,
                'LaborOp' => '',
                'complaintid' => 32,
                'techrate' => 0,
            ],
            [
                'shopid' => $shopid,
                'LaborID' => 14,
                'ROID' => 1003,
                'HourlyRate' => 89,
                'LaborHours' => 1.5,
                'Labor' => 'Replace 4 Spark Plugs',
                'Tech' => 'SMITH, RON',
                'LineTotal' => 133.5,
                'LaborOp' => '',
                'complaintid' => 32,
                'techrate' => 0,
            ],
            [
                'shopid' => $shopid,
                'LaborID' => 15,
                'ROID' => 1003,
                'HourlyRate' => 89,
                'LaborHours' => 0.5,
                'Labor' => 'Check tires and wheel alignment',
                'Tech' => 'SMITH, RON',
                'LineTotal' => 44.5,
                'LaborOp' => '',
                'complaintid' => 32,
                'techrate' => 0,
            ],
            [
                'shopid' => $shopid,
                'LaborID' => 16,
                'ROID' => 1003,
                'HourlyRate' => 89,
                'LaborHours' => 1.5,
                'Labor' => 'REPLACE DRIVE BELT',
                'Tech' => 'SMITH, RON',
                'LineTotal' => 133.5,
                'LaborOp' => '',
                'complaintid' => 34,
                'techrate' => 0,
            ],
        ];

        DB::table('labor')->insert($laborRecords);
    }
}
