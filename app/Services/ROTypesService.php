<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class ROTypesService
{
    public function insertROTypes($shopid)
    {
        $roTypes = [
            ['shopid' => $shopid, 'ROType' => 'Internal'],
            ['shopid' => $shopid, 'ROType' => 'No Approval'],
            ['shopid' => $shopid, 'ROType' => 'No Problem'],
            ['shopid' => $shopid, 'ROType' => 'No Charge'],
            ['shopid' => $shopid, 'ROType' => 'Customer Pay'],
        ];

        DB::table('rotype')->insert($roTypes);
    }
}
