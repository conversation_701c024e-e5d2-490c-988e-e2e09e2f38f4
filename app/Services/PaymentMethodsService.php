<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class PaymentMethodsService
{
    public function insertPaymentMethods($shopid)
    {
        DB::table('paymentmethods')->insert([
            ['shopid' => $shopid, 'method' => 'Cash'],
            ['shopid' => $shopid, 'method' => 'Check'],
            ['shopid' => $shopid, 'method' => 'Visa'],
            ['shopid' => $shopid, 'method' => 'Mastercard'],
            ['shopid' => $shopid, 'method' => 'American Express'],
            ['shopid' => $shopid, 'method' => 'Debit Card'],
        ]);
    }
}
