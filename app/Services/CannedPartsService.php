<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;

class CannedPartsService
{
    public function insertCannedParts($shopid, $cannedID)
    {
        DB::table('cannedparts')->insert([
            [
                'shopid' => $shopid,
                'partnumber' => 'Oil',
                'partdescription' => '10W30 Oil',
                'partcost' => '0.00',
                'partprice' => '0.00',
                'supplier' => 'JUST IN TIME',
                'cannedjobsid' => $cannedID,
                'qty' => '5',
            ],
            [
                'shopid' => $shopid,
                'partnumber' => 'Filter',
                'partdescription' => 'Oil Filter',
                'partcost' => '0.00',
                'partprice' => '0.00',
                'supplier' => 'JUST IN TIME',
                'cannedjobsid' => $cannedID,
                'qty' => '1',
            ],
            [
                'shopid' => $shopid,
                'partnumber' => 'LOF Fee',
                'partdescription' => 'LOF Disposal Fee',
                'partcost' => '0.00',
                'partprice' => '0.00',
                'supplier' => 'JUST IN TIME',
                'cannedjobsid' => $cannedID,
                'qty' => '1',
            ],
        ]);
    }
}
