<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\DB;

class StoreNewShopRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'salesRep' => 'required|integer',
            'name' => 'required|string|max:255',
            'firstName' => 'required|string|max:255',
            'lastName' => 'required|string|max:255',
            'password' => 'required|min:8',
            'username' => 'nullable|string|max:255|unique:employees,username',
            'email' => 'required|email|max:255|unique:company,companyemail',
            'phone' => ['required', 'string', 'regex:/^\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}$/'],
            'address' => 'required|string|max:255',
            'country' => 'required|string|max:100',
            'timeZone' => 'required|string|max:100',
            'zip' => 'required|string|max:10',
            'city' => 'required|string|max:255',
            'state' => 'required|string|max:255',
            'type' => 'nullable|string|max:100',
            'laborRate' => 'nullable|numeric|min:0',
            'partsTaxRate' => 'nullable|numeric|min:0',
            'laborTaxRate' => 'nullable|numeric|min:0',
            'subletTaxRate' => 'nullable|numeric|min:0',
            'includeTestData' => 'nullable|boolean',
            'matco' => 'nullable|boolean',
            'referralSource' => 'nullable|string|max:255',
            'referralCode' => 'nullable|string|max:255',
            'salesRepPassword' => [
                'required',
                function ($attribute, $value, $fail) {
                    $storedPassword = DB::table('adminlogin')
                        ->where('id', $this->input('salesRep'))
                        ->value('password');

                    if ($value !== $storedPassword) {
                        $fail('Sales Rep Password is not correct');
                    }
                },
            ],
            'package' => 'required|string|max:255',
        ];
    }
}
