<?php

namespace App\Http\Requests\User;

use Illuminate\Foundation\Http\FormRequest;

class UserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'name' => 'required|min:1|max:60',
            'email' => 'required|email|unique:users,email,'.$this->route('user'),
            'password' => [
                'nullable',
                'min:8',
                'max:20',
                'regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).+$/',
            ],
            'role' => 'required|in:superadmin,admin,matco,customer success,customer support,sales',
        ];
        if ($this->isMethod('post')) {
            $rules['password'][] = 'required';
        }

        return $rules;
    }

    /**
     * Custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Please provide your name.',
            'name.min' => 'Your name must be at least :min character long.',
            'name.max' => 'Your name cannot exceed :max characters.',
            'email.required' => 'An email address is required.',
            'email.unique' => 'This email address is already registered.',
            'password.required' => 'Please create a password.',
            'password.min' => 'Your password must be at least :min characters long.',
            'password.max' => 'Your password cannot exceed :max characters.',
            'password.regex' => 'Your password must include at least one uppercase letter, one lowercase letter, one number, and one special character.',
        ];
    }
}
