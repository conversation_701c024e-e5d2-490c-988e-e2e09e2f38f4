<?php

namespace App\Http\Requests\ApiKey;

use Illuminate\Foundation\Http\FormRequest;

class ApiKeyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = [
            'type' => 'required',
            'name' => 'required',
            'agent' => 'required',
        ];

        if ($this->input('type') === 'Birdeye Add' || $this->input('type') === 'Podium Add') {
            $rules['providedKey'] = 'required';
            $rules['businessId'] = 'required';
        }

        if ($this->input('type') === 'Add New API Key') {
            $rules['key'] = 'required';
        }

        return $rules;
    }
}
