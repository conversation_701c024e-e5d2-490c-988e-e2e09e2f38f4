<?php

namespace App\Http\Controllers\Shop;

use App\Http\Requests\ApiKey\ApiKeyRequest;
use App\Http\Requests\ApiKey\AutoServ1Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class APIKeyController extends Controller
{
    public function index($shop)
    {
        $this->shopid = $shop;

        return Inertia::render('apiKey/Index', [
            'shopid' => $shop,
            'companyDetails' => $this->getCompany(),
            'companies' => $this->getCompanies(),
            'joinedShops' => $this->getJoinedShops(),
            'partTech' => $this->getPartsTechShops(),
            'companyAdds' => $this->getCompanyAdds(),
            'betaFeatures' => $this->getBetaFeatures(),
        ]);
    }

    public function create($shop)
    {
        $this->shopid = $shop;

        return Inertia::render('apiKey/Create', [
            'shopid' => $shop,
            'companies' => $this->getCompanies(),
            'apiCompanies' => $this->getAPICompanies(),
            'companyDetails' => $this->getCompany(),
            'joinedShops' => $this->getJoinedShops(),
            'partTech' => $this->getPartsTechShops(),
            'companyAdds' => $this->getCompanyAdds(),
            'betaFeatures' => $this->getBetaFeatures(),
        ]);
    }

    public function destroy($shop, $id)
    {
        DB::table('apilogin')
            ->where('id', $id)
            ->where('shopid', $shop)
            ->delete();

        return redirect()->back()->with('message', `API Key by id:$id deleted!`);
    }

    public function store(ApiKeyRequest $request, $shop)
    {
        $apiLoginExists = DB::table('apilogin')
            ->where('shopid', $shop)
            ->where('companyname', $request->name)
            ->exists();

        if (! $apiLoginExists && $request->type === 'Create API Key') {
            DB::table('apilogin')->insert([
                'shopid' => $shop,
                'companyname' => $request->name,
                'apikey' => md5('drott;'.time()),
                'supportagent' => $request->agent,
            ]);

            return redirect()->back()->with('message', 'API Key has been added successfully!');
        }

        if ($request->type === 'Add New API Key') {
            DB::table('apilogin')->insert([
                'shopid' => $shop,
                'companyname' => $request->key,
                'apikey' => md5('drott;'.time()),
                'supportagent' => $request->agent,
            ]);

            return redirect()->back()->with('message', 'New API Key has been added successfully!');
        }

        if ($request->type === 'Birdeye Add' || $request->type === 'Podium Add') {
            DB::table('apilogin')->insert([
                'shopid' => $shop,
                'companyname' => $request->name,
                'apikey' => $request->businessId,
                'username' => $request->providedKey,
                'supportagent' => $request->agent,
            ]);

            return redirect()->back()->with('message', 'New API Key has been added successfully!');
        }

        if ($request->type === 'Broadly Add') {
            $apikey = 'U2hvcEJvc3M6T3FJdjJZZGIyeTV2ZlZYYUl2cmxtNnNVam9RTUJQYnAyajNNQlFJMw==';
            $newco = 'broadly';

            DB::table('apilogin')->insert([
                'shopid' => $shop,
                'companyname' => $newco,
                'apikey' => $apikey,
                'supportagent' => $request->agent,
            ]);

            return redirect()->back()->with('message', 'New API Key has been added successfully!');
        }

        if (! $apiLoginExists && $request->type === 'RepairShopSolutions Add') {
            DB::table('apilogin')->insert([
                'shopid' => $shop,
                'companyname' => $request->name,
                'apikey' => $request->key,
                'supportagent' => $request->agent,
            ]);

            return redirect()->back()->with('message', 'API Key has been added successfully!');
        }

        return redirect()->back()->with('message', 'Unable to update the API Key as an API token has already been generated!');
    }

    public function autoserv1(AutoServ1Request $request, $shop)
    {
        $autoServeShopExists = DB::table('autoserveshop')
            ->where('shopid', $shop)
            ->exists();

        if (! $autoServeShopExists) {
            DB::table('autoserveshop')->insert([
                'shopid' => $shop,
                'asid' => $request->key,
                'supportagent' => $request->agent,
            ]);

            return redirect()->back()->with('message', 'API Key has been added successfully!');
        }

        return redirect()->back()->with('message', 'Unable to update the API Key as an API token has already been generated!');
    }

    // DataTable Functions
    public function datatable($shop)
    {
        $apiLoginQuery = DB::table('apilogin')
            ->select('shopid', 'id', 'username', 'password', 'companyname', 'apikey')
            ->where('companyname', '!=', 'vendorsuppliedapikey')
            ->where('shopid', $shop);

        $autoServeShopQuery = DB::table('autoserveshop')
            ->select('shopid', 'id', DB::raw("'' as username"), DB::raw("'' as password"), DB::raw("'Autoserv1' as companyname"), 'asid as apikey')
            ->where('shopid', $shop);

        $data = $apiLoginQuery->union($autoServeShopQuery)
            ->orderBy('companyname');

        return Datatables::of($data)
            ->make(true);
    }
}
