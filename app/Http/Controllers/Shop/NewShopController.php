<?php

namespace App\Http\Controllers\Shop;

use App\Http\Requests\StoreNewShopRequest;
use App\Mail\NewAccountNotification;
use App\Models\Company;
use App\Models\CompanyCategories;
use App\Models\Employees;
use App\Services\AccountTypesService;
use App\Services\CampaignsService;
use App\Services\CannedJobsService;
use App\Services\CannedLaborService;
// Services
use App\Services\CannedPartsService;
use App\Services\CategoriesService;
use App\Services\ChartOfAccountsService;
use App\Services\CodeService;
use App\Services\ColorCodingService;
use App\Services\ComplaintcatsService;
use App\Services\ComplaintsService;
use App\Services\CustomersService;
use App\Services\DiscountReasonsService;
use App\Services\InspectionCategoriesService;
use App\Services\InspectionItemsService;
use App\Services\InspectionModelsService;
use App\Services\InspectionsService;
use App\Services\JobDescriptionsService;
use App\Services\JobsService;
use App\Services\LaborService;
use App\Services\NotificationSettingsService;
use App\Services\PartsInventoryService;
use App\Services\PartsRegistryService;
use App\Services\PartsService;
use App\Services\PaymentMethodsService;
use App\Services\PriorityService;
use App\Services\RepairOrderService;
use App\Services\RequiredFieldsService;
use App\Services\ROStatusesService;
use App\Services\ROTypesService;
use App\Services\SettingsService;
use App\Services\SourcesService;
use App\Services\SubletService;
use App\Services\SupplierService;
use App\Services\TechModeSettingsService;
use App\Services\VehicleLabelsService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Inertia\Inertia;

class NewShopController extends Controller
{
    private $roDisclosure = "Replaced parts may be requested by customer. I hereby authorize the above repair work to be done along with all necessary materials. You and your employees may operate the above vehicle for the purposes of testing, inspection or delivery at my risk. An express mechanic's lien is acknowledged on the above vehicle to secure the amount of repairs thereto. The shop will not be held responsible for loss or damage to vehicle or articles left in vehicle in case of fire, theft, accident or any other cause beyond your control. In the event legal action is necessary to enforce this contract, I understand that I am solely responsible for all costs including attorney's fees and court costs. I have read the above and acknowledge receipt of an estimate.";

    private $roWarrantyDisclosure = 'Warranty: From the date of delivery for a period of [warrantymonths] months or [warrantymiles] miles, whichever comes first, this firm will repair free of charge any defects in material and workmanship to the repairs stated on the invoice. All work to be done in our shop only. This does not include towing charges or customer supplied parts. A storage fee of [storagefee] per day will be charged 24 hours after notification that work is complete. Neglect/abuse of vehicle will immediately void any and all warranties.';

    public function __construct(
        protected AccountTypesService $accountTypesService,
        protected LaborService $laborService,
        protected CampaignsService $campaignsService,
        protected NotificationSettingsService $notificationSettingsService,
        protected CannedJobsService $cannedJobsService,
        protected CannedLaborService $cannedLaborService,
        protected PartsInventoryService $partsInventoryService,
        protected CannedPartsService $cannedPartsService,
        protected PartsRegistryService $partsRegistryService,
        protected CategoriesService $categoriesService,
        protected PartsService $partsService,
        protected ChartOfAccountsService $chartOfAccountsService,
        protected PaymentMethodsService $paymentMethodsService,
        protected CodeService $codeService,
        protected PriorityService $priorityService,
        protected ColorCodingService $colorCodingService,
        protected ComplaintcatsService $complaintcatsService,
        protected ComplaintsService $complaintsService,
        protected CustomersService $customersService,
        protected DiscountReasonsService $discountReasonsService,
        protected InspectionCategoriesService $inspectionCategoriesService,
        protected InspectionItemsService $inspectionItemsService,
        protected InspectionModelsService $inspectionModelsService,
        protected InspectionsService $inspectionsService,
        protected JobDescriptionsService $jobDescriptionsService,
        protected JobsService $jobsService,
        protected RepairOrderService $repairOrderService,
        protected RequiredFieldsService $requiredFieldsService,
        protected ROStatusesService $roStatusesService,
        protected ROTypesService $roTypesService,
        protected SettingsService $settingsService,
        protected SourcesService $sourcesService,
        protected SubletService $subletService,
        protected SupplierService $supplierService,
        protected TechModeSettingsService $techModeSettingsService,
        protected VehicleLabelsService $vehicleLabelsService
    ) {}

    public function index()
    {
        $salesRep = DB::table('adminlogin')
            ->select(['id', 'first_name', 'last_name'])
            ->where('salesrep', 'yes')
            ->orderBy('first_name')
            ->orderBy('last_name')
            ->get();

        return Inertia::render('shop/Create', [
            'salesRep' => $salesRep,
        ]);
    }

    public function store(StoreNewShopRequest $request)
    {
        $shopid = $this->getLastShopId() + 1;
        $companyPhone = preg_replace('/\D/', '', $request->phone);

        $date = Carbon::now()->format('Y-m-d');
        $expiration = Carbon::now()->addDays(30)->format('m/d/Y');

        $trialExpiration = ($request->matco == 'yes')
            ? Carbon::now()->addDays(7)->format('Y-m-d')
            : Carbon::now()->addDays(30)->format('Y-m-d');

        $inspectExpiration = Carbon::now()->addDays(7)->format('Y-m-d');

        // Company Section
        $companyData = [
            'shopid' => $shopid,
            'companyname' => $request->name,
            'datestarted' => $date,
            'trialexpiration' => $trialExpiration,
            'package' => 'Trial',
            'active' => 'yes',
            'referredby' => $request->referralSource ? $request->referralSource : '',
            'affiliateid' => $request->referralCode ? $request->referralCode : '',
            'new' => 'yes',
            'timezone' => $request->timeZone,
            'status' => 'ACTIVE',
            'contact' => $request->firstName.' '.$request->lastName,
            'companycountry' => strtoupper($request->country),
            'companyaddress' => strtoupper($request->address),
            'companycity' => strtoupper($request->city),
            'companystate' => strtoupper($request->state),
            'companyzip' => $request->zip,
            'companyphone' => $companyPhone,
            'rodisclosure' => $this->roDisclosure,
            'rowarrdisclosure' => $this->roWarrantyDisclosure,
            'companyemail' => $request->email,
            'defaulttaxrate' => $request->partsTaxRate ? $request->partsTaxRate : 0,
            'defaultlabortaxrate' => $request->laborTaxRate ? $request->laborTaxRate : 0,
            'defaultsublettaxrate' => $request->subletTaxRate ? $request->subletTaxRate : 0,
            'hourlyrate' => $request->laborRate ? $request->laborRate : 0,
            'dashexpiration' => $expiration,
            'shopip' => '',
            'inspectexpiration' => $inspectExpiration,
            'matco' => $request->matco ? 'yes' : 'no',
            'protractor' => 'no',
            'salesrep' => $this->getSalesRepById($request->salesRep),
            'newpackagetype' => $request->package,
            'userfee1' => 'SHOP SUPPLIES',
            'userfee1type' => '%',
            'userfee1amount' => '3',
            'userfee1max' => '20',
            'userfee1taxable' => 'Taxable',
            'userfee1applyon' => 'all',
            'nexpartpassword' => 'showcommlogyes',
        ];

        Company::insert($companyData);

        CompanyCategories::insert([
            'shopid' => $shopid,
            'category' => $request->type ? $request->type : '',
        ]);

        // Matco API Login key
        if ($request->matco) {
            $today = time();
            $apistr = 'drott;'.$today;
            $apikey = md5($apistr);

            DB::table('apilogin')->insert([
                'shopid' => $shopid,
                'companyname' => 'matco',
                'apikey' => $apikey,
            ]);

            $from = '<EMAIL>';
        } else {
            $from = '<EMAIL>';
        }

        // Add Employee
        Employees::insert([
            'shopid' => $shopid,
            'employeefirst' => strtoupper($request->firstName),
            'employeelast' => strtoupper($request->lastName),
            'username' => $request->username,
            'employeeid' => '125',
            'defaultwriter' => 'Yes',
            'active' => 'Yes',
            'password' => $request->password,
            'passwordenc' => password_hash($request->password, PASSWORD_DEFAULT),
        ]);

        $this->paymentMethodsService->insertPaymentMethods($shopid);
        $this->settingsService->insertSettings($shopid);
        $this->categoriesService->insertCategories($shopid);
        $this->codeService->inserCodes($shopid);
        $this->jobDescriptionsService->insertJobDescriptions($shopid);
        $this->roTypesService->insertROTypes($shopid);
        $this->roStatusesService->insertROStatuses($shopid);
        $this->sourcesService->insertSources($shopid);

        if ($request->includeTestData) {
            $this->complaintsService->insertComplaints($shopid);
            $this->customersService->insertCustomers($shopid);
            $this->laborService->insertLabor($shopid);
            $this->partsService->insertParts($shopid);
            $this->partsRegistryService->insertPartsRegistry($shopid);
            $this->repairOrderService->insertRepairOrders($shopid);
            $this->subletService->insertSublet($shopid);
        }

        $this->complaintcatsService->insertComplaintcats($shopid);
        $this->partsInventoryService->insertPartsInventory($shopid);
        $this->partsRegistryService->insertPartsRegistryWOTestData($shopid);
        $this->supplierService->insertSupplier($shopid);
        $cannedJobsId = $this->cannedJobsService->insertCannedJobs($shopid);
        $this->cannedPartsService->insertCannedParts($shopid, $cannedJobsId);
        $this->cannedLaborService->insertCannedLabor($shopid, $cannedJobsId);
        $this->jobsService->insertJobs($shopid);
        $this->campaignsService->insertCampaigns($shopid);
        $this->accountTypesService->insertAccountTypes($shopid);
        $this->discountReasonsService->insertDiscountReasons($shopid);
        $this->notificationSettingsService->insertNotificationSettings($shopid);
        $this->chartOfAccountsService->insertChartOfAccounts($shopid);

        $inspectionId = $this->inspectionsService->insertInspections($shopid);
        $categoryId = $this->inspectionCategoriesService->insertInspectionCategories($shopid, 'Interior/Exterior', '1', $inspectionId);

        $this->inspectionItemsService->insertInspectionItems('Interior/Exterior', $shopid, 'Headlights, Tail Lights, Turn Signals', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Interior/Exterior', $shopid, 'Windshield Wipers', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Interior/Exterior', $shopid, 'Horn', $categoryId);

        $categoryId = $this->inspectionCategoriesService->insertInspectionCategories($shopid, 'Underhood', '2', $inspectionId);

        $this->inspectionItemsService->insertInspectionItems('Underhood', $shopid, 'Belts', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Underhood', $shopid, 'Hoses', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Underhood', $shopid, 'Air Filter', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Underhood', $shopid, 'Visible Leaks', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Underhood', $shopid, 'Radiator and Coolant', $categoryId);

        $categoryId = $this->inspectionCategoriesService->insertInspectionCategories($shopid, 'Tires', '3', $inspectionId);

        $this->inspectionItemsService->insertInspectionItems('Tires', $shopid, 'R/F Tread Depth', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Tires', $shopid, 'R/F Tire Pressure', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Tires', $shopid, 'R/F Tire', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Tires', $shopid, 'L/F Tread Depth', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Tires', $shopid, 'L/F Tire Pressure', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Tires', $shopid, 'L/F Tire', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Tires', $shopid, 'R/R Tread Depth', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Tires', $shopid, 'R/R Tire Pressure', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Tires', $shopid, 'R/R Tire', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Tires', $shopid, 'L/R Tread Depth', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Tires', $shopid, 'L/R Tire Pressure', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Tires', $shopid, 'L/R Tire', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Tires', $shopid, 'Spare Tire Pressure', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Tires', $shopid, 'Spare Tire Tread Depth', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Tires', $shopid, 'Spare Tire', $categoryId);

        $categoryId = $this->inspectionCategoriesService->insertInspectionCategories($shopid, 'Steering and Suspension', '4', $inspectionId);

        $this->inspectionItemsService->insertInspectionItems('Steering and Suspension', $shopid, 'CV Boots, Half Shafts, Control Arms', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Steering and Suspension', $shopid, 'Ball Joints, Steering Linkage, Sway bar links', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Steering and Suspension', $shopid, 'Shocks and Struts', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Steering and Suspension', $shopid, 'Alignment', $categoryId);

        $categoryId = $this->inspectionCategoriesService->insertInspectionCategories($shopid, 'Brakes', '5', $inspectionId);

        $this->inspectionItemsService->insertInspectionItems('Brakes', $shopid, 'Front brake pad LF%', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Brakes', $shopid, 'Front brake pad RF%', $categoryId);
        $this->inspectionItemsService->insertInspectionItems('Brakes', $shopid, 'Rear Brake Pads/Shoes LR%', $categoryId);

        $this->priorityService->insertPriority($shopid);
        $this->vehicleLabelsService->insertVehicleLabels($shopid);
        $this->requiredFieldsService->insertRequiredFields($shopid);
        $this->techModeSettingsService->insertTechModeSettings($shopid);
        $this->colorCodingService->insertColorCoding($shopid);
        $this->inspectionModelsService->insert($shopid, false);

        $emailData = [
            'shopname' => $request->name,
            'shopid' => $shopid,
            'first_name' => $request->firstName,
            'last_name' => $request->lastName,
            'address' => $request->address,
            'city' => $request->city,
            'state' => $request->state,
            'zip' => $request->zip,
            'phone' => $request->phone,
            'shopemail' => $request->email,
            'username' => $request->username,
            'password' => $request->password,
            'date' => $date,
            'accountUrl' => 'https://shopbosspro.com/login.php',
        ];

        if ($request->matco) {
            Mail::to('<EMAIL>')->send(new NewAccountNotification($emailData));
            Mail::to('<EMAIL>')->send(new NewAccountNotification($emailData));
            Mail::to($from)->send(new NewAccountNotification($emailData));
        } else {
            Mail::to($from)->send(new NewAccountNotification($emailData));
        }

        Mail::to('<EMAIL>')->send(new NewAccountNotification($emailData));

        // $data = array(
        //     'SHOP_BOSS_LEGACY_CRM_ID__C' => $shopid,
        //     'Shop_Boss_Shop_Id__c' => $shopid,
        //     'Name' => $request->name,
        //     'BILLINGSTREET' => $request->address,
        //     'Billing City' => $request->city,
        //     'BILLINGSTATECode' => $request->state,
        //     'Billing Postal Code' => $request->zip,
        //     'BillingCountry' => '',
        //     'Phone' => $request->phone,
        //     'Email' => $request->email,
        //     'SHOP_BOSS_LEGACY_SYSTEM_CREATED_DATE__C' => Carbon::parse($date)->format('m/d/Y'),
        //     'Contract_Sign_Date__c' => Carbon::parse($date)->format('m/d/Y'),
        //     'Contact' => $request->firstName .' '. $request->lastName
        // );

        // $jsonEncodedData = json_encode($data);
        // $curl = curl_init();
        // $opts = array(
        //     CURLOPT_URL             => 'https://endpoint.scribesoft.com/v1/orgs/47078/requests/22702?accesstoken=03fdbdf1-0c25-4fc8-84fb-472f7a90e4d6',
        //     CURLOPT_RETURNTRANSFER  => true,
        //     CURLOPT_CUSTOMREQUEST   => 'POST',
        //     CURLOPT_POST            => 1,
        //     CURLOPT_POSTFIELDS      => $jsonEncodedData,
        //     CURLOPT_HTTPHEADER  => array('Content-Type: application/json','Content-Length: ' . strlen($jsonEncodedData))
        // );
        // curl_setopt_array($curl, $opts);
        // $result = curl_exec($curl);
        // curl_close($curl);

        // DB::table('errors')
        //     ->insert([
        //         'desc' => 'Salesforce Create Trial',
        //         'shopid' => $shopid,
        //         'fulldesc' => $jsonEncodedData
        //     ]);

        return redirect()->back()->with(['message' => 'Trial shop:'.$shopid.' created successfully and ready to use!']);
    }

    private function getReferralShopid($refCode)
    {
        return DB::table('company')
            ->where('referralcode', $refCode)
            ->value('shopid');
    }

    private function getLastShopId()
    {
        $lastShopId = DB::table('company')
            ->whereRaw("shopid REGEXP '^[0-9]+$'")
            ->whereNotIn('shopid', ['477287', '908239'])
            ->orderByRaw('CAST(shopid AS UNSIGNED) DESC')
            ->limit(1)
            ->value('shopid');

        return $lastShopId;
    }

    public function generateUsername(Request $request)
    {
        $firstName = strtolower(trim(preg_replace('/\s+/', '', $request->input('firstname', ''))));
        $lastName = strtolower(trim(preg_replace('/\s+/', '', $request->input('lastname', ''))));

        if (empty($firstName) && empty($lastName)) {
            return response()->json(['error' => 'First name and last name are required'], 400);
        }

        $baseUsername = $firstName && $lastName ? $firstName.$lastName : ($firstName ?: $lastName);

        if (strlen($baseUsername) < 5) {
            $baseUsername = str_pad($baseUsername, 4, '0').'1';
        }

        $usernameOptions = [
            $firstName.$lastName,
            $lastName.$firstName,
            $firstName.'.'.$lastName,
            $lastName.'.'.$firstName,
        ];

        foreach ($usernameOptions as $username) {
            if (strlen($username) < 5) {
                $username = str_pad($username, 4, '0').'1';
            }
            if ($this->isUsernameAvailable($username)) {
                return response()->json($username);
            }
        }

        $counter = 1;
        while (! $this->isUsernameAvailable($baseUsername.$counter)) {
            $counter++;
        }

        $finalUsername = $baseUsername.$counter;
        if (strlen($finalUsername) < 5) {
            $finalUsername = str_pad($finalUsername, 4, '0').'1';
        }

        return response()->json($finalUsername);
    }

    private function isUsernameAvailable($username)
    {
        return ! DB::table('employees')->where('username', $username)->exists();
    }

    private function getSalesRepById($id)
    {
        $salesRep = DB::table('adminlogin')
            ->where('id', $id)
            ->select('first_name', 'last_name')
            ->first();

        return $salesRep ? $salesRep->first_name.' '.$salesRep->last_name : null;
    }
}
