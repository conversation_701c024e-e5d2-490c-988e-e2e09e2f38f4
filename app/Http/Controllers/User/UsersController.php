<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\UserRequest;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Inertia\Inertia;

class UsersController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        return Inertia::render('users/Index', [
            'users' => User::get(),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        Auth::user()->role == 'superadmin' ?: abort(403, 'Resource is unavailable');

        return Inertia::render('users/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(UserRequest $request)
    {
        Auth::user()->role == 'superadmin' ?: abort(403, 'Resource is unavailable');

        $validated = $request->validated();

        $validated['password'] = Hash::make($validated['password']);

        User::create($validated);

        return redirect()->back()->with('message', 'User Successfully Created!');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        Auth::user()->role == 'superadmin' ?: abort(403, 'Resource is unavailable');

        return Inertia::render('users/Show', [
            'user' => User::find($id),
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UserRequest $request, string $id)
    {
        Auth::user()->role == 'superadmin' ?: abort(403, 'Resource is unavailable');

        $user = User::findOrFail($id);

        $validated = $request->validated();

        if (! is_null($validated['password'])) {
            $validated['password'] = Hash::make($validated['password']);
        } else {
            unset($validated['password']);
        }

        $user->update($validated);

        return redirect()->back()->with('message', 'User Successfully Updated!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        Auth::user()->role == 'superadmin' ?: abort(403, 'Resource is unavailable');

        User::find($id)->delete();

        return redirect()->back()->with('message', 'User Sucesfully Deleted!');
    }
}
