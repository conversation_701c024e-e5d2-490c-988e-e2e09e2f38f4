<?php

namespace App\Http\Controllers\Dashboard;

use App\Models\Company;
use App\Models\RepairOrder;
use Illuminate\Support\Facades\DB;

abstract class Chart
{
    // Get Shops
    protected function getShops($start_date, $end_date, $connection = 'mysql')
    {
        $query = DB::connection($connection)->table('company')
            ->select(
                DB::raw('DATE(ts) as date'),
                DB::raw('COUNT(*) as count')
            )
            ->where(DB::raw('lcase(`status`)'), '=', 'active')
            ->where('readonly', '!=', 'yes')
            ->whereBetween('ts', [$start_date, $end_date])
            ->groupBy(DB::raw('DATE(ts)'))
            ->orderBy(DB::raw('DATE(ts)'), 'asc');

        $connection === 'mysql' && $query->where('demo', '!=', 'yes');

        return $query->get();
    }

    // Get Boss Pay
    protected function getBossPay($start_date, $end_date)
    {
        $data = Company::where(DB::raw('lcase(`status`)'), '=', 'active')
            ->where('readonly', '!=', 'yes')
            ->where('demo', '!=', 'yes')
            ->where('merchantaccount', '360')
            ->whereBetween('ts', [$start_date, $end_date])
            ->count();

        return $data;
    }

    // Get Account Packages
    protected function getAccountPackages($start_date, $end_date)
    {
        $data = Company::select(
            DB::raw("COUNT(CASE WHEN newpackagetype = 'silver' THEN 1 END) as silver"),
            DB::raw("COUNT(CASE WHEN newpackagetype = 'gold' THEN 1 END) as gold"),
            DB::raw("COUNT(CASE WHEN newpackagetype = 'platinum' THEN 1 END) as platinum")
        )
            ->where(DB::raw('lcase(`status`)'), '=', 'active')
            ->where('readonly', '!=', 'yes')
            ->where('demo', '!=', 'yes')
            ->where('package', 'Paid')
            ->whereBetween('ts', [$start_date, $end_date])
            ->first();

        return $data;
    }

    protected function getARO($start_date, $end_date)
    {
        $data = RepairOrder::select(DB::raw('SUM(totalro) / COUNT(roid) as average_totalro'))
            ->whereBetween('StatusDate', [$start_date, $end_date])
            ->first();

        return $data;
    }

    // Get Previous Period Date For Calculations
    protected function getPeriod($start_date, $end_date)
    {
        $periodType = round($start_date->diffInDays($end_date)) == 1 ? 'day' :
            (round($start_date->diffInDays($end_date)) == 7 ? 'week' : 'month');

        if ($periodType === 'day') {
            $prevstart_date = $start_date->copy()->subDay();
            $prevend_date = $end_date->copy()->subDay();
        } elseif ($periodType === 'week') {
            $prevend_date = $start_date->copy()->subDay()->endOfDay();
            $prevstart_date = $prevend_date->copy()->subDays(6)->startOfDay();
        } else {
            $prevstart_date = $start_date->copy()->subMonth()->startOfMonth();
            $prevend_date = $start_date->copy()->subMonth()->endOfMonth();
        }

        return [$prevstart_date, $prevend_date];
    }
}
