<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Shared\DateRangeRequest;
use App\Models\Company;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class PPHInternalController extends Controller
{
    public function index()
    {
        return Inertia::render('report/PPHInternal');
    }

    public function datatable(DateRangeRequest $request)
    {
        if ($request->ajax()) {
            $start_date = $request->input('start_date');
            $end_date = $request->input('end_date');

            $data = Company::join('repairorders as ro', 'company.shopid', '=', 'ro.shopid')
                ->join('companycategories as co', 'company.shopid', '=', 'co.shopid')
                ->select([
                    'company.shopid',
                    'company.companyname',
                    'company.companyphone',
                    'company.companycity',
                    'company.companystate',
                    'company.timezone',
                    'company.companyemail',
                    'company.newpackagetype',
                    DB::raw('ROUND(AVG(ro.pph), 2) as pph'),
                    DB::raw('ROUND(AVG(ro.totalro), 2) as totalro'),
                    DB::raw('GROUP_CONCAT(DISTINCT co.category) as category'),
                ])
                ->where('company.shopid', '!=', '13846')
                ->where('company.profitboost', 'yes')
                ->where('company.status', 'active')
                ->where('company.readonly', '!=', 'yes')
                ->where('ro.rotype', '!=', 'no approval')
                ->where('ro.status', 'closed')
                ->whereBetween('ro.statusdate', [$start_date, $end_date])
                ->groupBy('company.shopid');

            return Datatables::of($data)
                ->editColumn('companyphone', fn ($row) => $this->formatPhoneNumber($row->companyphone))
                ->editColumn('pph', fn ($row) => $this->asDollars($row->pph))
                ->editColumn('totalro', fn ($row) => $this->asDollars($row->totalro))
                ->make(true);
        }
    }
}
