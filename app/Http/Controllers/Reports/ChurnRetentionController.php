<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Report\ChurnDateRangeRequest;
use App\Models\Company;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class ChurnRetentionController extends Controller
{
    public function index()
    {
        return Inertia::render('report/ChurnRetention');
    }

    public function datatable(ChurnDateRangeRequest $request)
    {
        if ($request->ajax()) {
            $start_date = $request['start_date'];
            $end_date = $request['end_date'];
            $not_in_date = $request['not_in_date'];

            $data = Company::join('companycategories as cc', 'company.shopid', '=', 'cc.shopid')
                ->join('repairorders as ro', 'company.shopid', 'ro.shopid')
                ->select([
                    'company.shopid',
                    'company.datestarted',
                    'company.contact',
                    'company.CompanyName',
                    'company.CompanyEmail',
                    'company.CompanyPhone',
                    'company.newpackagetype',
                    'company.flatprice',
                    'company.datesuspended',
                    'company.lastpaymentdate',
                    DB::raw('GROUP_CONCAT(DISTINCT cc.category) as category'),
                    DB::raw('(SELECT datein FROM repairorders WHERE repairorders.shopid = company.shopid ORDER BY datein DESC LIMIT 1) as datein'),
                ])
                ->where('company.package', 'paid')
                ->where('company.shopid', '!=', 'demo');

            if ($not_in_date == 'true') {
                $data->whereNotIn('company.shopid', function ($query) use ($start_date, $end_date) {
                    $query->select(DB::raw('DISTINCT shopid'))
                        ->from('repairorders')
                        ->whereBetween('datein', [$start_date, $end_date]);
                });
            } else {

                $data->where(function ($query) use ($start_date, $end_date) {
                    $query->where(function ($query) use ($start_date, $end_date) {
                        $query->where('company.status', 'suspended')
                            ->whereBetween('company.datesuspended', [$start_date, $end_date]);
                    })
                        ->orWhere(function ($query) use ($start_date, $end_date) {
                            $query->where('company.status', 'active')
                                ->where('company.readonly', 'yes')
                                ->whereBetween('company.lastpaymentdate', [$start_date, $end_date]);
                        });
                });
            }

            $data->groupBy('company.shopid');

            return Datatables::of($data)
                ->filterColumn('datein', function ($query, $keyword) {
                    $query->havingRaw('datein LIKE ?', ["%{$keyword}%"]);
                })
                ->editColumn('CompanyPhone', fn ($row) => $this->formatPhoneNumber($row->CompanyPhone))
                ->editColumn('flatprice', fn ($row) => $this->asDollars($row->flatprice))
                ->make(true);
        }
    }
}
