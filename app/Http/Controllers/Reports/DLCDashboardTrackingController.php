<?php

namespace App\Http\Controllers\Reports;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class DLCDashboardTrackingController extends Controller
{
    public function index()
    {
        $statuses = DB::table('campaignregistry')
            ->distinct()
            ->pluck('status');

        return Inertia::render('report/DLCDashboardTracking', ['statuses' => $statuses]);
    }

    public function datatable(Request $request)
    {
        $start_date = $request->input('start_date');
        $end_date = $request->input('end_date');
        $status = $request->input('status');

        if ($request->ajax()) {
            $data = DB::table('campaignregistry');

            if ($start_date && $end_date) {
                $data->whereBetween('ts', [$start_date, $end_date]);
            }

            if ($status && $status != 'All') {
                $data->where('status', $status);
            }

            if ($status == '') {
                $data->where('status', '');
            }

            return Datatables::of($data)
                ->editColumn('dba', function ($data) {
                    return html_entity_decode($data->dba);
                })
                ->make(true);
        }
    }
}
