<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Shared\DateRangeRequest;
use App\Models\Company;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class ChurnListRetentionController extends Controller
{
    public function index()
    {
        return Inertia::render('report/ChurnListRetention');
    }

    public function datatable(DateRangeRequest $request)
    {
        if ($request->ajax()) {
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            $data = Company::join('companycategories as companyCategory', 'company.shopid', '=', 'companyCategory.shopid')
                ->select([
                    'company.CompanyName',
                    'company.shopid',
                    'company.newpackagetype',
                    'company.salesrep',
                    'company.flatprice',
                    'company.lastpaymentdate',
                    'company.vhc_number',
                    'company.datestarted',
                    'company.dateofacceptance',
                    'company.churndate',
                    'company.whochurned',
                    'company.churnreason',
                    'companyCategory.category',
                ])
                ->where('company.churn', 'yes')
                ->whereBetween('company.churndate', [$startDate, $endDate]);

            return Datatables::of($data)
                ->editColumn('lastpaymentdate', function ($row) {
                    return $row->lastpaymentdate != '0000-00-00'
                        ? ($row->lastpaymentdate)
                        : '';
                })
                ->editColumn('dateofacceptance', function ($row) {
                    return $row->lastpaymentdate != '11/30/-0001'
                        ? ($row->dateofacceptance)
                        : '';
                })
                ->editColumn('flatprice', fn ($row) => $this->asDollars($row->flatprice))
                ->filterColumn('category', function ($query, $keyword) {
                    $query->havingRaw('category LIKE ?', ["%{$keyword}%"]);
                })
                ->make(true);
        }
    }
}
