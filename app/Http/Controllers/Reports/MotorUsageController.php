<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Shared\DateRangeRequest;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class MotorUsageController extends Controller
{
    public function index()
    {
        return Inertia::render('report/MotorUsage');
    }

    public function datatable(DateRangeRequest $request)
    {
        if ($request->ajax()) {
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            $data = DB::table('_company_view as company')
                ->leftJoin('motortrack as mt', function ($join) use ($startDate, $endDate) {
                    $join->on('company.shopid', '=', 'mt.shopid')
                        ->whereBetween('mt.dt', [$startDate, $endDate]);
                })
                ->select([
                    'company.shopid',
                    'company.datestarted',
                    'company.companyname',
                    'company.newpackagetype',
                    'company.status AS Status',
                    'company.lastpaymentdate',
                    'company.churndate',
                    DB::raw('SUM(mt.counter) as total_clicks'),
                    'company.contact',
                    'company.companyemail',
                    'company.companyphone',
                ])
                ->where(function ($query) {
                    $query->where('company.status', '!=', 'suspended')
                        ->orWhere(function ($query) {
                            $query->where('company.status', 'suspended')
                                ->where('company.lastpaymentdate', '>=', '2022-10-01');
                        });
                })
                ->where('company.newpackagetype', '!=', 'silver')
                ->where(function ($query) {
                    $query->whereNull('company.churndate')
                        ->orWhere('company.churndate', '=', '0000-00-00')
                        ->orWhere('company.churndate', '>=', '2022-10-01');
                })
                ->orderBy('total_clicks', 'DESC')
                ->groupBy('company.shopid');

            return DataTables::of($data)
                ->editColumn('companyphone', fn ($row) => $this->formatPhoneNumber($row->companyphone))
                ->editColumn('total_clicks', fn ($row) => $row->total_clicks ? $row->total_clicks : 0)
                ->make(true);
        }
    }
}
