<?php

namespace App\Http\Controllers\Reports;

use App\Models\Company;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class ShopCategoryController extends Controller
{
    public function index()
    {
        return Inertia::render('report/ShopCategory');
    }

    public function datatable(Request $request)
    {
        if ($request->ajax()) {
            $data = Company::join('companycategories as cc', 'company.shopid', '=', 'cc.shopid')
                ->select([
                    'company.shopid',
                    'company.CompanyName',
                    'company.newpackagetype',
                    'company.vhc_number',
                    'cc.category',
                    'cc.level',
                ])
                ->whereRaw('lcase(company.status) = ?', ['active'])
                ->where('company.package', 'paid')
                ->where('company.readonly', '!=', 'yes')
                ->where('company.churn', 'no');

            return Datatables::of($data)
                ->make(true);
        }
    }
}
