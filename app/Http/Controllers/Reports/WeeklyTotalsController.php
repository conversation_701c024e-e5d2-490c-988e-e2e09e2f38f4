<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Shared\DateRangeRequest;
use App\Models\Company;
use App\Models\RepairOrder;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class WeeklyTotalsController extends Controller
{
    public function index()
    {
        return Inertia::render('report/WeeklyTotals');
    }

    public function datatable(DateRangeRequest $request)
    {
        if ($request->ajax()) {
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            $repairOrder = RepairOrder::select(
                DB::raw('count(*) as roCount'),
                DB::raw('round(sum(totalro), 2) as totalRO'),
                'shopid'
            )
                ->where('statusdate', '>=', $startDate)
                ->where('statusdate', '<=', $endDate)
                ->where('status', 'closed')
                ->where('rotype', '!=', 'no approval')
                ->groupBy('shopid');

            $data = Company::select([
                'company.shopid',
                'company.CompanyName',
                'company.CompanyCity',
                'company.CompanyState',
                'repairorder.roCount',
                'repairorder.totalRO',
            ])
                ->leftJoinSub($repairOrder, 'repairorder', function ($join) {
                    $join->on('company.shopid', '=', 'repairorder.shopid');
                })
                ->whereRaw('lower(company.status) IN (?)', ['active'])
                ->where('company.shopid', '!=', 'demo')
                ->where('company.package', 'paid')
                ->where('company.readonly', 'no')
                ->where('company.demo', '!=', 'demo');

            return Datatables::of($data)
                ->editColumn('totalRO', fn ($row) => $this->asDollars($row->totalRO))
                ->make(true);
        }

    }
}
