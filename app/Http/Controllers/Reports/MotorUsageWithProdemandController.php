<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Shared\DateRangeRequest;
use App\Models\Company;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class MotorUsageWithProdemandController extends Controller
{
    public function index()
    {
        return Inertia::render('report/MotorUsageWithProdemand');
    }

    public function datatable(DateRangeRequest $request)
    {
        if ($request->ajax()) {
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            $data = Company::select([
                'company.shopid',
                'company.companyname',
                'company.newpackagetype',
                'company.status',
                'company.datestarted',
                'company.lastpaymentdate',
                'company.churndate',
                'company.contact',
                'company.companyemail',
                'company.companyphone',
                DB::raw('UCASE(cat.category) AS category'),
                DB::raw("IF(a.apikey IS NULL, 'NO', 'YES') AS prodemand"),
                DB::raw("DATE_FORMAT(mt.dt, '%M %Y') AS month"),
                DB::raw('COALESCE(SUM(mt.counter), 0) AS total_clicks'),
            ])
                ->leftJoin('motortrack AS mt', function ($join) use ($startDate, $endDate) {
                    $join->on('company.shopid', '=', 'mt.shopid')
                        ->whereBetween('mt.dt', [$startDate, $endDate]);
                })
                ->leftJoin('companycategories AS cat', 'company.shopid', '=', 'cat.shopid')
                ->leftJoin('apilogin AS a', function ($join) {
                    $join->on('company.shopid', '=', 'a.shopid')
                        ->where('a.companyname', '=', 'prodemand');
                })
                ->whereNotIn('company.status', ['suspended'])
                ->where('company.newpackagetype', '!=', 'silver')
                ->groupBy(
                    'company.shopid',
                    'company.companyname',
                    'company.newpackagetype',
                    'company.status',
                    'company.datestarted',
                    'company.lastpaymentdate',
                    'company.churndate',
                    'company.contact',
                    'company.companyemail',
                    'company.companyphone',
                    'cat.category',
                    'a.apikey',
                    DB::raw("DATE_FORMAT(mt.dt, '%M %Y')")
                )
                ->get();

            return DataTables::of($data)
                ->make(true);
        }
    }
}
