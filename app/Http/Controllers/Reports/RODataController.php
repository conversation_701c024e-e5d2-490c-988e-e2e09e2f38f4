<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Shared\DateRangeRequest;
use App\Models\Company;
use App\Models\RepairOrder;
use DateInterval;
use DatePeriod;
use DateTime;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class RODataController extends Controller
{
    public function index()
    {
        return Inertia::render('report/ROData');
    }

    public function datatable(DateRangeRequest $request)
    {
        if ($request->ajax()) {
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            // Generate list of months within the date range
            $start = new DateTime($startDate);
            $end = new DateTime($endDate);
            $end->modify('first day of next month');
            $interval = new DateInterval('P1M');
            $period = new DatePeriod($start, $interval, $end);

            $monthConditions = [];
            foreach ($period as $dt) {
                $month = $dt->format('n');
                $year = $dt->format('Y');
                $monthYear = $dt->format('Y_m');

                $cntCondition = "SUM(CASE WHEN subquery.month = $month AND subquery.year = $year THEN subquery.cnt ELSE 0 END) as cnt_$monthYear";
                $troCondition = "SUM(CASE WHEN subquery.month = $month AND subquery.year = $year THEN subquery.tro ELSE 0 END) as tro_$monthYear";

                $monthConditions[] = DB::raw($cntCondition);
                $monthConditions[] = DB::raw($troCondition);
            }

            $subQuery = RepairOrder::select(
                DB::raw('count(1) as cnt'),
                DB::raw('round(sum(totalro), 2) as tro'),
                DB::raw('YEAR(statusdate) as year'),
                DB::raw('MONTH(statusdate) as month'),
                'shopid'
            )
                ->whereRaw('statusdate >= ?', [$startDate])
                ->whereRaw('statusdate <= ?', [$endDate])
                ->where('status', 'closed')
                ->where('rotype', '!=', 'no approval')
                ->groupBy(DB::raw('YEAR(statusdate), MONTH(statusdate), shopid'));

            $data = Company::select(array_merge([
                'company.shopid',
                'company.CompanyName',
                'company.newpackagetype',
                'company.status',
                'company.datesuspended',
            ], $monthConditions))
                ->leftJoinSub($subQuery, 'subquery', function ($join) {
                    $join->on('company.shopid', '=', 'subquery.shopid');
                })
                ->whereRaw('lower(company.status) IN (?, ?)', ['active', 'suspended'])
                ->where('company.shopid', '!=', 'demo')
                ->where('company.package', 'paid')
                ->where('company.readonly', 'no')
                ->where('company.demo', '!=', 'demo')
                ->whereBetween('company.datesuspended', [$startDate, $endDate])
                ->groupBy('company.shopid', 'company.CompanyName', 'company.newpackagetype', 'company.status', 'company.datesuspended')
                ->get();

            return Datatables::of($data)
                ->make(true);
        }
    }
}
