<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Shared\DateRangeRequest;
use App\Models\Company;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class PachageUpgradesController extends Controller
{
    public function index()
    {
        return Inertia::render('report/PackageUpgrades');
    }

    public function datatable(DateRangeRequest $request)
    {
        if ($request->ajax()) {
            $start_date = $request->input('start_date');
            $end_date = $request->input('end_date');

            $data = Company::join('companyadds as ca', 'company.shopid', '=', 'ca.shopid')
                ->select([
                    'company.shopid',
                    'company.companyname',
                    'company.companyphone',
                    'company.companyemail',
                    'company.vhc_number',
                    'ca.name',
                    'ca.price',
                    'ca.dateadded',
                ])
                ->where('ca.price', '>', 0)
                ->whereBetween('ca.dateadded', [$start_date, $end_date]);

            return Datatables::of($data)
                ->editColumn('companyphone', fn ($row) => $this->formatPhoneNumber($row->companyphone))
                ->editColumn('price', fn ($row) => $this->asDollars($row->price))
                ->make(true);
        }
    }
}
