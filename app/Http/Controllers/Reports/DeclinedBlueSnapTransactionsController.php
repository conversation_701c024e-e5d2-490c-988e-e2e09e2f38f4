<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Shared\DateRangeRequest;
use App\Models\Company;
use Illuminate\Support\Facades\Http;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class DeclinedBlueSnapTransactionsController extends Controller
{
    public function index()
    {
        return Inertia::render('report/DeclinedBlueSnapTransactions');
    }

    public function datatable(DateRangeRequest $request)
    {
        if ($request->ajax()) {
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            $endpoint = 'https://ws.bluesnap.com/services/2/report/DeclinedTransactions';
            $url = $endpoint."?period=CUSTOM&from_date=$startDate&to_date=$endDate";

            $response = Http::withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
                'Authorization' => 'Basic QVBJXzE2Mjk5ODY3MTMyNTkyMTMwMTYyNDU5OlRvYnkxZG9n',
            ])->get($url);

            $declined_transactions = $response->json()['data'];

            foreach ($declined_transactions as &$declined_transaction) {
                $company = Company::select([
                    'shopid',
                    'CompanyName',
                    'CompanyPhone',
                    'CompanyEMail',
                ])
                    ->where('bluesnapid', $declined_transaction['Shopper ID'])
                    ->first();

                $declined_transaction['Transaction Date'] = $this->formatDate($declined_transaction['Transaction Date']);
                $declined_transaction['Amount (USD)'] = $this->asDollars($declined_transaction['Amount (USD)']);
                $declined_transaction['card_owner'] = $declined_transaction['Shopper First Name'].' '.$declined_transaction['Shopper Last Name'];
                $declined_transaction['card_type'] = $declined_transaction['Bin Card Type'].' '.$declined_transaction['Credit Card Type'];

                if ($company) {
                    $declined_transaction['shopid'] = $company->shopid;
                    $declined_transaction['company_name'] = $company->CompanyName;
                    $declined_transaction['company_phone'] = $this->formatPhoneNumber($company->CompanyPhone);
                    $declined_transaction['company_email'] = $company->CompanyEMail;
                } else {
                    $declined_transaction['shopid'] = '';
                    $declined_transaction['company_name'] = '';
                    $declined_transaction['company_phone'] = '';
                    $declined_transaction['company_email'] = '';
                }
            }
            unset($declined_transaction);

            return Datatables::of($declined_transactions)
                ->make(true);
        }
    }
}
