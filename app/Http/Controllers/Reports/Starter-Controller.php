<?php

namespace App\Http\Controllers\Reports;

use App\Models\Company;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class StarterController extends Controller
{
    public function index()
    {
        return Inertia::render('report/BlueSnap');
    }

    public function datatable(Request $request)
    {
        if ($request->ajax()) {
            $data = Company::select([
                'shopid',
                'bluesnapid',
                'CompanyName',
                'contact',
            ])
                ->whereRaw('lcase(status) = ?', ['active'])
                ->where('shopid', '!=', 'demo')
                ->where('package', 'paid')
                ->where('readonly', 'no');

            return Datatables::of($data)
                ->make(true);
        }
    }
}
