<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Report\ReportAddToShopRequest;
use App\Http\Requests\Report\SharedReportRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class CustomReportsController extends Controller
{
    public function index()
    {
        $sharedDirectories = DB::table('report_directory')->orderBy('ts', 'desc')->get();

        foreach ($sharedDirectories as $sd) {
            $customReports = DB::table('customreports')->where('shared_id', $sd->id)->get();
            $sd->customReports = $customReports;
        }

        return Inertia::render('report/CustomReports', [
            'sharedDirectories' => $sharedDirectories,
            'companies' => $this->getCompanies(),
        ]);
    }

    public function editSharedReport(SharedReportRequest $request)
    {
        DB::table('report_directory')
            ->where('id', $request->id)
            ->update([
                'name' => $request->name,
                'category' => empty($request->category) ? '' : $request->category,
                'columns' => empty($request->columns) ? '' : $request->columns,
                'description' => $request->description,
            ]);

        return redirect()->back()->with('message', 'Shared report updated for the shop.');
    }

    public function deleteSharedReport($id)
    {
        DB::table('report_directory')->where('id', $id)->delete();

        return redirect()->back()->with('message', 'Shared report successfully deleted.');
    }

    public function datatable(Request $request)
    {
        if ($request->ajax()) {
            $data = DB::table('customreports')->select([
                'id',
                'shopid',
                'name',
                'desc',
                'location',
                'ts',
            ]);

            $data = $data->get()->map(function ($item) {
                $item->name = html_entity_decode($item->name);
                $item->desc = html_entity_decode($item->desc);
                $item->location = html_entity_decode($item->location);

                return $item;
            });

            return Datatables::of($data)
                ->make(true);
        }
    }

    public function sharedDatatable(Request $request)
    {
        if ($request->ajax()) {
            $data = DB::table('customreports')->select([
                'id',
                'shopid',
                'name',
                'desc',
                'location',
                'ts',
            ]);

            $data = $data->get()->map(function ($item) {
                $item->name = html_entity_decode($item->name);
                $item->desc = html_entity_decode($item->desc);
                $item->location = html_entity_decode($item->location);

                return $item;
            });

            return Datatables::of($data)
                ->make(true);
        }
    }

    public function addToShop(ReportAddToShopRequest $request)
    {
        $reportDirectory = DB::table('report_directory')->where('id', $request->id)->first(['name', 'description', 'location']);

        $CustomReportExists = DB::table('customreports')->where('shared_id', $request->id)->where('shopid', $request->shopid)->count();

        if ($CustomReportExists != 0) {
            return redirect()->back()->with('message', 'Shop already has this shared report added');
        }

        DB::table('customreports')->insert([
            'shopid' => $request->shopid,
            'name' => $request->name,
            'desc' => $reportDirectory->description,
            'location' => $reportDirectory->location,
            'displayat' => 'reports',
            'shared_id' => $request->id,
        ]);

        return redirect()->back()->with('message', 'Shared report added for the shop.');
    }

    public function editSharedReportForShop(SharedReportRequest $request)
    {
        DB::table('customreports')
            ->where('id', $request->id)
            ->update([
                'name' => $request->name,
                'desc' => $request->description,
            ]);

        return redirect()->back()->with('message', 'Shared report updated for the shop.');
    }

    public function deleteSharedReportForShop($id)
    {
        DB::table('customreports')
            ->where('id', $id)
            ->delete();

        return redirect()->back()->with('message', 'Shared report deleted for the shop.');
    }

    public function addToShared(SharedReportRequest $request)
    {
        $generatedId = DB::table('report_directory')->insertGetId([
            'report_id' => $request->id,
            'name' => $request->name,
            'description' => $request->description,
            'location' => $request->location,
            'category' => $request->category,
            'columns' => $request->columns,
        ]);

        return redirect()->back()->with('data', $generatedId);
    }
}
