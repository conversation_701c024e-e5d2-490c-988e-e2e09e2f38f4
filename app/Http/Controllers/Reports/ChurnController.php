<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\CompanyCommentsRequest;
use App\Http\Requests\Shared\DateRangeRequest;
use App\Models\Company;
use App\Models\CompanyComments;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class ChurnController extends Controller
{
    public function index()
    {
        return Inertia::render('report/Churn');
    }

    public function show($shopid)
    {
        return Inertia::render('churn/Index',
            [
                'comments' => CompanyComments::select(['comment', 'commentdatetime'])->where('shopid', $shopid)->latest('ts')->get(),
                'shopid' => $shopid,
            ]);
    }

    public function store(CompanyCommentsRequest $request)
    {
        $comment = CompanyComments::create([
            ...$request->validated(),
            'commentdatetime' => Carbon::today()->toDateString(),
        ]);

        return response()->json(['data' => $comment], 200);
    }

    public function datatable(DateRangeRequest $request)
    {
        $date = $request->validated();

        if ($request->ajax()) {
            $data = Company::select([
                'shopid',
                'CompanyName',
                'contact',
                'CompanyPhone',
                'CompanyEmail',
                'dateofacceptance',
                'datesuspended',
                DB::raw('(SELECT datein FROM repairorders WHERE repairorders.shopid = company.shopid ORDER BY datein DESC LIMIT 1) as datein'),
                DB::raw('(SELECT count(1) FROM repairorders WHERE repairorders.shopid = company.shopid AND status = "closed" and rotype != "no approval" ORDER BY datein DESC LIMIT 1) as roCount'),
            ])
                ->whereRaw('lcase(status) = ?', ['suspended'])
                ->where('shopid', '!=', 'demo')
                ->where('package', 'paid')
                ->where('readonly', 'no')
                ->whereBetween('datesuspended', $date);

            return Datatables::of($data)
                ->filterColumn('datein', function ($query, $keyword) {
                    $query->havingRaw('datein LIKE ?', ["%{$keyword}%"]);
                })
                ->make(true);
        }
    }
}
