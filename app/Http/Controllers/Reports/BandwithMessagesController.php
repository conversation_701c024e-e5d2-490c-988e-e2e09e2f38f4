<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Shared\DateRangeRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Inertia\Inertia;

class BandwithMessagesController extends Controller
{
    public function index()
    {
        return Inertia::render('report/BandwithMessages');
    }

    public function datatable(DateRangeRequest $request)
    {
        $startDate = $request->input('start_date').'T00:00:00.000Z';
        $endDate = $request->input('end_date').'T23:59:59.000Z';
        $limit = $request->limit;
        $messageStatus = strtoupper($request->messageStatus) != 'ALL' ? strtoupper($request->messageStatus) : null;
        $searchBy = strtoupper($request->search['by']) != 'CHOOSE' ? strtoupper($request->search['by']) : null;
        $searchText = $request->search['text'] ?? '';
        $sort = $request->sort ?? null;

        $searchParameters = [
            'SOURCE TELEPHONE' => 'sourceTn',
            'DESTINATION TELEPHONE' => 'destinationTn',
            'CAMPAIGN ID' => 'campaignId',
        ];

        try {
            $params = [
                'fromDateTime' => $startDate,
                'toDateTime' => $endDate,
                'limit' => $limit,
                'pageToken' => request()->input('pageToken', null),
            ];

            if ($messageStatus !== null) {
                $params['messageStatus'] = $messageStatus;
            }

            if ($searchBy !== null) {
                $params[$searchParameters[$searchBy]] = $searchText;
            }

            if ($sort !== null) {
                $params['sort'] = $sort;
            }

            $response = Http::withBasicAuth('c88edfa0-fc8e-4d1d-92ea-dfd6256f6281', 'ej488O241AT8Eh6')->get('https://messaging.bandwidth.com/api/v2/users/5005858/messages', $params);

            $data = $response->json();

            if (empty($data['messages'])) {
                unset($params['pageToken']);

                $response = Http::withBasicAuth('c88edfa0-fc8e-4d1d-92ea-dfd6256f6281', 'ej488O241AT8Eh6')->get('https://messaging.bandwidth.com/api/v2/users/5005858/messages', $params);

                $data = $response->json();
            }

            if (! empty($data['messages'])) {
                foreach ($data['messages'] as &$message) {
                    $shopid = null;
                    $number = substr($message['sourceTn'], 2);
                    $sourceShop = 'SS';

                    $shopid = DB::table('smsnumbers')
                        ->where('smsnum', $number)
                        ->value('shopid');

                    if (! $shopid) {
                        $shopid = DB::table('sms')
                            ->where('from', $number)
                            ->value('shopid');
                    }

                    if (! $shopid) {
                        $shopid = DB::connection('sbpent')->table('smsnumbers')
                            ->where('smsnum', $number)
                            ->value('shopid');

                        $sourceShop = 'MS';
                    }

                    if (! $shopid) {
                        $shopid = DB::connection('sbpent')->table('sms')
                            ->where('from', $number)
                            ->value('shopid');

                        $sourceShop = 'MS';
                    }

                    $message['shopid'] = $shopid;
                    $message['sourceShop'] = $sourceShop;
                }

                unset($message);
            }

            return response()->json([
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }

    }
}
