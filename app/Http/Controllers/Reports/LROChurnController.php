<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Shared\DateRangeRequest;
use App\Models\Company;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class LROChurnController extends Controller
{
    public function index()
    {
        return Inertia::render('report/LROChurn');
    }

    public function datatable(DateRangeRequest $request)
    {
        $start_date = $request->input('start_date');
        $end_date = $request->input('end_date');

        if ($request->ajax()) {
            $data = Company::join('payments as p', 'company.shopid', '=', 'p.shopid')
                ->join('repairorders as ro', 'company.shopid', '=', 'ro.shopid')
                ->select([
                    'company.shopid',
                    'company.companyname',
                    'company.dateofacceptance',
                    'company.churndate',
                    DB::raw('MAX(p.paymentdate) as recentPaymentDate'),
                    DB::raw('COUNT(DISTINCT(ro.roid)) as roCount'),
                    DB::raw('MAX(ro.datein) as lastRODateIn'),
                ])
                ->where('company.churn', 'yes')
                ->where('ro.rotype', '!=', 'no approval')
                ->whereBetween('company.churndate', [$start_date, $end_date])
                ->groupBy('company.shopid');

            return Datatables::of($data)
                ->make(true);
        }
    }
}
