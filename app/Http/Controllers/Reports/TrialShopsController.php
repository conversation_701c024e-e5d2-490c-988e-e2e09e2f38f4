<?php

namespace App\Http\Controllers\Reports;

use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class TrialShopsController extends Controller
{
    public function index()
    {
        return Inertia::render('report/TrialShops');
    }

    public function datatable(Request $request)
    {
        if ($request->ajax()) {
            $data = Company::select([
                'company.shopid',
                'company.datestarted',
                'company.status',
                'company.companyname',
                'company.contact',
                'company.companyphone',
                'company.companyemail',
                'company.companystate',
                'company.newpackagetype',
                DB::raw('(SELECT datein FROM repairorders WHERE shopid = company.shopid ORDER BY roid DESC limit 1) as lastRODate'),
                DB::raw('(SELECT COUNT(1) FROM repairorders WHERE shopid = company.shopid AND rotype != "No Approval" AND status = "Closed") as roCount'),
                DB::raw('(SELECT SUM(totalRO) FROM repairorders WHERE shopid = company.shopid AND rotype != "No Approval" AND status = "Closed") as totalRO'),
            ])
                ->where('company.package', 'Trial')
                ->where('company.status', 'active')
                ->groupBy('company.shopid');

            return Datatables::of($data)
                ->editColumn('roCount', fn ($row) => $this->asDollars($row->roCount))
                ->editColumn('companyphone', fn ($row) => $this->formatPhoneNumber($row->roCount))
                ->editColumn('totalRO', fn ($row) => $row->totalRO ? $this->asDollars($row->totalRO) : $this->asDollars(0))
                ->make(true);
        }
    }
}
