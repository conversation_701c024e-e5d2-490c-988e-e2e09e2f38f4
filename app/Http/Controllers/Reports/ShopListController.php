<?php

namespace App\Http\Controllers\Reports;

use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class ShopListController extends Controller
{
    public function index()
    {
        return Inertia::render('report/ShopList');
    }

    public function datatable(Request $request)
    {
        if ($request->ajax()) {
            $data = Company::select([
                DB::raw("IF(merchantaccount != 'no', CONCAT(TRIM(CompanyName), ' - ', TRIM(merchantaccount)), TRIM(CompanyName)) AS CompanyName"),
                'merchantaccount',
                'company.shopid',
                'CompanyEmail',
                DB::raw('TRIM(contact) AS contact'),
                'CompanyPhone',
                DB::raw("IF(CompanyCity != '', CONCAT(CompanyCity, ', ', CompanyState), '') AS address"),
                'newpackagetype',
                'datestarted',
                'smsnum',
                'status',
                'datesuspended',
            ])
                ->leftJoin('smsnumbers as s', 's.shopid', '=', 'company.shopid')
                ->whereRaw('lcase(status) '.($request->suspended ? '= ?' : '!= ?'), ['suspended'])
                ->where('company.shopid', '!=', 'demo')
                ->where('package', $request->trials ? 'Trial' : 'Paid');

            if ($request->trials) {
                $data->addSelect(
                    DB::raw('(
                        SELECT a.eventdatetime
                        FROM audit a
                        WHERE a.shopid = company.shopid
                        ORDER BY a.id DESC
                        LIMIT 1
                    ) AS lastLogin'),
                    DB::raw('(
                        SELECT count(*)
                        FROM audit a
                        WHERE a.shopid = company.shopid
                    ) as loginCount')
                );
            }

            if ($request->matco) {
                $data->where('matco', 'yes');
            }

            if ($request->suspended) {
                $data->addSelect(
                    DB::raw('(
                        SELECT count(*)
                        FROM repairorders a
                        WHERE a.shopid = company.shopid
                    ) AS roCount'),
                );
            }

            return Datatables::of($data)
                ->filterColumn('address', function ($query, $keyword) {
                    $sql = "CONCAT(CompanyCity, ', ', CompanyState) LIKE ?";
                    $query->whereRaw($sql, ["%{$keyword}%"]);
                })
                ->filterColumn('contact', function ($query, $keyword) {
                    $sql = 'TRIM(contact) LIKE ?';
                    $query->whereRaw($sql, ["%{$keyword}%"]);
                })
                ->editColumn('CompanyName', fn ($row) => html_entity_decode($row->CompanyName))
                ->editColumn('contact', fn ($row) => $row->contact ? strtoupper($row->contact) : '')
                ->editColumn('CompanyEmail', fn ($row) => strtolower($row->CompanyEmail))
                ->editColumn('address', fn ($row) => strtoupper($row->address))
                ->editColumn('newpackagetype', fn ($row) => ucfirst(strtolower($row->newpackagetype)))
                ->editColumn('CompanyPhone', fn ($row) => $this->formatPhoneNumber($row->CompanyPhone))
                ->editColumn('smsnum', fn ($row) => $this->formatPhoneNumber($row->smsnum))
                ->make(true);
        }
    }
}
