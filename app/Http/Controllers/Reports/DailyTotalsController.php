<?php

namespace App\Http\Controllers\Reports;

use App\Models\RepairOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class DailyTotalsController extends Controller
{
    public function index()
    {
        return Inertia::render('report/DailyTotals');
    }

    public function datatable(Request $request)
    {
        if ($request->ajax()) {
            $weekstart = date('Y-m-d', strtotime('2 sunday ago'));
            $weekend = date('Y-m-d', strtotime('last saturday'));

            $data = RepairOrder::select(
                DB::raw('DATE_FORMAT(StatusDate, "%W") as day_of_week'),
                'StatusDate',
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(totalro) as ammount')
            )
                ->whereBetween('StatusDate', [$weekstart, $weekend])
                ->groupBy('StatusDate');

            return Datatables::of($data)
                ->make(true);
        }
    }
}
