<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Shared\DateRangeRequest;
use App\Models\Company;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class MatcoScanListController extends Controller
{
    public function index()
    {
        return Inertia::render('report/MatcoScanList');
    }

    public function datatable(DateRangeRequest $request)
    {
        if ($request->ajax()) {
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            $data = Company::select([
                'CompanyName',
                'shopid',
                'contact',
                'dateofacceptance',
                DB::raw("CONCAT(CompanyAddress, ' ', CompanyCity, ', ', CompanyState, ' ', CompanyZip) AS address"),
                'CompanyEMail',
                'CompanyPhone',
                DB::raw("IF(newpackagetype = 'none', 'USAGE', newpackagetype) AS newpackagetype"),
                'newpackagetype',
                'datestarted',
                DB::raw("(SELECT count(*) FROM matco_records WHERE shopid = company.shopid AND ts >= '$startDate' AND ts <= '$endDate') AS scancount"),
                DB::raw('(SELECT count(*) FROM matco_records WHERE shopid = company.shopid) AS allscancount'),
                DB::raw("(SELECT DISTINCT COUNT(*) FROM dvi WHERE shopid = company.shopid AND completed_at >= '$startDate' AND completed_at <= '$endDate') AS bicount"),
                DB::raw("(SELECT COUNT(distinct roid) FROM roinspection WHERE shopid = company.shopid AND ts >= '$startDate' AND ts <= '$endDate') AS dvicount"),
            ])
                ->where('status', 'active')
                ->where('package', '!=', 'trial')
                ->where('readonly', '!=', 'yes')
                ->where('shopid', '!=', 'demo')
                ->where('matco', 'yes');

            return Datatables::of($data)
                ->editColumn('lastpaymentdate', function ($row) {
                    return $row->lastpaymentdate != '0000-00-00'
                        ? ($row->lastpaymentdate)
                        : '';
                })
                ->editColumn('dateofacceptance', function ($row) {
                    return $row->lastpaymentdate != '11/30/-0001'
                        ? ($row->dateofacceptance)
                        : '';
                })
                ->editColumn('CompanyPhone', fn ($row) => $this->formatPhoneNumber($row->CompanyPhone))
                ->editColumn('flatprice', fn ($row) => $this->asDollars($row->flatprice))
                ->make(true);
        }
    }
}
