<?php

namespace App\Http\Controllers\Reports;

use App\Models\Employees;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class EmployeesController extends Controller
{
    public function index()
    {
        $completedShops = DB::table('shopboss.employees as e')
            ->join('shopboss.company as c', 'c.shopid', '=', 'e.shopid')
            ->where('e.Active', 'yes')
            ->where('c.shopid', '!=', 'demo')
            ->where('c.package', 'Paid')
            ->whereRaw('LOWER(c.status) != ?', ['suspended'])
            ->groupBy('e.shopid')
            ->havingRaw('COUNT(*) = SUM(CASE WHEN e.username IS NOT NULL AND e.username != "" THEN 1 ELSE 0 END)')
            ->select('e.shopid')
            ->count();

        $notCompletedShops = DB::table('shopboss.employees as e')
            ->join('shopboss.company as c', 'c.shopid', '=', 'e.shopid')
            ->where('e.Active', 'yes')
            ->where('c.shopid', '!=', 'demo')
            ->where('c.package', 'Paid')
            ->whereRaw('LOWER(c.status) != ?', ['suspended'])
            ->whereIn('e.shopid', function ($query) {
                $query->select('shopid')
                    ->from('shopboss.employees')
                    ->where('Active', 'yes')
                    ->groupBy('shopid')
                    ->havingRaw('COUNT(*) = SUM(CASE WHEN (username = "" OR username IS NULL) THEN 1 ELSE 0 END)');
            })
            ->distinct()
            ->count('e.shopid');

        return Inertia::render('report/Employees', [
            'completedShops' => $completedShops,
            'notCompletedShops' => $notCompletedShops,
        ]);
    }

    public function data(Request $request)
    {
        if ($request->expectsJson()) {
            $data = Employees::select([
                'employees.shopid',
                DB::raw('LOWER(CONCAT(employees.EmployeeLast, ", ", employees.EmployeeFirst)) as employee'),
                'employees.username',
                'employees.datehired',
            ])
                ->join('company', function ($join) {
                    $join->on('company.shopid', '=', 'employees.shopid')
                        ->where('company.shopid', '!=', 'demo')
                        ->where('company.package', '=', 'Paid')
                        ->whereRaw('LOWER(company.status) != ?', ['suspended']);
                })
                ->where('employees.Active', 'YES');

            $data = $request->employeesCompleted
                ? $data->whereNotNull('username')->where('username', '!=', '')
                : $data->where(function ($query) {
                    $query->where('username', '')->orWhereNull('username');
                });

            return Datatables::of($data)->make(true);
        }

        return response()->json(['error' => 'Invalid request'], 400);
    }

    public function shops(Request $request)
    {
        if ($request->expectsJson()) {
            if ($request->shopsCompleted) {
                $data = Employees::select([
                    'employees.shopid',
                    DB::raw('LOWER(company.companyname) as companyName'),
                ])
                    ->join('company', 'company.shopid', '=', 'employees.shopid')
                    ->where('employees.Active', 'yes')
                    ->whereNotNull('employees.username')
                    ->where('employees.username', '!=', '')
                    ->where('company.shopid', '!=', 'demo')
                    ->where('company.package', '=', 'Paid')
                    ->whereRaw('LOWER(company.status) != ?', ['suspended'])
                    ->whereIn('employees.shopid', function ($query) {
                        $query->select('shopid')
                            ->from('employees')
                            ->where('Active', 'yes')
                            ->groupBy('shopid')
                            ->havingRaw('SUM(CASE WHEN username IS NULL OR username = "" THEN 1 ELSE 0 END) = 0');
                    })
                    ->groupBy('employees.shopid');
            } else {
                $data = Employees::select([
                    'employees.shopid',
                    DB::raw('LOWER(company.companyname) as companyName'),
                    DB::raw('LOWER(CONCAT(employees.EmployeeLast, ", ", employees.EmployeeFirst)) as employee'),
                    'employees.username',
                    'employees.datehired',
                ])
                    ->join('company', function ($join) {
                        $join->on('company.shopid', '=', 'employees.shopid')
                            ->where('company.shopid', '!=', 'demo')
                            ->where('company.package', '=', 'Paid')
                            ->whereRaw('LOWER(company.status) != ?', ['suspended']);
                    })
                    ->where('employees.Active', 'YES')
                    ->where(function ($query) {
                        $query->where('username', '')->orWhereNull('username');
                    });
            }

            return Datatables::of($data)->make(true);

        }

        return response()->json(['error' => 'Invalid request'], 400);
    }
}
