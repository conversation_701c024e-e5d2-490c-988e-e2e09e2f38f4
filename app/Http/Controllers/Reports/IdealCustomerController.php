<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Report\IdealCustomerDateRangeRequest;
use App\Models\Company;
use App\Models\RepairOrder;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class IdealCustomerController extends Controller
{
    public function index()
    {
        return Inertia::render('report/IdealCustomer');
    }

    public function datatable(IdealCustomerDateRangeRequest $request)
    {
        if ($request->ajax()) {
            $startDate = $request['start_date'];
            $endDate = $request['end_date'];
            $package = $request['package'];

            $shopList = Company::where('shopid', '!=', 'demo')
                ->whereBetween('datestarted', [$startDate, $endDate])
                ->when($package != 'all', function ($query) use ($package) {
                    $query->where('newpackagetype', $package);
                })
                ->pluck('shopid')
                ->toArray();

            $repairOrders = RepairOrder::select(
                DB::raw('COUNT(*) as totalRO'),
                DB::raw('SUM(totalro) as roRevenue'),
                DB::raw('SUM(totalro) / COUNT(*) as ARO'),
                DB::raw("'$package' as selectedPackage")
            )
                ->where('status', 'closed')
                ->where('rotype', '!=', 'no approval')
                ->whereBetween('statusdate', [$startDate, $endDate])
                ->whereIn('shopid', $shopList);

            return Datatables::of($repairOrders)
                ->editColumn('roRevenue', fn ($row) => $this->asDollars($row->roRevenue))
                ->editColumn('ARO', fn ($row) => $this->asDollars($row->ARO))
                ->make(true);
        }
    }
}
