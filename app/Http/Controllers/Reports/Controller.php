<?php

namespace App\Http\Controllers\Reports;

use App\Models\Company;
use DateTime;
use Illuminate\Support\Facades\Cache;

abstract class Controller
{
    protected function asDollars($number)
    {
        if (! is_numeric($number)) {
            return null;
        }

        return '$'.number_format($number, 2, '.', ',');
    }

    protected function formatPhoneNumber($phoneNumber)
    {
        $phoneNumber = preg_replace('/[^0-9]/', '', $phoneNumber);

        if (strlen($phoneNumber) == 10) {
            return '('.substr($phoneNumber, 0, 3).') '.substr($phoneNumber, 3, 3).'-'.substr($phoneNumber, 6);
        } else {
            return $phoneNumber;
        }
    }

    protected function formatDate($dateString)
    {
        $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        $date = new DateTime($dateString);
        $day = $date->format('j');
        $month = $months[$date->format('n') - 1];
        $year = $date->format('Y');

        return "{$month}-{$day}-{$year}";
    }

    protected function getCompanies($latest = false)
    {
        $companies = Cache::remember('companies_list'.($latest ? '_latest' : ''), now()->addHours(1), function () use ($latest) {
            return Company::select('shopid', 'companyName')
                ->distinct()
                ->whereRaw('LOWER(status) != ?', ['suspended'])
                ->where('shopid', '!=', 'demo')
                ->where('package', 'Paid')
                ->orderBy('shopid', 'asc')
                ->when($latest, fn ($query) => $query->orderBy('ts', 'desc'))
                ->get()
                ->mapWithKeys(function ($company) {
                    return [$company->shopid => html_entity_decode($company->companyName)];
                })
                ->toArray();
        });

        return $companies;
    }
}
