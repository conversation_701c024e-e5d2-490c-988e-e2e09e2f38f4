<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Shared\DateRangeRequest;
use App\Models\Company;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class DVICompareController extends Controller
{
    public function index()
    {
        return Inertia::render('report/DVICompare');
    }

    public function datatable(DateRangeRequest $request)
    {
        if ($request->ajax()) {
            $start_date = $request->input('start_date');
            $end_date = $request->input('end_date');

            $data = Company::select([
                'shopid',
                'companystate',
                'companyzip',
                DB::raw("(CASE WHEN companyzip REGEXP '^[0-9]+$' THEN 'USA' ELSE 'CANADA' END) as country"),
                DB::raw("(SELECT coalesce(round(avg(totalro - salestax),2),0) FROM repairorders WHERE shopid=company.shopid AND rotype != 'No Approval' AND statusdate >= '$start_date' AND statusdate <= '$end_date' AND status = 'closed') as totalRO"),
                DB::raw("(CASE WHEN (SELECT COUNT(*) FROM dvi WHERE shopid = company.shopid AND DATE(created_at) >= '$start_date' AND DATE(created_at) <= '$end_date' LIMIT 1) > 0 THEN 'YES' ELSE 'NO' END) as boss_inspect"),
                DB::raw("(CASE WHEN (SELECT COUNT(*) FROM roinspectionheader WHERE shopid = company.shopid AND DATE(ts) >= '$start_date' AND DATE(ts) <= '$end_date' LIMIT 1) > 0 THEN 'YES' ELSE 'NO' END) as dvi_lite"),
            ])
                ->where('status', 'active')
                ->where('package', 'paid')
                ->where('readonly', '!=', 'yes')
                ->where('demo', '!=', 'yes');

            return Datatables::of($data)
                ->editColumn('totalRO', fn ($row) => $this->asDollars($row->totalRO))
                ->make(true);
        }
    }
}
