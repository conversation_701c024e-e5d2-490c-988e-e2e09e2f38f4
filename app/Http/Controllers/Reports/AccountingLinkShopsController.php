<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Shared\DateRangeRequest;
use App\Models\Company;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class AccountingLinkShopsController extends Controller
{
    public function index()
    {
        return Inertia::render('report/AccountingLinkShops');
    }

    public function datatable(DateRangeRequest $request)
    {
        if ($request->ajax()) {
            $start_date = $request['start_date'];
            $end_date = $request['end_date'];

            $data = Company::join('backofficekeys as b', 'company.shopid', '=', 'b.shopid')
                ->select([
                    'company.shopid',
                    'company.CompanyName',
                    'company.newpackagetype',
                    'company.CompanyPhone',
                    'b.user',
                    'b.ts',
                ])
                ->where('company.status', 'active')
                ->whereBetween('b.ts', [$start_date, $end_date]);

            return Datatables::of($data)
                ->editColumn('ts', fn ($row) => $this->formatDate($row->ts))
                ->editColumn('CompanyPhone', fn ($row) => $this->formatPhoneNumber($row->CompanyPhone))
                ->make(true);
        }
    }
}
