<?php

namespace App\Http\Controllers\Reports;

use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class AccountingReportController extends Controller
{
    protected function accoutingQuery($matco = false)
    {
        $query = Company::select([
            'shopid',
            'contact',
            'CompanyName',
            DB::raw("CONCAT(CompanyAddress, ' ', CompanyCity, ', ', CompanyState, ' ', CompanyZip) AS address"),
            'CompanyEMail',
            'CompanyPhone',
            DB::raw("CONCAT(billingaddress, ' ', billingcity, ', ', billingstate, ' ', billingzip) AS billing"),
            DB::raw("IF(merchantaccount != 'no', merchantaccount, NULL) AS merchantaccount"),
            DB::raw("IF(newpackagetype != 'none', newpackagetype, NULL) AS newpackagetype"),
            'flatprice',
        ])
            ->whereRaw('lcase(status) = ?', ['active'])
            ->where('shopid', '!=', 'demo')
            ->where('package', '!=', 'trial')
            ->where('readonly', '!=', 'yes');

        $matco && $query->where('matco', 'yes');

        return $query;
    }

    protected function accountingMSQuery($matco = false)
    {
        $query = DB::connection('sbpent')->table('company')->select([
            'enterpriseid',
            'shopid',
            'contact',
            'CompanyName',
            DB::raw("CONCAT(CompanyAddress, ' ', CompanyCity, ', ', CompanyState, ' ', CompanyZip) AS address"),
            'CompanyEMail',
            'CompanyPhone',
            DB::raw("CONCAT(billingaddress, ' ', billingcity, ', ', billingstate, ' ', billingzip) AS billing"),
            DB::raw("IF(merchantaccount != 'no', merchantaccount, NULL) AS merchantaccount"),
            'flatprice',
        ])
            ->whereRaw('lcase(status) = ?', ['active'])
            ->where('shopid', '!=', 'demo')
            ->where('package', '!=', 'trial')
            ->where('readonly', '!=', 'yes')
            ->orderByDesc('enterpriseid');

        $matco && $query->where('matco', 'yes');

        return $query;
    }

    public function index()
    {
        return Inertia::render('report/Accounting');
    }

    public function datatable(Request $request)
    {
        if ($request->ajax()) {
            $data = $this->accoutingQuery();

            return Datatables::of($data)
                ->filterColumn('address', function ($query, $keyword) {
                    $sql = "CONCAT(companyaddress, ' ', companycity, ', ', companystate, ' ', companyzip) LIKE ?";
                    $query->whereRaw($sql, ["%{$keyword}%"]);
                })
                ->filterColumn('billing', function ($query, $keyword) {
                    $sql = "CONCAT(billingaddress, ' ', billingcity, ', ', billingstate, ' ', billingzip) LIKE ?";
                    $query->whereRaw($sql, ["%{$keyword}%"]);
                })
                ->editColumn('billing', fn ($row) => $row->billing === ' ,  ' ? '' : $row->billing)
                ->editColumn('flatprice', fn ($row) => $this->asDollars($row->flatprice))
                ->editColumn('CompanyPhone', fn ($row) => $this->formatPhoneNumber($row->CompanyPhone))
                ->make(true);
        }
    }

    public function indexMS()
    {
        return Inertia::render('report/AccountingMS');
    }

    public function datatableMS(Request $request)
    {
        if ($request->ajax()) {
            $data = $this->accountingMSQuery();

            return Datatables::of($data)
                ->filterColumn('address', function ($query, $keyword) {
                    $sql = "CONCAT(companyaddress, ' ', companycity, ', ', companystate, ' ', companyzip) LIKE ?";
                    $query->whereRaw($sql, ["%{$keyword}%"]);
                })
                ->filterColumn('billing', function ($query, $keyword) {
                    $sql = "CONCAT(billingaddress, ' ', billingcity, ', ', billingstate, ' ', billingzip) LIKE ?";
                    $query->whereRaw($sql, ["%{$keyword}%"]);
                })
                ->editColumn('billing', fn ($row) => $row->billing === ' ,  ' ? '' : $row->billing)
                ->editColumn('flatprice', fn ($row) => $this->asDollars($row->flatprice))
                ->editColumn('CompanyPhone', fn ($row) => $this->formatPhoneNumber($row->CompanyPhone))
                ->make(true);
        }
    }

    public function accountingMatco()
    {
        return Inertia::render('report/AccountingMatco');
    }

    public function accountingMatcoDatatable(Request $request)
    {
        if ($request->ajax()) {
            $data = $this->accoutingQuery(true);

            return Datatables::of($data)
                ->filterColumn('address', function ($query, $keyword) {
                    $sql = "CONCAT(companyaddress, ' ', companycity, ', ', companystate, ' ', companyzip) LIKE ?";
                    $query->whereRaw($sql, ["%{$keyword}%"]);
                })
                ->filterColumn('billing', function ($query, $keyword) {
                    $sql = "CONCAT(billingaddress, ' ', billingcity, ', ', billingstate, ' ', billingzip) LIKE ?";
                    $query->whereRaw($sql, ["%{$keyword}%"]);
                })
                ->editColumn('billing', fn ($row) => $row->billing === ' ,  ' ? '' : $row->billing)
                ->editColumn('flatprice', fn ($row) => $this->asDollars($row->flatprice))
                ->editColumn('CompanyPhone', fn ($row) => $this->formatPhoneNumber($row->CompanyPhone))
                ->make(true);
        }
    }

    public function accountingMatcoMS()
    {
        return Inertia::render('report/AccountingMatcoMS');
    }

    public function accountingMatcoMSDatatable(Request $request)
    {
        if ($request->ajax()) {
            $data = $this->accountingMSQuery(true);

            return Datatables::of($data)
                ->filterColumn('address', function ($query, $keyword) {
                    $sql = "CONCAT(companyaddress, ' ', companycity, ', ', companystate, ' ', companyzip) LIKE ?";
                    $query->whereRaw($sql, ["%{$keyword}%"]);
                })
                ->filterColumn('billing', function ($query, $keyword) {
                    $sql = "CONCAT(billingaddress, ' ', billingcity, ', ', billingstate, ' ', billingzip) LIKE ?";
                    $query->whereRaw($sql, ["%{$keyword}%"]);
                })
                ->editColumn('billing', fn ($row) => $row->billing === ' ,  ' ? '' : $row->billing)
                ->editColumn('flatprice', fn ($row) => $this->asDollars($row->flatprice))
                ->editColumn('CompanyPhone', fn ($row) => $this->formatPhoneNumber($row->CompanyPhone))
                ->make(true);
        }
    }
}
