<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Shared\DateRangeRequest;
use App\Models\CustomReports;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class CustomReportListController extends Controller
{
    public function index()
    {
        return Inertia::render('report/CustomReportList');
    }

    public function datatable(DateRangeRequest $request)
    {
        if ($request->ajax()) {
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            $data = CustomReports::select([
                DB::raw('(SELECT companyname FROM company WHERE company.shopid = customreports.shopid LIMIT 1) as companyName'),
                'shopid',
                'name',
                'ts',
            ])
                ->whereBetween('ts', [$startDate, $endDate]);

            return Datatables::of($data)
                ->make(true);
        }
    }
}
