<?php

namespace App\Http\Controllers\Reports;

use App\Models\ShopsReadOnly;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class ReadOnlyShopsController extends Controller
{
    public function index()
    {
        return Inertia::render('report/ReadOnlyShops');
    }

    public function datatable(Request $request)
    {
        if ($request->ajax()) {
            $data = ShopsReadOnly::select([
                '_shops_read_only.shopid',
                '_shops_read_only.companyname',
                '_shops_read_only.companyaddress',
                DB::raw("CONCAT(_shops_read_only.companycity, ' ', _shops_read_only.companystate, ' ', _shops_read_only.companyzip) AS companyCSZ"),
                '_shops_read_only.companyphone',
                '_shops_read_only.companyemail',
                '_shops_read_only.contact',
                '_shops_read_only.merchantaccount',
                '_shops_read_only.newpackagetype',
                '_shops_read_only.package',
                '_shops_read_only.status',
                '_shops_read_only.matco',
                '_shops_read_only.readonly',
                '_shops_read_only.demo',
                '_shops_read_only.churn',
                '_shops_read_only.churn_date',
                '_shops_read_only.lastpaymentdate',
                '_shops_read_only.dateofacceptance',
                '_shops_read_only.datestarted',
                '_shops_read_only.last_login_date',
                '_shops_read_only.user_account_that_logged_in_last',
                'company.vhc_number',
            ])->join('company', 'company.shopid', '=', '_shops_read_only.shopid');

            return Datatables::of($data)
                ->editColumn('lastpaymentdate', function ($row) {
                    return $row->lastpaymentdate != '0000-00-00'
                        ? $this->formatDate($row->lastpaymentdate)
                        : '';
                })
                ->editColumn('dateofacceptance', function ($row) {
                    return $row->dateofacceptance != '0000-00-00'
                        ? $this->formatDate($row->dateofacceptance)
                        : '';
                })
                ->editColumn('datestarted', function ($row) {
                    return $row->datestarted != '0000-00-00'
                        ? $this->formatDate($row->datestarted)
                        : '';
                })
                ->editColumn('last_login_date', function ($row) {
                    return $row->last_login_date != '12/31/1969'
                        ? $this->formatDate($row->last_login_date)
                        : '';
                })
                ->editColumn('churn_date', fn ($row) => $this->formatDate($row->churn_date))
                ->editColumn('companyphone', fn ($row) => $this->formatPhoneNumber($row->companyphone))
                ->make(true);
        }
    }
}
