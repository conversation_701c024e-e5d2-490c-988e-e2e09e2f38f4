<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Shared\DateRangeRequest;
use App\Models\Company;
use App\Models\RepairOrder;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class DVIReportForSSShopUsersController extends Controller
{
    public function index()
    {
        return Inertia::render('report/DVIReportForSSShopUsers');
    }

    public function datatable(DateRangeRequest $request)
    {
        if ($request->ajax()) {
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            $repairOrder = RepairOrder::select(
                DB::raw('count(*) as roCount'),
                DB::raw('round(sum(totalro), 2) as totalRO'),
                'repairorders.shopid',
                DB::raw('SUM(accountpayments.amt) as accountPaymentsAmount')
            )
                ->leftJoin('accountpayments', function ($join) use ($startDate, $endDate) {
                    $join->on('repairorders.shopid', '=', 'accountpayments.shopid')
                        ->on('repairorders.roid', '=', 'accountpayments.roid')
                        ->where('accountpayments.ptype', '!=', 'cash')
                        ->where('accountpayments.ptype', '!=', 'check')
                        ->where('repairorders.statusdate', '>=', $startDate)
                        ->where('repairorders.statusdate', '<=', $endDate);
                })
                ->where('repairorders.statusdate', '>=', $startDate)
                ->where('repairorders.statusdate', '<=', $endDate)
                ->where('repairorders.status', 'closed')
                ->where('repairorders.rotype', '!=', 'no approval')
                ->groupBy('repairorders.shopid');

            $data = Company::select([
                'company.shopid',
                'company.CompanyName',
                'company.contact',
                'company.CompanyPhone',
                'company.CompanyEMail',
                DB::raw('IFNULL(repairorder.roCount, 0) as roCount'),
                DB::raw('IFNULL(repairorder.totalRO, 0) as totalRO'),
                DB::raw('IFNULL(repairorder.accountPaymentsAmount, 0) as accountPaymentsAmmount'),
            ])
                ->leftJoinSub($repairOrder, 'repairorder', function ($join) {
                    $join->on('company.shopid', '=', 'repairorder.shopid');
                })
                ->where('company.status', 'active')
                ->where('company.package', 'paid')
                ->where('company.readonly', 'no')
                ->where('company.shopid', '!=', 'demo')
                ->where('company.merchantaccount', '!=', '360')
                ->where('company.merchantaccount', '!=', 'authorize.net')
                ->where('company.merchantaccount', '!=', 'cardknox')
                ->where('company.companyzip', 'REGEXP', '^[0-9]+$');

            return Datatables::of($data)
                ->addColumn('LRO', function ($row) {
                    return $this->formatDate(RepairOrder::where('shopid', $row->shopid)->orderBy('roid', 'desc')->limit(1)->value('statusdate'));
                })
                ->editColumn('totalRO', fn ($row) => $this->asDollars($row->totalRO))
                ->editColumn('accountPaymentsAmmount', fn ($row) => $this->asDollars($row->accountPaymentsAmmount))
                ->editColumn('CompanyPhone', fn ($row) => $this->formatPhoneNumber($row->CompanyPhone))
                ->make(true);
        }
    }
}
