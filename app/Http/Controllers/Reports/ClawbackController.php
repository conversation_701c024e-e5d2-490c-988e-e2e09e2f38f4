<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\CompanyCommentsRequest;
use App\Http\Requests\Report\ClawbackDateRangeRequest;
use App\Models\Company;
use App\Models\CompanyComments;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class ClawbackController extends Controller
{
    public function index()
    {
        return Inertia::render('report/Clawback');
    }

    public function show($shopid)
    {
        return Inertia::render('clawback/Index',
            [
                'comments' => CompanyComments::select(['comment', 'commentdatetime'])->where('shopid', $shopid)->latest('ts')->get(),
                'shopid' => $shopid,
            ]);
    }

    public function store(CompanyCommentsRequest $request)
    {
        $comment = CompanyComments::create([
            ...$request->validated(),
            'commentdatetime' => Carbon::today()->toDateString(),
        ]);

        return response()->json(['data' => $comment], 200);
    }

    public function datatable(ClawbackDateRangeRequest $request)
    {
        if ($request->ajax()) {
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');
            $suspended = $request['suspended'];

            $data = Company::select([
                'shopid',
                'companyname',
                'newpackagetype',
                'lastpaymentdate',
                'status',
                'salesrep',
                'dateofacceptance',
                DB::raw('(SELECT paymentdate FROM payments WHERE shopid = company.shopid ORDER BY paymentdate ASC LIMIT 1) as firstPayDate'),
                DB::raw('(SELECT paymentdate FROM payments WHERE shopid = company.shopid ORDER BY paymentdate desc LIMIT 1) as lastPayDate'),
                DB::raw('(SELECT amount FROM payments WHERE shopid = company.shopid ORDER BY paymentdate DESC LIMIT 1) as lastPayAmount'),
                DB::raw('(SELECT COUNT(*) FROM payments WHERE shopid = company.shopid AND paymentdate >= "2022-01-01") as totalPayments'),
                DB::raw('(SELECT datein FROM repairorders WHERE shopid = company.shopid order by datein DESC LIMIT 1) as lastRODate'),
            ])
                ->where('package', '!=', 'trial')
                ->where('status', $suspended == 'true' ? 'suspended' : 'active')
                ->where('active', 'yes')
                ->where('lastpaymentdate', '!=', '0000-00-00')
                ->whereBetween('dateofacceptance', [$startDate, $endDate])
                ->where('readonly', 'no')
                ->where('demo', '!=', 'yes')
                ->orderByRaw('CAST(shopid AS unsigned) asc');

            return Datatables::of($data)
                ->editColumn('lastPayAmount', fn ($row) => $this->asDollars($row->lastPayAmount))
                ->filterColumn('firstPayDate', function ($query, $keyword) {
                    $query->havingRaw('firstPayDate LIKE ?', ["%{$keyword}%"]);
                })
                ->filterColumn('lastPayDate', function ($query, $keyword) {
                    $query->havingRaw('lastPayDate LIKE ?', ["%{$keyword}%"]);
                })
                ->filterColumn('lastPayAmount', function ($query, $keyword) {
                    $query->havingRaw('lastPayAmount LIKE ?', ["%{$keyword}%"]);
                })
                ->filterColumn('lastRODate', function ($query, $keyword) {
                    $query->havingRaw('lastRODate LIKE ?', ["%{$keyword}%"]);
                })
                ->filterColumn('totalPayments', function ($query, $keyword) {
                    $query->havingRaw('totalPayments LIKE ?', ["%{$keyword}%"]);
                })
                ->make(true);
        }
    }
}
