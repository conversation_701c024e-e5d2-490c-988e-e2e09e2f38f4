<?php

namespace App\Http\Controllers\Reports;

use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class BlueSnapController extends Controller
{
    public function index()
    {
        return Inertia::render('report/BlueSnap');
    }

    public function datatable(Request $request)
    {
        if ($request->ajax()) {
            $data = Company::select([
                'shopid',
                'bluesnapid',
                'CompanyName',
                'contact',
            ])
                ->whereRaw('lcase(status) = ?', ['active'])
                ->where('shopid', '!=', 'demo')
                ->where('package', 'paid')
                ->where('readonly', 'no');

            return Datatables::of($data)
                ->make(true);
        }
    }

    public function indexMS()
    {
        return Inertia::render('report/BlueSnapMS');
    }

    public function datatableMS(Request $request)
    {
        if ($request->ajax()) {
            $data = DB::connection('sbpent')->table('company')->select([
                'enterpriseid',
                'shopid',
                'bluesnapid',
                'CompanyName',
                'contact',
            ])
                ->whereRaw('lcase(status) = ?', ['active'])
                ->where('shopid', '!=', 'demo')
                ->where('package', 'paid')
                ->where('readonly', 'no')
                ->orderByDesc('enterpriseid');

            return Datatables::of($data)
                ->make(true);
        }
    }
}
