<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Shared\DateRangeRequest;
use App\Models\Lyftfunds;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class LyftController extends Controller
{
    public function index()
    {
        return Inertia::render('report/Lyft');
    }

    public function datatable(DateRangeRequest $request)
    {
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        if ($request->ajax()) {
            $data = Lyftfunds::select([
                'shopid',
                'paymentamt',
                'rideamt',
                'transdate',
                'balance',
                'rideid',
            ])
                ->where('shopid', '!=', '6062')
                ->whereBetween('transdate', [$startDate, $endDate]);

            return Datatables::of($data)
                ->editColumn('paymentamt', fn ($row) => $this->asDollars($row->paymentamt))
                ->make(true);
        }
    }
}
