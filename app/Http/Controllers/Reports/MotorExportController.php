<?php

namespace App\Http\Controllers\Reports;

use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class MotorExportController extends Controller
{
    public function index()
    {
        return Inertia::render('report/MotorExport');
    }

    public function datatable(Request $request)
    {
        if ($request->ajax()) {
            $data = Company::select([
                'shopid',
                'CompanyName',
                'CompanyCity',
                'CompanyState',
                'newpackagetype',
                DB::raw('"USD" as basecurrency'),
                'bluesnapid',
            ])
                ->whereIn('newpackagetype', ['GOLD', 'PLATINUM', 'PREMIER'])
                ->where('status', 'active');

            return Datatables::of($data)
                ->make(true);
        }
    }
}
