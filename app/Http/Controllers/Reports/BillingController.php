<?php

namespace App\Http\Controllers\Reports;

use App\Models\Company;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class BillingController extends Controller
{
    public function index()
    {
        return Inertia::render('report/Billing');
    }

    public function datatable(Request $request)
    {
        if ($request->ajax()) {
            $data = Company::select([
                'shopid',
                'companyname',
                'howpaying',
                'dateofacceptance',
                'datestarted',
                'bankaccount',
                'routing',
                'flatprice',
            ])
                ->whereRaw('lcase(status) = ?', ['active'])
                ->where('shopid', '!=', 'demo')
                ->where('package', 'paid')
                ->where('readonly', 'no');

            return Datatables::of($data)
                ->make(true);
        }
    }
}
