<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Shared\DateRangeRequest;
use App\Models\Company;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class SuspendedShopsListController extends Controller
{
    public function index()
    {
        return Inertia::render('report/SuspendedShopsList');
    }

    public function datatable(DateRangeRequest $request)
    {
        if ($request->ajax()) {
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            $data = Company::select([
                'shopid',
                'companyname',
                'newpackagetype',
                'flatprice',
                'lastpaymentdate',
                'datestarted',
                'dateofacceptance',
                'datesuspended',
            ])
                ->where('status', 'suspended')
                ->whereBetween('datesuspended', [$startDate, $endDate]);

            return Datatables::of($data)
                ->editColumn('flatprice', fn ($row) => $this->asDollars($row->flatprice))
                ->make(true);
        }
    }
}
