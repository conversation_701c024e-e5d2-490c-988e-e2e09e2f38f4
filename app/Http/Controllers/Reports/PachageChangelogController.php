<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Shared\DateRangeRequest;
use App\Models\PackageChangeLog;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class PachageChangelogController extends Controller
{
    public function index()
    {
        return Inertia::render('report/PackageChangelog');
    }

    public function datatable(DateRangeRequest $request)
    {
        if ($request->ajax()) {
            $start_date = $request->input('start_date');
            $end_date = $request->input('end_date');

            $data = PackageChangeLog::select([
                'package_change_log.shopid',
                'package_change_log.oldpackage',
                'package_change_log.currentpackage',
                'package_change_log.sbemp',
                'package_change_log.ts',
                'company.vhc_number',
            ])
                ->join('company', 'company.shopid', '=', 'package_change_log.shopid')
                ->whereBetween('package_change_log.ts', [$start_date, $end_date]);

            return Datatables::of($data)
                ->make(true);
        }
    }
}
