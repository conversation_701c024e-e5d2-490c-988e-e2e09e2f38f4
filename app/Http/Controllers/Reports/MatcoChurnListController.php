<?php

namespace App\Http\Controllers\Reports;

use App\Http\Requests\Shared\DateRangeRequest;
use App\Models\Company;
use Inertia\Inertia;
use Yajra\DataTables\Facades\DataTables;

class MatcoChurnListController extends Controller
{
    public function index()
    {
        return Inertia::render('report/MatcoChurnList');
    }

    public function datatable(DateRangeRequest $request)
    {
        if ($request->ajax()) {
            $startDate = $request->input('start_date');
            $endDate = $request->input('end_date');

            $data = Company::select([
                'CompanyName',
                'shopid',
                'newpackagetype',
                'salesrep',
                'flatprice',
                'lastpaymentdate',
                'datestarted',
                'dateofacceptance',
                'churndate',
                'whochurned',
                'churnreason',
            ])
                ->where('churn', 'yes')
                ->where('matco', 'yes')
                ->whereBetween('churndate', [$startDate, $endDate]);

            return Datatables::of($data)
                ->editColumn('flatprice', fn ($row) => $this->asDollars($row->flatprice))
                ->make(true);
        }
    }
}
