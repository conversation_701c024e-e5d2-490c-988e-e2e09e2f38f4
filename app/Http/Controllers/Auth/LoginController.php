<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Auth\Authenticate;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Str;
use Inertia\Inertia;

class LoginController extends Authenticate
{
    // Login Page
    public function login()
    {
        return Auth::check()
                ? redirect($this->homePage())
                : Inertia::render('auth/Login', [
                    'status' => session('status'),
                ]);
    }

    // Login Handler
    public function store(LoginRequest $request)
    {

        $request->authenticate();
        $request->session()->regenerate();

        return redirect($this->homePage())->with('reload', true);
    }

    // Logout Handler
    public function destroy(Request $request)
    {
        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect()->route('login');
    }

    // Password Reset
    public function resetPassword()
    {
        return Auth::check()
                ? redirect($this->homePage())
                : Inertia::render('auth/ForgotPassword');
    }

    // Send Reset Email
    public function sendEmail(Request $request)
    {
        $request->validate(['email' => 'required|email']);

        $status = Password::sendResetLink(
            $request->only('email')
        );

        return $status === Password::RESET_LINK_SENT
                    ? back()->with(['status' => __($status)])
                    : back()->withError(['email' => __($status)]);
    }

    // Reset Password Form
    public function resetForm(Request $request)
    {
        return Inertia::render('Auth/ResetPassword', [
            'email' => $request->email,
            'token' => $request->route('token'),
        ]);
    }

    // Reset Password Handler
    public function resetHandler(Request $request)
    {
        $request->validate([
            'token' => 'required',
            'email' => 'required|email',
            'password' => 'required|min:3|confirmed',
        ]);

        $status = Password::reset(
            $request->only('email', 'password', 'password_confirmation', 'token'),
            function (User $user, string $password) {
                $user->forceFill([
                    'password' => Hash::make($password),
                ])->setRememberToken(Str::random(60));

                $user->save();
            }
        );

        return $status === Password::PASSWORD_RESET
            ? redirect()->route('login')->with('status', __($status))
            : back()->withErrors(['email' => [__($status)]]);
    }
}
