<?php

namespace App\Http\Controllers\Auth;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

abstract class Authenticate{
    private $defaultPageFor = [
        'superadmin' => 'dashboard.index',
        'admin' => 'dashboard.index',
        'matco' => 'report.shoplist.matco.index',
        'customer success' => 'report.shoplist.index',
        'customer support' => 'report.shoplist.index',
        'sales' => 'report.shoplist.index',
    ];

    protected function homePage()
    {
        $routeName = $this->defaultPageFor[Auth::user()->role] ?? 'dashboard.index';
        return route($routeName);
    }
}
