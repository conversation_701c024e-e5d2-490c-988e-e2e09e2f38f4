<?php

namespace App\Helpers;

class BaseFunctions
{
    public static function asDollars($number)
    {
        if (! is_numeric($number)) {
            return null;
        }

        return '$'.number_format($number, 2, '.', ',');
    }

    public static function formatPhoneNumber($phoneNumber)
    {
        $phoneNumber = preg_replace('/[^0-9]/', '', $phoneNumber);

        if (strlen($phoneNumber) == 10) {
            return '('.substr($phoneNumber, 0, 3).') '.substr($phoneNumber, 3, 3).'-'.substr($phoneNumber, 6);
        } else {
            return $phoneNumber;
        }
    }

    public static function formatDate($dateString)
    {
        $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        $date = new \DateTime($dateString);
        $day = $date->format('j');
        $month = $months[$date->format('n') - 1];
        $year = $date->format('Y');

        return "{$month}-{$day}-{$year}";
    }
}
